<template>
  <view class="feed-back-list">
    <!-- 分类S -->
    <view class="tabs flex justify-between items-center mb-4">
      <view
        :data-select="!tabIndex"
        class="tab-item flex-1 text-center mr-4 text-base"
        @click="onTabChange(0)">
        待处理
      </view>
      <view
        :data-select="!!tabIndex"
        class="tab-item flex-1 text-center ml-4 text-base"
        @click="onTabChange(1)">
        已回复
      </view>
    </view>
    <!-- 分类E -->

    <LoadMoreList ref="loadMoreList" :request-fn="getFeedbackList" :request-params="{ filter }">
      <template #default="{ list }">
        <FeedbackItem
          v-for="(item, index) in (list as FeedBackItemType[])"
          :key="index"
          :data="item" />
      </template>
    </LoadMoreList>
  </view>
  <!-- 提意见S -->
  <view class="fixed-bottom" @click="goFeedBack">
    <view class="btn flex items-center justify-center">
      <u-icon name="edit-pen" size="30" color="#fff"></u-icon>
      <view class="btn-words">我要提意见</view>
    </view>
  </view>
  <!-- 提意见E -->
</template>

<script setup lang="ts">
import FeedbackItem from './item.vue'
import LoadMoreList from '@/components/LoadMoreList.vue'
import constants from '@/config/constants'
import { getFeedbackList } from '@/api/project/feedback/index'
import type { FeedBackItemType } from '@/api/project/feedback/type'

const tabIndex = ref(0)
const filter = ref([
  {
    enCode: 'f_stauts',
    method: 'ne',
    type: 'custom',
    value: '已回复',
  },
])

const loadMoreList = ref<InstanceType<typeof LoadMoreList>>()
onReachBottom(() => {
  loadMoreList.value!.onReachBottom()
})
onMounted(() => {})
/**
 * 切换标签
 * @param index 下标
 */
function onTabChange(index: number) {
  // 如果下标相同，不做处理
  if (tabIndex.value === index) return
  tabIndex.value = index
  filter.value[0].method = index === 0 ? 'ne' : 'eq'
  loadMoreList.value!.refresh()
}

function goFeedBack() {
  uni.navigateTo({
    url: '/subPages/feedback/edit',
  })
}
</script>

<style lang="scss" scoped>
.feed-back-list {
  --active-color: #1989fa;
  --active-bg-color: #1989fa;
  --unActive-color: #999;
  --bg-color: #fff;
  padding: 32rpx;
  background: #f5f7fb;
  box-sizing: border-box;
  height: 100vh;
  .tabs {
    padding: 0 20rpx;

    .tab-item {
      background: var(--bg-color);
      border-radius: 8rpx;
      font-size: 36rpx;
      padding: 10rpx 20rpx;
      font-weight: 400;
      color: var(--unActive-color);
      &[data-select='true'] {
        background: var(--active-bg-color);
        color: #fff;
      }
    }
  }
}

.fixed-bottom {
  margin: auto;
  position: fixed;
  bottom: 20rpx;
  width: 100%;
  text-align: center;
  display: flex;
  justify-content: center;
  padding-bottom: 0;
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);

  .btn {
    text-align: center;
    color: #ffffff;
    background: #6377f5;
    padding: 10rpx 30rpx;
    border-radius: 50rpx;
    box-sizing: border-box;

    .btn-words {
      margin-left: 15rpx;
      font-size: 36rpx;
    }
  }
}
</style>
