<template>
  <view
    v-if="internalShow"
    class="common-modal-overlay"
    :style="{
      opacity: animationShow ? 1 : 0,
      transition: 'opacity 0.3s ease-in-out',
    }"
    @click="onOverlayClick">
    <view
      class="common-modal-container"
      :style="{
        opacity: animationShow ? 1 : 0,
        transform: `scale(${animationShow ? 1 : 0.8})`,
        transition: 'opacity 0.3s ease-in-out, transform 0.3s ease-in-out',
      }"
      @click.stop>
      <!-- 关闭按钮 -->
      <view v-if="showCloseButton" class="close-button" @click="onClose">
        <u-icon name="close" size="50rpx" color="#ffffff"></u-icon>
      </view>

      <!-- 标题 -->
      <view v-if="title" class="modal-title" :style="titleStyle">
        <text>{{ title }}</text>
      </view>

      <!-- 内容区域 -->
      <view class="modal-content">
        <slot></slot>
      </view>

      <!-- 底部按钮区域 -->
      <view v-if="buttonText" class="modal-footer">
        <u-button
          :text="buttonText"
          type="primary"
          shape="circle"
          :loading="buttonLoading"
          :disabled="buttonDisabled"
          class="footer-button"
          :custom-style="buttonStyle"
          @click="onButtonClick">
        </u-button>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts" name="common-modal">
interface Props {
  show: boolean
  title?: string
  showCloseButton?: boolean
  buttonText?: string
  buttonLoading?: boolean
  buttonDisabled?: boolean
  closeOnOverlay?: boolean
  titleStyle?: Record<string, any>
  buttonStyle?: Record<string, any>
}

interface Emits {
  (e: 'update:show', value: boolean): void
  (e: 'button-click'): void
  (e: 'close'): void
}

const props = withDefaults(defineProps<Props>(), {
  title: '',
  showCloseButton: true,
  buttonText: '',
  buttonLoading: false,
  buttonDisabled: false,
  closeOnOverlay: true,
  titleStyle: () => ({}),
  buttonStyle: () => ({}),
})

const emit = defineEmits<Emits>()

// 控制动画状态
const animationShow = ref(false)
// 控制元素显示状态
const internalShow = ref(false)
// 定时器引用
let hideTimer: NodeJS.Timeout | null = null

// 监听 show 变化，控制动画
watch(
  () => props.show,
  newVal => {
    if (newVal) {
      // 显示时，清除隐藏定时器
      if (hideTimer) {
        clearTimeout(hideTimer)
        hideTimer = null
      }
      // 先显示元素，然后触发动画
      internalShow.value = true
      setTimeout(() => {
        animationShow.value = true
      }, 10)
    } else {
      // 隐藏时先触发退出动画
      animationShow.value = false
      // 等待动画完成后隐藏元素
      hideTimer = setTimeout(() => {
        internalShow.value = false
        hideTimer = null
      }, 300)
    }
  },
  { immediate: true }
)

// 关闭弹窗
function onClose() {
  emit('update:show', false)
  emit('close')
}

// 按钮点击
function onButtonClick() {
  emit('button-click')
}

// 点击遮罩层
function onOverlayClick() {
  if (props.closeOnOverlay) {
    onClose()
  }
}
</script>

<style lang="scss" scoped>
.common-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  box-sizing: border-box;
}

.common-modal-container {
  position: relative;
  background: #ffffff;
  border-radius: 24rpx;
  width: 650rpx;
  max-height: 80vh;
  box-sizing: border-box;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
  padding: 0 40rpx;
}

.close-button {
  position: absolute;
  top: -70rpx;
  right: 0;
  width: 80rpx;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
  margin-bottom: 40rpx;
}

.modal-title {
  padding: 24rpx 0 35rpx 0;
  text-align: center;
  color: #333333;
  font-size: 28rpx;
  font-weight: bold;
}

.modal-content {
  max-height: 60vh;
  overflow-y: auto;

  &::-webkit-scrollbar {
    width: 0;
    background: transparent;
  }
}

.modal-footer {
  margin: 40rpx 0;
  display: flex;
  gap: 24rpx;
  justify-content: center;

  .footer-button {
    flex: 1;
    width: 100%;
    line-height: 68rpx;
  }

  .cancel-button {
    background: #f5f5f5;
    color: #666666;
    border: 1rpx solid #e0e0e0;
  }

  .confirm-button {
    background: #459af7;
    color: #ffffff;
  }
}

/* 动画样式已移至行内样式 */
</style>
