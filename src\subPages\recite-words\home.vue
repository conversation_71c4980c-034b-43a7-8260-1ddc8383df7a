<template>
  <view class="min-h-screen bg-gray-50">
    <!-- 状态栏占位 -->
    <view
      class="w-full bg-gray-50"
      :style="{
        height: `${placeholderHeight}px`,
        paddingTop: '0px',
      }">
    </view>
    <view class="mx-30rpx">
      <!-- 用户信息区域 -->
      <view class="flex relative h-200 mt-16rpx">
        <view class="mr-30rpx">
          <image
            :src="userInfo?.headIcon || defaultAvatar"
            class="w-140 h-140 rounded-full bg-gray-200"
            mode="aspectFill">
          </image>
        </view>
        <view class="flex-1 mt-14rpx">
          <text class="block text-40rpx font-medium text-#333333 mb-18rpx">{{
            userInfo?.realName || '暂无名称'
          }}</text>
          <text class="block text-28rpx text-#969DAB">已学习{{ studyDays }}天~</text>
        </view>
        <image
          class="absolute top-[-16rpx] right-0 w-232 h-196"
          :src="getSystemImg('687758481807a96b974f9916/68832fd1cfdce7607d9cd878')"
          mode="scaleToFill" />
      </view>

      <!-- 功能菜单 -->
      <view class="bg-white rounded-16rpx shadow-sm px-40rpx">
        <view
          v-for="(item, index) in menuItems"
          :key="item.key"
          class="flex items-center py-30rpx"
          :style="{
            borderBottom: index < menuItems.length - 1 ? '1rpx solid #EEEEEE' : 'none',
          }"
          @click="handleMenuClick(item)">
          <view class="w-60rpx h-60rpx flex items-center justify-center mr-20rpx">
            <image :src="getSystemImg(item.icon)" :class="item.iconSize" mode="scaleToFill" />
          </view>
          <text class="flex-1 text-32rpx text-gray-800">{{ item.title }}</text>
          <u-icon name="arrow-right" size="12" color="#C7C7CC"></u-icon>
        </view>
      </view>
    </view>
    <!-- 底部导航 -->
    <ReciteTabbar current-page="home" />
  </view>
</template>

<script setup lang="ts" name="recite-words-home">
import { computed, onMounted, ref } from 'vue'
import ReciteTabbar from './components/ReciteTabbar.vue'
import { getHeadIcon, getSystemImg, getStartEndTime } from '@/utils'
import useUserStore from '@/store/modules/user'
import useMemberStore from '@/store/modules/member'
import { getCameraClockInRecordsDataList, getOnlineRecordsDataList } from '@/api/project/clock-in'

const userStore = useUserStore()

// 占位高度：H5用状态栏高度，小程序用胶囊按钮距离屏幕顶部的距离
const placeholderHeight = computed(() => {
  try {
    // 判断是否为小程序环境
    if (typeof uni.getMenuButtonBoundingClientRect === 'function') {
      // 小程序：使用胶囊按钮底部距离屏幕顶部的距离
      return uni.getMenuButtonBoundingClientRect().bottom
    } else {
      // H5或其他平台：使用状态栏高度
      const systemInfo = uni.getSystemInfoSync()
      return systemInfo.statusBarHeight || 44
    }
  } catch (error) {
    console.error('获取占位高度失败:', error)
    return 44 // 兜底值
  }
})

// 用户信息
const userInfo = computed(() => userStore.userInfo)

// 默认头像
const defaultAvatar = ref('')

// 学习天数（从打卡记录计算得出）
const studyDays = ref(0)

// 菜单配置
const menuItems = ref([
  {
    key: 'logout',
    title: '退出登录',
    icon: '687758481807a96b974f9916/68832fd8cfdce7607d9cd87a',
    iconSize: 'w-32rpx h-32rpx',
    action: 'logout',
  },
  {
    key: 'settings',
    title: '设置',
    icon: '687758481807a96b974f9916/68832fdccfdce7607d9cd87c',
    iconSize: 'w-36rpx h-36rpx',
    action: 'settings',
  },
])

// 获取系统信息
onMounted(() => {
  // 获取默认头像
  getDefaultAvatar()

  // 获取学习天数
  getStudyDays()
  userStore.refreshUserInfo()
})

// 获取默认头像
function getDefaultAvatar() {
  try {
    defaultAvatar.value = getHeadIcon()
  } catch (error) {
    console.error('获取默认头像失败:', error)
  }
}

// 获取学习天数 （连续打卡天数）
async function getStudyDays() {
  try {
    // 获取当前月份的开始和结束时间
    const today = getStartEndTime()

    // 获取拍照打卡记录
    const cameraRecords = await getCameraClockInRecordsDataList(today.startTime, today.endTime)

    // 获取在线学习记录
    const onlineRecords = await getOnlineRecordsDataList(today.startTime, today.endTime)

    // 合并所有记录
    const allRecords = [...cameraRecords.data.list, ...(onlineRecords.data as [])]

    // 计算连续学习天数
    const completedRecords = allRecords.filter(
      (item: any) => item.is_complete === 1 && new Date(item.date).getDate() < new Date().getDate()
    )

    // 按日期排序
    completedRecords.sort((x: any, y: any) => x.date - y.date)

    // 计算连续天数
    let continuousDays = 0
    completedRecords.forEach((item: any, index: number) => {
      if (index !== completedRecords.length - 1 && item.date !== completedRecords[index + 1].date) {
        continuousDays++
      }
      if (
        index !== 0 &&
        new Date(item.date).getDate() - new Date(completedRecords[index - 1].date).getDate() > 1
      ) {
        continuousDays = 0
      }
      if (index === completedRecords.length - 1) {
        continuousDays++
      }
    })

    studyDays.value = continuousDays
  } catch (error) {
    console.error('获取学习天数失败:', error)
    // 如果获取失败，保持默认值
    studyDays.value = 0
  }
}

// 统一的菜单点击处理
function handleMenuClick(item: any) {
  switch (item.action) {
    case 'logout':
      handleLogout()
      break
    case 'settings':
      handleSettings()
      break
    default:
      console.log('未知的菜单操作:', item.action)
  }
}

// 退出登录
function handleLogout() {
  uni.showModal({
    title: '提示',
    content: '是否确认退出',
    success: success => {
      if (success.confirm) {
        useUserStore().userLogout()
        useMemberStore().memberLogout()
        uni.switchTab({
          url: '/',
        })
      }
    },
  })
}

// 设置
function handleSettings() {
  uni.showToast({
    title: '设置功能开发中',
    icon: 'none',
  })

  // TODO: 跳转到设置页面
  // uni.navigateTo({
  //   url: '/subPages/recite-words/settings'
  // })
}
</script>
