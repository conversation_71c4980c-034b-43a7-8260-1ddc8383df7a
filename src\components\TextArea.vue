<template>
  <view class="sp-editor-wrapper">
    <view class="flex">
      <u-icon name="photo" size="32px" @click="insertImage"></u-icon>
      <u-icon name="calendar" size="32px" @click="insertText" />
      <u-icon name="trash" size="32px" class="m-l-auto" @click="ClearAll" />
    </view>
    <!-- 分割线 -->
    <view class="divider"></view>
    <editor
      id="editor"
      class="ql-editor editor-container"
      :class="{ 'ql-image-overlay-none': readOnly }"
      show-img-toolbar
      :placeholder="placeholder"
      :read-only="isreadOnly"
      @input="answerValue($event)"></editor>
    <up-overlay :show="isMaskVisible"></up-overlay>
  </view>
</template>

<script setup lang="ts">
import { getCurrentInstance } from 'vue' // uniapp的getCurrentInstance
import type { ShortAnswer } from '@/api/project/exercises/type'
import useExercisesStore from '@/store/modules/exercises'
import { miniProgramUploadFile } from '@/utils/index'
// Props
const props = defineProps<Props>()
// Emit,传递数据给父组件

const emit = defineEmits(['updateAnswer', 'keyboardHeightChange'])
const useExercises = useExercisesStore() // 这是一个 store,里面有一些题目答题的数据,主要这里是用来进行,解析模式下的数据回显
const instance = getCurrentInstance() // 获取当前实例

let isLoading = false // 状态变量来管理加载状态
const isMaskVisible = ref(false) // 控制遮罩层显示与隐藏
interface Props {
  isInputVisible: boolean
  shortAnswers: ShortAnswer[]
  placeholder?: string
  readOnly?: boolean
  isAnalysis?: boolean // 是否是解析模式
  curIndex: number
}
// 设置默认值
// const editorId = props.editorId || 'editor'
const placeholder = props.placeholder || '答题区域 ~'
const isreadOnly = ref(false)
// Data, 定义编辑器上下文
let editorCtx: any = null
const keyboardHeight = ref(0) // 键盘高度
// 定义事件
let timerId: ReturnType<typeof setTimeout> | null = null // 用于保存定时器 ID

function fixCursor() {
  // 设置只读状态，避免光标显示到第一个，这是 editor 组件的一个小问题
  isreadOnly.value = true // 先设置为只读

  // 清除之前的定时器（如果有的话）
  if (timerId) {
    clearTimeout(timerId)
  }

  // 创建新的定时器
  timerId = setTimeout(() => {
    isreadOnly.value = false // 300毫秒后再回复可编辑状态
  }, 300)
}
// 注册键盘高度变化的监听
const onKeyboardHeightChange = (res: any) => {
  keyboardHeight.value = res.height // 获取键盘高度
  console.log('键盘高度:', keyboardHeight.value)
  // 触发事件并传递键盘高度
  emit('keyboardHeightChange', keyboardHeight.value)
}

const onEditorReady = () => {
  console.log('文本框组件是否显示', props.isInputVisible)

  // 使用 uni.createSelectorQuery() 获取编辑器上下文
  const query = uni.createSelectorQuery().in(instance?.proxy) // 将选择范围设置为当前组件

  // 返回一个 Promise 来处理异步获取上下文
  const getEditorContext = () => {
    return new Promise((resolve, reject) => {
      query
        .select('#editor') // 选择编辑器组件
        .context((res: any) => {
          if (res) {
            editorCtx = res.context // 获取编辑器上下文
            console.log('获取编辑器上下文成功', editorCtx)
            resolve(editorCtx)
          } else {
            console.error('无法获取编辑器上下文')
            reject(new Error('无法获取编辑器上下文'))
          }
        })
        .exec()
    })
  }

  // 设置是否只读，这里是父组件传递过来的，可以设置是否只读
  isreadOnly.value = props.readOnly || false

  // 调用 getEditorContext 函数并处理 Promise
  getEditorContext()
    .then(() => {
      console.warn('获取编辑器上下文成功，开始设置内容')

      // 确保获取到编辑器上下文后再进行条件判断
      if (props.isAnalysis && props.isInputVisible) {
        const listData = useExercises.getListData()
        console.log('解析模式下的数据', listData)

        // 设置编辑器内容
        console.warn('第一次点击时可以看到的下标', props.curIndex)

        editorCtx.setContents({
          text: listData[props.curIndex].user_answer,
          html: listData[props.curIndex].user_answer,
          success: () => {
            // uni.hideKeyboard(); // 隐藏键盘
            console.log('通过store回显答案成功')
          },
          fail: (err: any) => {
            console.error('回显答案失败:', err)
          },
        })
        isreadOnly.value = true
      }
    })
    .catch(err => {
      console.error(err)
    })
}

const insertText = () => {
  const date = new Date()
  const formatDate = `${date.getFullYear()}/${date.getMonth() + 1}/${date.getDate()}`
  editorCtx.insertText({
    text: formatDate,
  })
}
const insertImage = () => {
  if (props.isAnalysis) {
    return
  }
  if (isLoading) return

  isLoading = true
  isMaskVisible.value = true // 显示遮罩层
  uni.chooseImage({
    success: (res: any) => {
      uni.showLoading({
        title: 'loading...',
      })

      const uploadPromises = res.tempFilePaths.map((filePath: string) => {
        return miniProgramUploadFile(filePath).then((data: any) => {
          const imageUrl = `${import.meta.env.VITE_SERVE}${data.data.url}` // 获取完整的图片 URL

          // 插入图片到编辑器
          editorCtx?.insertImage({
            src: imageUrl,
          })
          console.log('插入图片成功', data.data.url)

          // 返回处理后的数据
          return {
            name: data.data.name, // 获取文件名
            url: data.data.url, // 使用上传响应中的 URL
          }
        })
      })

      Promise.all(uploadPromises)
        .then(uploadedImages => {
          // 创建负载数据
          const payload = {
            uploadImgField101: uploadedImages,
          }

          // 调用 createUserImage 接口，将负载存入数据库
        })
        .then(response => {
          console.log('图片信息保存成功', response)
        })
        .catch(err => {
          console.error('上传图片失败', err)
        })
        .finally(() => {
          setTimeout(() => {
            uni.hideLoading()
            isLoading = false
            isMaskVisible.value = false
          }, 2000)
        })
    },
    fail: () => {
      isLoading = false
      isMaskVisible.value = false
      uni.hideLoading()
    },
  })
}
const ClearAll = () => {
  if (props.isAnalysis) {
    // 解析模式下不能清空
    return
  }
  uni.showModal({
    title: '清空编辑器',
    content: '确定清空编辑器全部内容？',
    success: res => {
      if (res.confirm) {
        editorCtx.clear({
          success(res: any) {
            console.log('clear success')
          },
        })
      }
    },
  })
}
let debounceTimer: ReturnType<typeof setTimeout> // 使用 ReturnType 来适应不同环境 ;

// 定义 debounce 函数，func 是一个接受任意参数并返回 void 的函数
const debounce = <T extends (...args: any[]) => void>(
  func: T,
  delay: number
): ((...args: Parameters<T>) => void) => {
  return function (this: any, ...args: Parameters<T>): void {
    clearTimeout(debounceTimer)
    debounceTimer = setTimeout(() => {
      func.apply(this, args)
    }, delay)
  }
}

const answerValue = debounce((event: any) => {
  // console.log(event.detail )
  const { html, text } = event.detail
  // 触发自定义事件，将 html 和 text 传递给父组件
  emit('updateAnswer', { html, text })
}, 400) // 400毫秒延迟

// 方法：根据 curIndex 查询对应的 questionId
const getQuestionByCurIndex = (shortAnswers: ShortAnswer[], curIndex: number) => {
  // 查找对应的题目
  const question = shortAnswers.find(ans => ans.questionId === curIndex)

  if (question) {
    // console.log('查询到的题目数据:', question)
    return question // 返回当前题目的完整对象
  } else {
    // console.log('未找到对应的题目')
    return null // 或者返回一个默认值
  }
}

// watch(
//   () => [props.curIndex, props.isInputVisible], // 监听多个属性
//   async ([newCurIndex, newIsInputVisible], [oldCurIndex, oldIsInputVisible]) => {
//     // console.log(`curIndex 发生变化: ${oldCurIndex} -> ${newCurIndex}`);
//     console.log(`isInputVisible 发生变化: ${oldIsInputVisible} -> ${newIsInputVisible}`)
//     // 这里可以根据两个属性的变化执行相应的逻辑
//   }
// )
// 定义子组件的方法curIndex是从0开始的,当前页面上的题号
const someMethod = (curIndex: number, isInputVisible: boolean) => {
  // 如果是解析模式，就设置为只读
  if (props.isAnalysis && isInputVisible) {
    const listData = useExercises.getListData()

    console.warn('子组件当前的下标是', curIndex)
    editorCtx.setContents({
      text: listData[curIndex].user_answer,
      html: listData[curIndex].user_answer,
      success: () => {
        // uni.hideKeyboard() // 隐藏键盘
        console.log('store回显答案成功')
        isreadOnly.value = true
      },
      fail: (err: any) => {
        console.error('回显答案失败:', err)
      },
    })
    return
  }
  editorCtx.clear({
    success(res: any) {
      // console.log('clear success')
    },
  })
  // curIndex也是从零开始的
  // console.log('子组件中的答案数组是:', props.shortAnswers, '同时接收到的题号是', curIndex)
  // 回显答案
  const questionData = getQuestionByCurIndex(props.shortAnswers, curIndex)
  if (questionData) {
    // 如果查询的题目有答案，就回显答案
    editorCtx.setContents({
      text: questionData?.answerText,
      html: questionData?.answerHtml,
      success: () => {
        fixCursor()
        // uni.hideKeyboard() // 隐藏键盘
        console.log('回显答案成功')
      },
      fail: (err: any) => {
        console.error('回显答案失败:', err)
      },
    })
  } else {
    console.log('当前题的答案还没有')
  }
}

// 使用 defineExpose 来暴露方法
defineExpose({ someMethod })

// 生命周期
onMounted(() => {
  uni.onKeyboardHeightChange(onKeyboardHeightChange) // 监听键盘高度变化
  onEditorReady()
})

// 在组件卸载时移除监听
onBeforeUnmount(() => {
  uni.offKeyboardHeightChange(onKeyboardHeightChange)
  if (timerId) {
    clearTimeout(timerId)
  } // 清除定时器
})
</script>

<style lang="scss" scoped>
$paddingValue: 20rpx; // 通用内边距
$base-width: 710rpx; // 通用宽度

.sp-editor-wrapper {
  width: calc(710rpx - 40rpx);
  border: 1px solid #ccc;
  /* 添加边框 */
  /* 添加内边距 */
  border-radius: 8rpx;
  /* 可选：添加圆角 */
  background-color: #fff;
  /* 可选：设置背景色 */
  padding: $paddingValue; // 使用变量
}

.divider {
  height: 2rpx;
  /* 控制分割线的高度 */
  background-color: #e0e0e0;
  /* 分割线颜色 */
  margin: 8rpx 0;
  /* 上下间距 */
}
</style>
