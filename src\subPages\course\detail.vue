<template>
  <!-- 播放视频 -->
  <view class="w750rpx h420rpx">
    <video
      v-if="fla"
      id="video"
      :src="nowVideo"
      class="w-full h-full"
      autoplay
      @timeupdate="updateTime"
      @play="setStatus(true)"
      @pause="upVideoTime"></video>
    <view
      v-else
      class="w-full h-full bg-cover bg-no-repeat flex-center"
      :style="`background-image: url(${assembleImgData(
        courseDetail?.course_cover?.[0]
      )}); background-size: cover;`">
      <view class="play-icon flex-center flex-col" @click=";(fla = true), setStatus(true)">
        <u-icon name="play-right-fill" color="#fff" size="72rpx" />
        <text>{{ hintText }}</text>
      </view>
    </view>
  </view>
  <view class="course-panel p24rpx flex u-content-color text-center bg-white">
    <view class="course-panel-item relative flex-1">
      <view class="u-main-color">{{ course_number }}人</view>
      <text class="text-26rpx">在学</text>
    </view>
    <view class="course-panel-item relative flex-1">
      <view class="u-main-color">{{ courseDetail?.course_hours || 64 }}节</view>
      <text class="text-26rpx">课时数</text>
    </view>
    <view class="course-panel-item relative flex-1">
      <view class="u-main-color">{{ course_score }}分</view>
      <text class="text-26rpx">评分</text>
    </view>
  </view>
  <view class="mt20rpx w750rpx bg-white">
    <up-tabs
      :list="tabs"
      :item-style="{ width: '180rpx', height: '100rpx' }"
      :line-width="40"
      :line-height="6"
      :current="tabIndex"
      @change="tabChange"></up-tabs>
    <swiper class="h620rpx" :current="tabIndex" @change="swiperChange">
      <swiper-item :key="111" style="overflow: auto">
        <IntroDuce class="rich" :content="courseDetail?.course_describe" />
      </swiper-item>
      <swiper-item :key="112" style="overflow: auto">
        <DirecTory
          v-if="courseDetail?._id"
          :course-id="courseDetail?._id"
          :dirnum="courseDetail.course_hours" />
      </swiper-item>
      <swiper-item :key="113" style="overflow: auto">
        <ComMents v-if="courseDetail?._id" ref="comMents" :course-no="courseDetail.course_no" />
      </swiper-item>
      <swiper-item :key="114" style="overflow: auto">
        <InforMation v-if="courseDetail?._id" :course-id="courseDetail?._id" />
      </swiper-item>
    </swiper>
  </view>

  <u-tabbar>
    <view class="w-full flex items-center p-x-20rpx justify-between">
      <view class="u-content-color flex flex-col flex-center" @click="TargetWeChatService">
        <u-icon name="kefu-ermai" size="48rpx" />
        <view> 咨询 </view>
      </view>
      <view v-if="!isBuy" class="flex items-center"> </view>
      <view>
        <u-button
          :custom-style="buttonStyle"
          type="primary"
          shape="circle"
          text="我要评价"
          @click="toComment"></u-button>
      </view>
    </view>
  </u-tabbar>
  <PointsModel
    :show="pointsModelShow"
    type="reduce"
    :flag="pointsModelobj.flag"
    :points="pointsModelobj.points"
    :content="pointsModelobj.content"
    @close="pointsModelShow = false" />
</template>

<script setup lang="ts" name="course-detail">
import type { ICatalogHistoryData, ICourseItemResultType } from '../../api/project/course/type'
import IntroDuce from './TabsItem/IntroDuce.vue'
import ComMents from './TabsItem/ComMent.vue'
import InforMation from './TabsItem/InforMation.vue'
import DirecTory from './TabsItem/DirecTory.vue'
import PointsModel from '@/components/PointsModel.vue'
import {
  getCourseData,
  getHistoryCourseTime,
  updateCourseDirecTime,
} from '@/api/project/course/index'
import { TargetWeChatService, assembleImgData, getSystemImg } from '@/utils'
import useMemberStore from '@/store/modules/member'
import useCourseStore from '@/store/modules/course'

const comMents = ref<InstanceType<typeof ComMents>>()

const memberStore = useMemberStore()
const courseStore = useCourseStore()
// 积分弹窗
const pointsModelShow = ref(false)
const pointsModelobj = ref({
  flag: false,
  points: 0,
  content: '',
})

onShow(() => {
  comMents.value?.loadMore()
})

// 按钮样式
const buttonStyle = ref({
  marginLeft: '20rpx',
  padding: '20rpx',
  letterSpacing: '10rpx',
})

// 打卡学习时长
const timestamp = ref(0)

// 创建视频控制器
const videoContext = uni.createVideoContext('video')

// 当前播放的视频
const nowVideo = computed(() => {
  return courseStore.currentPlay.catalog_url
})
// 当前播放的章节
const catalogId = computed(() => {
  return courseStore.currentPlay.catalog_code
})
// 视频开始时长
const startTime = ref(0)
// 当前播放的时长s
const currentTime = ref(0)
// 总播放时长s
const duration = ref(0)

const fla = ref(false)
// 播放状态
const videoStatus = computed(() => {
  return courseStore.playStatus
})
// 设置播放状态
const setStatus = (flag: boolean) => {
  courseStore.setPlayStatus(flag)
}

// 更新观看历史记录
const upVideoTime = () => {
  if (currentTime.value > 2) {
    if (currentTime.value >= duration.value) {
      currentTime.value = 1
    }
    updateCourseDirecTime({
      catalog_id: catalogId.value,
      play_time: currentTime.value,
      play_total_time: duration.value,
      play_progress: Number((currentTime.value / duration.value).toFixed(2)),
    })
  }
}

// 监听播放进度
const updateTime = (e: any) => {
  currentTime.value = Math.floor(e.detail.currentTime)
  duration.value = Math.floor(e.detail.duration)
}

// 设置观看进度
const setVideoTime = async () => {
  const {
    data: { list },
  } = await getHistoryCourseTime(catalogId.value)
  videoContext.seek(list[0].play_time)
  startTime.value = list[0].play_time
}

// 按钮提示
const hintText = ref('免费试看')
// tabs导航
const tabs = ref([
  {
    id: 1,
    name: '介绍',
  },
  {
    id: 2,
    name: '目录',
  },
  {
    id: 3,
    name: '评论',
  },
  {
    id: 4,
    name: '资料',
  },
])
// 当前tab
const tabIndex = ref(0)
const tabChange = (val: any) => {
  tabIndex.value = val.index
}
const swiperChange = (val: any) => {
  tabIndex.value = val.detail.current
}

// 课程详情
const courseDetail = ref<ICourseItemResultType>()
// 是否已购买
const isBuy = ref(false)
// 验证课程是否已购买
const verifyCoursebuy = async () => {
  const res = await memberStore.pointsAction(
    '课程1套',
    courseDetail.value?.course_price,
    courseDetail.value?.course_no
  )
  if (!res.is) {
    if (res?.flag) {
      pointsModelobj.value.flag = res!.flag
    } else {
      pointsModelobj.value = res as any
    }
    pointsModelShow.value = true
  }
}

watchEffect(() => {
  if (videoStatus.value) {
    fla.value = true
    videoContext.play()
    setVideoTime()
  } else {
    videoContext.pause()
  }
})

// 课程人数
const course_number = computed(() => {
  return 3255
})
// 课程评分
const course_score = computed(() => {
  return 5.0
})

onLoad(async (val: any) => {
  const { data } = await getCourseData(val.id)
  courseDetail.value = data
  uni.setNavigationBarTitle({
    title: data.course_name || '课程详情',
  })
  verifyCoursebuy()
})
onShow(() => {
  if (courseDetail.value === undefined) {
    return
  }
  verifyCoursebuy()
})
// 购买课程
const payCourse = () => {
  uni.navigateTo({
    url: `/subPages/course/confirmorder?id=${courseDetail.value?._id}`,
  })
}
onHide(() => {
  upVideoTime()
})

onUnload(() => {
  upVideoTime()
  timestamp.value = currentTime.value - startTime.value

  uni.$emit('clockIn', timestamp.value)
})

// 去评价
const toComment = () => {
  uni.navigateTo({
    url: `/subPages/course/comment?no=${courseDetail.value?.course_no}`,
  })
}
</script>

<style>
page {
  background-color: #f3f4f6;
}
</style>

<style lang="scss" scoped>
.play-icon {
  width: 180rpx;
  height: 160rpx;
  z-index: 9;
  border-radius: 10px;
  background-color: rgba(0, 0, 0, 0.45);

  text {
    margin-top: 8rpx;
    font-size: 26rpx;
    font-weight: 400;
    color: #fff;
  }
}
.course-panel {
  .u-main-color {
    margin-bottom: 8rpx;
  }
  &-item:not(:last-child)::after {
    content: '';
    position: absolute;
    width: 1px;
    height: 60%;
    top: 20%;
    right: 0;
    background-color: #e5e5e5;
  }
}
</style>
