<template>
  <view class="m-y-20rpx flex items-start">
    <view
      class="w30rpx h30rpx rounded-full mt6rpx bg-white b-#459af7 b-2px b-solid flex flex-center">
      <view class="w16rpx h16rpx rounded-full bg-#459af7"></view>
    </view>
    <view class="flex flex-col ml20rpx">
      <view class="text-28rpx text-#333333 fw-500">{{ title || '早晨英语记忆单词500个' }}</view>
      <view class="text-22rpx text-#999999 fw-500 mt15rpx"
        >今天{{ `${formatDate(time[0])}~${formatDate(time[1])}` }}
      </view>
    </view>
  </view>
</template>

<script setup lang="ts" name="clock-in-completeitem">
import useDateFormatter from '@/hooks/useDateFormatter'

defineProps<{
  title?: string
  time: number[]
}>()

const { formatDate } = useDateFormatter('HH:mm')
</script>

<style lang="scss" scoped></style>
