import { Md5 } from 'ts-md5'
import { getModelList } from '../visual'
import type * as UserType from './type'
import type { RequestResponse } from '@/es/request.d'
import { get, post, put } from '@/config/request'

// 用户登录
export const login = (params: UserType.loginParams) => {
  const { VITE_APP_ID } = import.meta.env
  const url = `/api/oauth/login?client_id=admin&client_secret=123456&scope=all&grant_type=sms_code&appId=${VITE_APP_ID}`
  return post<{ token: string }>(url, { ...params })
}
// 验证码登录
export const loginByCaptcha = (params: UserType.codeLoginParams) => {
  return post<{ token: string }>(
    '/api/oauth/login',
    { username: params.phoneNumber, code: params.code },
    {
      params: {
        client_id: 'admin',
        client_secret: 123456,
        scope: 'all',
        grant_type: 'sms_code',
      },
    }
  )
}
// 发送验证码
export const sendCaptcha = (phoneNumber: string) => {
  return get(`/api/oauth/sendMsg/${phoneNumber}`)
}

// 获取当前用户信息
export const getCurrentUser = () => get<UserType.CurrentUserType, null>('/api/oauth/currentUserApp')

// 修改密码
export async function updatePassword(userId: string, password: string) {
  return post<RequestResponse<string>>(
    `/api/mongoSystem/Permission/Users/<USER>/Actions/ResetPassword`,
    {
      userPassword: Md5.hashStr(password),
    }
  )
}

// 获取当前用户模拟考试成绩
export const getCurrentUserExamScore = ({ pageSize = 10, currentPage = 1, filter = [] }) => {
  return getModelList<UserType.ExamScoreType>({
    menuId: '678b08ec7888c22414d8c30b',
    pageSize,
    currentPage,
    filter,
    sort: {
      creatorTime: 'asc',
    },
  })
}

// 获取当前用户的所有租户
export const getCurrentUserCorpList = () => {
  return get<UserType.JoinCorpType>(
    `/api/system/corp/getCorpList`,
    {},
    {
      unMessage: true,
    }
  )
}

// 加入租户
export const joinCorpById = (corpId: string) => {
  return get(`api/system/corp/joinCorp/${corpId}`)
}
// 获取指定租户列表
export const getCorpList = () => {
  return get<UserType.CorpType[]>(`/corpPrifile.json`)
}

// 加入企业
export const joinCorp = (code: string) => {
  return get(`/api/system/corp/joinCorp/${code}`)
}
// 创建企业
export const createCorp = (name: string) => {
  return post(`/api/system/corp/create`, { name })
}

// 获取公开Token
export const getPublicToken = () => {
  const corpId = uni.getStorageSync('corpId')
  return get<string>(`/api/oauth/getOpenToken/${corpId || import.meta.env.VITE_TENANT_ID}`)
}

// 修改用户信息
export const updateUser = (data: UserType.UserInfo) => {
  return put<string>(`/api/system/permission/users/${data.id}`, { ...data })
}
