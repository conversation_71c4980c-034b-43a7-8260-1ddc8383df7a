<template>
  <view class="attachment-list">
    <view
      v-for="item in attachment"
      :key="item.fileId"
      class="flex border-b border-b-solid border border-gray-200 pb2">
      <i class="iconfont icon-wenjian" style="font-size: 82rpx"></i>
      <view class="flex flex-col">
        <text class="text-sm">{{ item.name }}</text>
        <text class="text-xs text-gray-400 mt1">未知大小</text>
      </view>
      <view
        class="flex-1 text-right flex items-center justify-end text-xs text-blue"
        @click="download(item)">
        <i class="iconfont icon-yunduanxiazai mr-1"></i>
        文件下载
      </view>
    </view>
  </view>
</template>

<script setup lang="ts" name="AttachmentList">
import type { PropType } from 'vue'
import { downloadFile, hideLoading, showLoading } from '@/utils'

defineProps({
  // 附件
  attachment: {
    type: Array as PropType<UploadFzData[]>,
    required: true,
  },
})

async function download(item: UploadFzData) {
  showLoading('下载中...')
  await downloadFile(item.url, item.name)
  hideLoading()
}
</script>

<style lang="scss" scoped></style>
