export interface ICourseItemResultType {
  _id: string
  teaching_method: string
  course_describe: string
  course_grade: string
  course_subject: string
  subject_module: string
  course_name: string
  course_hours: string
  lecturer: string
  course_price: number
  course_tag: string[]
  creatorTime: number
  course_no: string
  course_cover: UploadImgData[]
}

export interface ICourseDirecToryResuleType {
  _id: string
  course_directory_name: string
  course_id: string
  creatorUserId: UserIdData
  course_directory_no: string
  creatorTime: number
  tableField107: ICatalogTableType[]
  sort: number
}

//章节
export interface ICatalogTableType {
  catalog_name: string
  catalog_code: string
  catalog_type: string
  catalog_url: string
  catalog_duration: string
  catalog_istry: string
}

// 历史记录
export interface ICatalogRequestData {
  catalog_id: string
  play_time: number
  play_total_time: number
  play_progress: number
}
export interface ICatalogHistoryData {
  _id: string
  catalog_id: string
  play_progress: number
  creatorTime: number
  play_total_time: number
  play_time: number
}

// 评论
export interface ICourseCommentRequestData {
  course_no: string
  evaluation_score: number
  evaluation_content: string
}

export interface ICourseCommentItemData {
  _id: string
  creatorUserId: UserIdData
  evaluation_content: string
  course_no: string
  creatorTime: number
  evaluation_score: number
}

// 资料
export interface ICourseInfomationType {
  _id: string
  course_id: string
  creatorUserId: UserIdData
  data_name: string
  creatorTime: number
  data_content: string
  data_url: UploadFzData[]
}

// 推荐课程
export interface ICourseRecommendType {
  _id: string
  select_course: string
  tableField102: ICourseRecommendTable[]
  creatorTime: number
  lastModifyTime: number
}

export interface ICourseRecommendTable {
  course_name: string
  course_cover: UploadImgData[]
  recommend: string
  course_price: number
  course_tag: string[]
}
