<script setup lang="ts">
onLaunch(async () => {})
onShow(async () => {
  // #ifdef MP
  // 小程序端检查更新
  miniProgramCheckUpdate()
  // #endif
})
onHide(() => {})
// 检查更新

function miniProgramCheckUpdate() {
  const updateManager = uni.getUpdateManager()
  updateManager.onCheckForUpdate(res => {
    // 请求完新版本信息的回调
    console.log('onCheckForUpdater：是否有版本更新', res.hasUpdate)
  })
  updateManager.onUpdateReady(res => {
    uni.showModal({
      title: '更新提示',
      showCancel: false,
      confirmText: '马上重启',
      content: '新版本已经上线，需要您重启小程序以应用新版本。',
      success(res) {
        if (res.confirm) {
          // 新的版本已经下载好，调用 applyUpdate 应用新版本并重启
          updateManager.applyUpdate()
        }
      },
    })
  })

  updateManager.onUpdateFailed(res => {
    // 新的版本下载失败
    uni.$u.toast('新版本下载失败，请删除当前小程序，重新搜索打开。')
    console.log('onUpdateFailed', res)
  })
}
</script>

<style lang="scss">
@import 'uview-plus/index.scss';
@import './styles/theme.scss';
.box-shadow {
  box-shadow: 0rpx 0rpx 3rpx 3rpx #e5e5e5;
}
//--------------以下为数学公式下划线样式和着重号样式----------------
.dot {
  text-emphasis-style: dot;
  text-emphasis-position: under left;
}
.single {
  min-width: 40px; /* 设置最小宽度 */
  display: inline-block; /* 确保即使没有内容，元素仍然占据空间 */
  position: relative;
  padding-bottom: 2px; /* 确保下划线和文本有一定空间 */
  line-height: 1.5; /* 设置行高确保元素高度一致 */
  vertical-align: bottom; /* 可以根据需要选择 bottom 或 middle */
}
.single::after {
  content: ''; /* 伪元素，保证即使没有内容也显示下划线 */
  position: absolute;
  left: 0;
  bottom: 0;
  width: 100%;
  height: 2px; /* 下划线的厚度 */
  background-color: currentColor; /* 使用文本的颜色 */
}
//--------------以下为数学公式下划线样式和着重号样式----------------
</style>
