<template>
  <view class="w-full">
    <view class="m-y-20rpx pl-20rpx">
      <up-tabs
        :list="subjectTabs"
        line-width="0"
        line-color="#f56c6c"
        :active-style="{
          color: '#459AF7',
          fontWeight: 'bold',
          transform: 'scale(1.2)',
          transition: 'transform 0.3s ease-in-out',
        }"
        :inactive-style="{
          color: '#606266',
          transform: 'scale(1)',
          transition: 'transform 0.3s ease-in-out',
          fontSize: '28rpx',
        }"
        item-style="padding-left: 15px; padding-right: 15px; height: 34px;"
        @change="debouncedHandleSubjectChange">
      </up-tabs>
    </view>
    <view class="p-x-20rpx box-border">
      <TabContainer
        :current-subject-module="currentSubjectModule"
        :current-subject="currentSubject" />
    </view>
  </view>
</template>

<script setup lang="ts" name="course">
import TabContainer from './components/TabContainer.vue'
import { useDebounce } from '@/hooks/useDebounce'
import useUserStore from '@/store/modules/user'
import { getSubject, getSubjectModule } from '@/api/project/exercises/index'

const useStore = useUserStore()
const subjectModule = ref()
// 科目模块
const subjectTabs = computed(() => {
  return subjectModule.value?.map((item: any) => ({
    name: item.subject,
    value: item.subject,
  }))
})

// 当前科目的模块
const currentSubjectModule = ref()
const currentSubject = ref()

// 科目切换
async function handleSubjectChange(currentItem: any) {
  if (currentItem.value === currentSubject.value) {
    return
  }
  currentSubject.value = currentItem.value
  console.log(currentSubject.value)
  await getModuleInfo(currentItem.value)
}

const debouncedHandleSubjectChange = useDebounce(handleSubjectChange, 300)

// 查询专升本科目模块
async function getSubjectInfo() {
  const subject = await getSubject()
  subjectModule.value = subject.data.list[0].tableField107
  currentSubject.value = subject.data.list[0].tableField107[0].subject
  // 获取科目模块
  getModuleInfo(subject.data.list[0].tableField107[0].subject)
}

// 获取科目模块
async function getModuleInfo(subject: string) {
  const Modules = await getSubjectModule('精选课程', subject)
  currentSubjectModule.value = Modules.data.list
}

onShow(async () => {
  getSubjectInfo()
})
</script>

<style>
page {
  background-color: #f4f5f7;
}
</style>

<style lang="scss" scoped></style>
