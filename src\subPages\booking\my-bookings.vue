<template>
  <view class="pb-120rpx">
    <u-tabs
      :list="[
        { name: '已预约', value: 'active' },
        { name: '已完成', value: 'completed' },
      ]"
      :current="curIndex"
      line-width="80rpx"
      line-height="8rpx"
      line-color="#459af7"
      :active-style="{
        color: '#459AF7',
        fontWeight: '600',
      }"
      :inactive-style="{
        color: '#333333',
        fontSize: '30rpx',
      }"
      :item-style="{ width: '50%', height: '100rpx' }"
      @change="onTabChange">
    </u-tabs>
    <!-- 课程 -->
    <view v-if="loading" class="page-load flex-center">
      <u-loading-icon text="预约加载中" text-size="36rpx" />
    </view>
    <view v-else>
      <view
        v-for="(item, index) in groupedData"
        :key="index"
        class="w690rpx bg-white rounded-10rpx mt20rpx mx-auto pt-22rpx pb-30rpx px-44rpx box-border">
        <view>
          <view class="flex justify-between text-36rpx color-#459AF7 font-600">
            <view>{{ item.school }}</view>
            <view>{{ formatDate(item.start_class_date) }}</view>
          </view>
          <view v-for="(item1, index1) in item.list" :key="item1._id" class="course-item">
            <view class="text-25rpx color-#333333 mt-10rpx relative left-[-10rpx]">
              《{{ item1.course_name }}》<text class="ml-18rpx">{{ item1.chapter }}</text></view
            >
            <view class="text-25rpx color-#666666 mt-10rpx"
              >{{ item1.startTime }}--{{ item1.endTime }}</view
            >
          </view>
        </view>
      </view>
    </view>
    <view class="mt200rpx">
      <up-empty v-if="empty" mode="list" text="暂无预约"> </up-empty>
    </view>
  </view>
</template>

<script setup lang="ts" name="my-bookings">
import useDateFormatter from '@/hooks/useDateFormatter'
import {} from '@/utils/index'
import { getBookingList } from '@/api/project/booking/index'
import type { MyBookingListItem } from '@/api/project/booking/type'
const { formatDate } = useDateFormatter('M月D日')
const loading = ref(true)
const empty = ref(false)
type MyBookingListItemWithStatus = MyBookingListItem & {
  status?: string
}
const list = ref([] as MyBookingListItemWithStatus[])
// 动态生成 filter 的函数
const filter = ref()
const curIndex = ref(0)

function setLoadingState(list: any) {
  loading.value = false
  if (list.length === 0) empty.value = true
}

// 将时间字符串和日期组合成完整的时间戳
function addTimeStringToTimestamp(timeString: string, baseTimestamp: number): number {
  const baseDate = new Date(baseTimestamp)
  const year = baseDate.getFullYear()
  const month = baseDate.getMonth()
  const day = baseDate.getDate()
  const splitString = timeString.split(':').map(Number)
  const [hours, minutes, seconds] =
    splitString.length == 2 ? [splitString[0], splitString[1], 0] : splitString
  const dateTimeString = new Date(year, month, day, hours, minutes, seconds)

  return dateTimeString.getTime()
}

function generateFilter(index: number) {
  const baseFilter = [
    {
      enCode: 'creatorUserId',
      method: 'eq',
      type: 'systemField',
      value: ['currentUser'],
    },
  ]

  if (index === 0) {
    return baseFilter // 已预约：只返回用户的预约
  } else if (index === 1) {
    // 已完成：需要在客户端过滤，因为需要结合 endTime 和 start_class_date 以及 type 字段
    return baseFilter
  }
  return []
}
function onTabChange(item: any) {
  curIndex.value = item.index
  loading.value = true
  empty.value = false
  list.value = []
  filter.value = generateFilter(item.index)
  getBookingListInfo()
}

function getBookingListInfo() {
  getBookingList(filter.value).then(e => {
    setLoadingState(e.data.list)
    list.value = e.data.list
  })
}

const groupedData = computed(() => {
  const result = {} as Record<string, any>

  // 根据当前选中的tab过滤数据
  const filteredList = list.value.filter((item: MyBookingListItem) => {
    if (curIndex.value === 0) {
      // 已预约：显示所有预约
      return true
    } else if (curIndex.value === 1) {
      // 已完成：只显示课程结束时间已过的
      const courseEndTime = addTimeStringToTimestamp(item.endTime, item.start_class_date)
      const now = new Date().getTime()
      const isCourseEnded = courseEndTime < now

      return isCourseEnded
    }
    return true
  })

  filteredList.forEach((item: MyBookingListItemWithStatus) => {
    const { school, start_class_date } = item
    const key = `${school}-${start_class_date}`

    // 获取课程结束时间的完整时间戳
    const courseEndTime = addTimeStringToTimestamp(item.endTime, item.start_class_date)
    const courseStartTime = addTimeStringToTimestamp(item.startTime, item.start_class_date)

    // 获取当前时间戳
    const now = new Date().getTime()

    // 判断课程状态
    if (now < courseStartTime) {
      item.status = '未开始'
    } else if (now >= courseStartTime && now <= courseEndTime) {
      // 课程进行中
      item.status = '进行中'
    } else if (now > courseEndTime) {
      // 课程已结束
      item.status = '已完成'
    }

    // 如果校区和开始日期的组合不存在于结果中，则初始化
    if (!result[key]) {
      result[key] = {
        school,
        start_class_date,
        list: [],
      }
    }

    // 将当前项添加到对应的列表中
    result[key].list.push(item)
  })

  // 将结果转换为数组形式
  return Object.values(result)
})

onLoad(() => {
  filter.value = generateFilter(0)
  getBookingListInfo()
})
</script>

<style>
page {
  background-color: #f4f5f7;
}
</style>

<style lang="scss" scoped>
.course-item {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  margin: 10rpx 0;
  border-bottom: 1px solid #d0d0d0;
  padding-bottom: 24rpx;
  &:last-child {
    border-bottom: none;
    padding-bottom: 0;
  }
}

.page-load,
.u-empty {
  height: 100%;
  min-height: 240rpx;
}
</style>
