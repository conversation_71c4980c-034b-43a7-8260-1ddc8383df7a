<template>
  <view class="question-time-group">
    <!-- 时间标签 -->
    <view class="py-24rpx">
      <text class="text-30rpx color-#333333">{{ timeLabel }}</text>
    </view>

    <!-- 题目列表 -->
    <view>
      <view
        v-for="(question, index) in questionList"
        :key="index"
        class="bg-white rounded-16rpx py-36rpx px-28rpx mb-20rpx"
        @click="handleQuestionClick(question)">
        <!-- 题目内容 -->
        <view class="mb-20rpx">
          <mpHtml class="text-28rpx color-#333 leading-58rpx" :content="question.title"></mpHtml>
        </view>

        <!-- 选项 -->
        <view class="grid grid-cols-2 gap-20rpx">
          <view
            v-for="(option, optionIndex) in question.option"
            :key="optionIndex"
            class="flex items-start text-26rpx color-#616A75">
            <text class="mr-8rpx">{{ String.fromCharCode(65 + optionIndex) }}.</text>
            <text class="flex-1">{{ option }}</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts" name="question-time-group">
import type { WrongExercises } from '@/api/project/exercises/type'
import mpHtml from '@/components/mp-html/mp-html.vue'

// 组件属性
defineProps<{
  timeLabel: string
  questionList: WrongExercises[]
}>()

// 组件事件
const emits = defineEmits<{
  questionClick: [question: WrongExercises]
}>()

// 处理题目点击
const handleQuestionClick = (question: WrongExercises) => {
  emits('questionClick', question)
}
</script>

<style lang="scss" scoped></style>
