<template>
  <view class="question-time-group">
    <!-- 时间标签 -->
    <view class="py-24rpx">
      <text class="text-30rpx color-#333333">{{ timeLabel }}</text>
    </view>

    <!-- 题目列表 -->
    <view>
      <view
        v-for="(question, index) in questionList"
        :key="index"
        class="bg-white rounded-16rpx py-36rpx px-28rpx mb-20rpx"
        @click="handleQuestionClick(question)">
        <!-- 题目内容 -->
        <view class="mb-20rpx">
          <text class="text-28rpx color-#333 leading-58rpx">{{ question.content }}</text>
        </view>

        <!-- 选项 -->
        <view class="grid grid-cols-2 gap-20rpx">
          <view
            v-for="(option, optionIndex) in question.options"
            :key="optionIndex"
            class="flex items-start text-26rpx color-#616A75">
            <text class="mr-8rpx">{{ option.label }}.</text>
            <text class="flex-1">{{ option.text }}</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts" name="question-time-group">
interface QuestionOption {
  label: string
  text: string
}

interface Question {
  id: string
  content: string
  options: QuestionOption[]
  correctAnswer: string
  userAnswer: string
  subject: string
  createTime: string
}

// 组件属性
defineProps<{
  timeLabel: string
  questionList: Question[]
}>()

// 组件事件
const emits = defineEmits<{
  questionClick: [question: Question]
}>()

// 处理题目点击
const handleQuestionClick = (question: Question) => {
  emits('questionClick', question)
}
</script>

<style lang="scss" scoped></style>
