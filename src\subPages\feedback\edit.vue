<template>
  <view class="feedback-edit">
    <view class="tip flex">
      <image
        style="width: 32rpx; height: 32rpx"
        src="https://kindoucloud.com:8011/api/file/Image/systemicon/5gxy/20221216_53c95f86f73b4e499e25503b57d79021.png"></image>
      <view class="tip">您对系统提出的宝贵意见，对我们的发展非常重要。</view>
    </view>
    <view class="form">
      <u-cell-group :border="false">
        <u-cell title="意见建议标题">
          <template #label>
            <view class="label-img">
              <u-textarea
                v-model="params.f_title"
                placeholder="请输入反馈标题"
                maxlength="30"
                count
                confirm-type="done"
                bg-color="#EDF3FF"></u-textarea>
            </view>
          </template>
        </u-cell>

        <u-cell title="详情描述">
          <template #label>
            <view class="label-img">
              <u-textarea
                v-model="params.f_detaile"
                placeholder="请详细描述一下您的建议..."
                maxlength="200"
                count
                confirm-type="done"
                bg-color="#EDF3FF"></u-textarea>
            </view>
          </template>
        </u-cell>

        <!-- 现场照片S -->
        <u-cell>
          <template #title>
            <view class="flex-sb flex-ai">
              <view class="cell-title">上传图片</view>
              <view class="cell-value">{{ fileList.length }}/5</view>
            </view>
          </template>
          <template #label>
            <view class="label-img">
              <u-upload
                :file-list="fileList"
                name="pic"
                multiple
                :max-count="5"
                :capture="['album', 'camera']"
                @after-read="afterRead"
                @delete="deletePic"></u-upload>
            </view>
          </template>
        </u-cell>
        <!-- 现场照片E -->

        <u-cell title="是否匿名">
          <template #value>
            <view class="cell-value">
              <u-switch
                v-model="params.f_enable_name"
                :inactive-value="0"
                :active-value="1"></u-switch>
            </view>
          </template>
        </u-cell>
      </u-cell-group>
    </view>

    <view class="submit" @click="submit"> 提交 </view>
  </view>
</template>

<script setup lang="ts" name="feedback-edit">
import { submitFeedback } from '@/api/project/feedback/index'
import { miniProgramUploadFile, showSuccessToast } from '@/utils'
import type { FeedBackSubmitItemType } from '@/api/project/feedback/type'

interface afterReadParams {
  file: {
    url: string
    status: string
    message: string
    name: string
  }[]
}
const fileList = ref<FeedBackSubmitItemType['f_pic'] & afterReadParams['file']>([])
const params = ref({
  f_title: '',
  f_detaile: '',
  f_images: [],
  f_enable_name: 0,
})

async function afterRead({ file }: afterReadParams) {
  const initIndex = fileList.value.length

  // 添加初始状态
  for (const item of file) {
    fileList.value.push({
      ...item,
      status: 'uploading',
      message: '上传中',
    })
  }

  // 上传并修改对于数据
  for (let index = 0; index < file.length; index++) {
    const item = file[index]

    await miniProgramUploadFile(item.url)
      .then(({ data }) => {
        fileList.value.splice(
          initIndex + index,
          1,
          Object.assign(item, {
            status: 'success',
            message: '',
            name: data.name,
            url: import.meta.env.VITE_SERVE + data.url,
          })
        )
      })
      .catch(() => {
        // 删除失败文件
        fileList.value.splice(initIndex + index, 1)
      })
  }
}
// // 删除图片
function deletePic({ index }: { index: number }) {
  fileList.value.splice(index, 1)
}

async function submit() {
  const { f_title, f_detaile } = params.value
  if (!f_title || !f_detaile) {
    uni.showToast({
      title: '请输入标题和内容',
      icon: 'none',
    })
    return
  }

  await submitFeedback({
    ...params.value,
    f_pic: fileList.value.map(item => {
      return {
        name: item.name,
        url: item.url.replace(import.meta.env.VITE_SERVE, ''),
      }
    }),
  })

  showSuccessToast('提交成功')
  setTimeout(() => {
    uni.navigateBack()
  }, 1500)
}
</script>

<style lang="scss" scoped>
.feedback-edit {
  background-color: #f5f7fb;
  padding: 20rpx;
  height: 95vh;
  box-sizing: border-box;

  .tip {
    font-size: 30rpx;
    color: #9296af;
    flex: 1;
    margin: 15rpx;

    image {
      margin-top: 25rpx;
    }
  }

  .form {
    background-color: #ffffff;
    position: relative;
    margin: 15rpx;
    border-radius: 12rpx;
    margin-top: 20rpx;

    .cell-title {
      color: #636676;
    }

    .cell-value {
      font-size: 26rpx;
      color: #9296af;
    }

    .label-img {
      margin: 10rpx 0;
      width: 100%;
    }
  }

  .submit {
    background: #6377f5;
    border-radius: 12rpx;
    margin: 40rpx 15rpx;
    padding: 20rpx;
    text-align: center;
    color: #ffffff;
  }
}
</style>
