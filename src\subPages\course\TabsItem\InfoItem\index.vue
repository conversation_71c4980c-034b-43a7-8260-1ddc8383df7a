<template>
  <view
    class="w-690rpx h-300rpx rounded-20rpx m-y20rpx m-x-auto bg-white p20rpx box-border flex flex-col justify-between">
    <view class="flex justify-between items-center">
      <view class="text-#333333 fw-bold text-30rpx">{{ title || '资料名' }}</view>
      <!-- <u-icon name="arrow-right"></u-icon> -->
    </view>
    <view class="text-26rpx text-#969DAB"> 发布人：{{ publish || '马小跳' }} </view>
    <view class="text-#666666 text-26rpx u-line-2">
      {{ content }}
    </view>
    <view class="flex justify-between items-center">
      <view class="text-#969DAB text-26rpx">{{ formatDate(dataTime || new Date()) }}</view>
      <view>
        <u-button
          text="下载"
          type="primary"
          size="mini
        "
          :custom-style="{
            width: '120rpx',
            height: '54rpx',
          }"
          shape="circle"
          @click="download"></u-button>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts" name="info-item">
import useDateFormatter from '@/hooks/useDateFormatter'
import { showToast } from '@/utils'
const props = defineProps<{
  title?: string
  publish?: string
  dataTime?: number
  content?: string
  dataurl: string
}>()

const { formatDate } = useDateFormatter()

// 下载
const base_url = import.meta.env.VITE_SERVE
const download = () => {
  uni.setClipboardData({
    data: base_url + props.dataurl,
    success: success => {
      showToast('复制成功，请去浏览器粘贴下载。')
    },
    fail: fail => {
      console.log(fail)
    },
  })
}
</script>

<style lang="scss" scoped></style>
