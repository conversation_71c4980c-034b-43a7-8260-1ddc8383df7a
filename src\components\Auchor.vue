<template>
  <view class="w-full h2rpx auchor"> </view>
</template>

<script setup lang="ts" name="author">
const emits = defineEmits(['scrol'])
const instance = getCurrentInstance()
const query = uni.createSelectorQuery().in(instance)

const scrollTop = ref(0)

const setTop = () => {
  query
    .select('.auchor')
    .boundingClientRect((data: any) => {
      scrollTop.value = parseInt(data.top)
    })
    .exec()
}

const refres = () => {
  setTop()
}

onMounted(() => {
  setTimeout(() => {
    query
      .select('.auchor')
      .boundingClientRect((data: any) => {
        scrollTop.value = data.top
        emits('scrol', data.top)
      })
      .exec()
  }, 500)
})

defineExpose({
  refres,
  scrollTop,
})
</script>

<style lang="scss" scoped></style>
