import { defineStore } from 'pinia'
import { getGradeAndSubject } from '@/api/project/exercises/index'

export default defineStore(
  'exercises',
  () => {
    const grades = ref([] as any[])
    const subjects = ref([] as any[])
    const listData = ref([] as any[]) // 新增的状态
    const itemData = ref({} as any) // 新增的状态
    const defaultIndex = ref(0) // 下标
    // 从本地存储中初始化数据
    const localGrades = uni.getStorageSync('userGrades')
    const localSubjects = uni.getStorageSync('userSubjects')

    if (localGrades) {
      grades.value = localGrades
    }

    if (localSubjects) {
      subjects.value = localSubjects
    }

    async function getGradeList() {
      const res = await getGradeAndSubject()
      grades.value = [res.data.list[0].grade]
      subjects.value = res.data.list[0].tableField107
    }
    // 新增的函数，用于设置 listData
    function setListData(data: any[]) {
      listData.value = data
    }

    function getListData() {
      return listData.value
    }

    // 新增的函数，用于设置 itemData
    function setItemData(data: any) {
      itemData.value = data
    }
    // 新增的函数，用于设置 itemData
    function setDefaultIndex(data: any) {
      defaultIndex.value = data
    }
    return {
      grades,
      subjects,
      listData,
      itemData,
      defaultIndex,
      getListData,
      getGradeList,
      setListData,
      setItemData,
      setDefaultIndex,
    }
  },
  {
    persist: {
      storage: {
        getItem: uni.getStorageSync,
        setItem: uni.setStorageSync,
      },
    },
  }
)
