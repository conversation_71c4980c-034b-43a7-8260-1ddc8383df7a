<template>
  <view class="invite container flex-col items-center">
    <TitleBar show-back nav-color="#71b3fa" text-color="white" :flag="false" />
    <image
      style="width: 80%; height: 150rpx"
      :src="getSystemImg('/6747e9ed0a34815816f11159/66bc26e2b23ec40f60a6eb88')" />
    <image
      style="width: 70%; height: 400rpx"
      :src="getSystemImg('/6747e9ed0a34815816f11159/66bc26e2b23ec40f60a6eb89')" />
    <view class="invite-main padding-30 flex-col bg-white">
      <view class="invite-main-head flex justify-between items-center">
        <view class="flex items-center">
          <u-avatar size="90rpx" :src="getHeadIcon(userInfo?.headIcon)"></u-avatar>
          <view class="margin-l-24">{{ userInfo?.realName }}</view>
        </view>
        <view class="save-qrcode flex-center h-fit color-white" @click="saveQrCode">
          保存二维码
        </view>
      </view>
      <view class="invite-main-title u-tips-color">我的邀请二维码</view>
      <view class="invite-main-qrcode flex justify-center overflow-auto">
        <uqrcode
          ref="uqrcodes"
          canvas-id="qrcode"
          :value="qrCode"
          :loading="qrCodeLoading"
          :size="400"
          size-unit="rpx"
          :options="options"
          @complete="qrCodeLoadComplete" />
      </view>
      <view class="text-#999999 text-28rpx text-center">获得积分：{{ inTegral }}</view>
      <view class="flex-center">
        <view class="invite-recode flex-center color-white" @click="toServe">
          查看我的邀请记录
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts" name="user-invite">
import uqrcode from '@/uni_modules/Sansnn-uQRCode/components/u-qrcode/u-qrcode.vue'
import TitleBar from '@/components/TitleBar.vue'
import useUserStore from '@/store/modules/user'
import { getHeadIcon, getSystemImg, showToast } from '@/utils'
import { getPointsInfo } from '@/api/project/member'
import type { IPointsData } from '@/api/project/member/type'

const userInfo = computed(() => {
  return useUserStore().userInfo
})

const uqrcodes = ref()

const qrCode = ref('')
const qrCodeLoading = ref(true)
const options = ref({
  margin: 20,
  foregroundImageSrc:
    'https://wx.qlogo.cn/mmhead/WD4FduqfeKIjTkC5owR1SYZ8iaUdGlXAfua2uFNBuwHHzXC7ibSqLXiba1WLEFibt1VP0NNKNN5nTHI/0',
  errorCorrectLevel: 0,
})
const qrCodeLoadComplete = () => {
  qrCodeLoading.value = false
}
const saveQrCode = () => {
  uqrcodes.value.save({
    success: () => {
      showToast('保存成功')
    },
    fail: () => {
      showToast('保存失败')
    },
  })
}
const toServe = () => {
  uni.navigateTo({
    url: '/subPages/user/invite-records',
  })
}

const pointsList = ref<IPointsData[]>([])
const inTegral = computed<number>(() => {
  return pointsList.value.reduce((pre, cur) => pre + cur.points_num, 0)
})

onLoad(() => {
  qrCode.value = `https://kindoucloud.com:8018/wxe04186c0f2ce20d3?formUser=${userInfo.value?.id}`
  // qrCode.value = `https://kindoucloud.com:8018/wxe58acb08d154150b?formUser=668cd43553a3e93f505933c1`
  getPointsInfo('邀请用户').then(res => {
    pointsList.value = res.data.list
  })
})
</script>

<style lang="scss" scoped>
@import '../../common/css/project.scss';
@import '../../common/css/index.scss';
.invite {
  background: linear-gradient(140deg, #79b8fa 0%, #459af7 100%);
  &-main {
    width: 88%;
    border-radius: 20rpx;
    .save-qrcode {
      background: linear-gradient(156deg, #459af7 0%, #7db9fb 100%);
      border-radius: 36rpx;
      padding: 10rpx 25rpx;
    }
    &-title {
      font-size: 32rpx;
      margin: 30rpx 0;
      text-align: center;
    }
    &-qrcode {
      min-height: 400rpx;
    }
    .invite-recode {
      margin: 30rpx 0;
      background: linear-gradient(156deg, #459af7 0%, #7db9fb 100%);
      border-radius: 48rpx;
      font-size: 28rpx;
      width: 600rpx;
      height: 96rpx;
    }
  }
}
</style>
