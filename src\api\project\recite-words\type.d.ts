export interface TagsResultType {
  _id: string
  creatorUserId: UserIdData
  creatorTime: number
  type: string
  tags: string
  sort: number
}
export interface BookInfoType {
  _id: string
  creatorUserId: UserIdData
  creatorTime: number
  title: string
  type: string
  tags: string
  owner: '公共' | '个人' // 公共 个人
  cover: UploadImgData[]
}

export interface RecitePlanResultType {
  _id: string
  creatorUserId: UserIdData
  book: string
  creatorTime: number
  word: number
  day: number
  bookName: string
  order: string
}
export interface UserReciteBookType {
  _id: string
  creatorUserId: UserIdData
  creatorTime: number
  bookName: string
  userId: string
  bookId: string
}

export interface ReciteWordCountType {
  totalCount: number
}

export interface CreateRecitePlanType {
  word: number
  bookName: string
  day: number
  order: string
  book: string
}

export interface IUpdateUserReciteBookParams {
  id: string
  bookName: string
  bookId: string
}

export interface IReciteWordDetail {
  _id: string
  creatorUserId: string
  book: string
  tableField102: {
    word: string
    learned: 0 | 1
  }[]
  sign: number
  days: number
  creatorTime: number
  lastModifyTime: number
  lastModifyUserId: string
  learnedWords?: Set<string> // 已学习的单词ID集合
}

export interface IReciteWordInfo {
  _id: string
  wordbookId: string
  word: string
  type?: '熟词' | '生词'
  typeId?: string
  pronunciation: string
  meaning: string[]
}

export interface IInsertWordParams {
  tableField102: {
    word: string
  }[]
  book: string
  sign: number
  days: number
}

export interface IInsertReciteWord {
  book: string
  wordId: string
  type: string
}
export interface IReciteDetailWord extends IInsertWordParams {
  _id: string
}

export interface IReciteWordRandom extends IReciteWordInfo {
  randomList: IReciteWordInfo[]
}

export interface IReciteWordTotal {
  _id: '熟词' | '生词'
  totalCount: number
}

export interface SubmitReciteWordType {
  _id?: string
  practiceNo: string
  type: string
  accuracy: number
  state: '提交' | '保存'
  answerTime: number
  bookID: string
  creatorTime?: number
  tableField106: TableField106[]
}

export interface TestHistoryItem {
  _id: string
  practiceNo: string
  answerTime: number
  accuracy: number
  state: string
  tableField106: TableField106[]
  creatorTime: number
  type: string
  bookID: string
}

interface TableField106 {
  wordId: string
  right: number
  _id?: string
}

export interface IReciteWordError {
  _id: string
  wrongTimes: number
  word: string
}
export interface IAddWord {
  wordbookId: string
  word: string
  meaning: string[]
}
export interface IAddBook {
  title: string
  owner: string
}

export interface INewWordAndFamiliarWord {
  _id: string
  comInputField105: string
  creatorUserId: string
  pronunciation: string
  meaning: string[]
  creatorTime: number
  word: string
  wordbookId: string
  recordId: string
}
