<template>
  <view class="bg-#F4F5F7 min-h-100vh px-2">
    <LoadingPage
      class="h-full"
      empty-text="暂无报告"
      loading-text="加载中..."
      :loading="loading"
      :empty="empty">
      <template #default="{ show }">
        <view v-if="subjectReport.length > 0" class="px-1" :test="show">
          <view class="bg-#459AF7 h-87rpx rounded flex items-center px-2">
            <text class="text-white text-size-36rpx">总体报告</text></view
          >
          <view class="bg-white rounded-xl">
            <view
              v-for="item in subjectReport"
              :key="item"
              class="w-92vw h-140rpx m-auto flex justify-between items-center">
              <view class="flex-1 ml-2">
                <view class="text-size-28rpx">{{ item.name }}</view>
                <view class="text-#999999 text-size-24rpx mt-1">{{
                  formatDate(item.report_time)
                }}</view>
              </view>
              <view class="w160rpx">
                <u-button
                  type="primary"
                  shape="circle"
                  text="查看报告"
                  @click="click(item.data)"></u-button>
              </view>
            </view>
          </view>
        </view>
      </template>
    </LoadingPage>
  </view>
</template>

<script setup lang="ts" name="exam-report-list">
import { getReportListByUser } from '@/api/project/exercises/index'
import LoadingPage from '@/components/LoadingPage.vue'
import useUserStore from '@/store/modules/user'
import { showFailToast } from '@/utils'
import useDateFormatter from '@/hooks/useDateFormatter'
const loading = ref(true)
const empty = ref(false)
const subjectReport = ref(<any>[])
const userStore = useUserStore()
const { formatDate } = useDateFormatter('YYYY-MM-DD')

function click(json: any) {
  uni.navigateTo({
    url: `/subPages/exercises/exam-report-info?data=${encodeURIComponent(JSON.stringify(json))}`,
  })
}

onLoad(async () => {
  if (!userStore.userInfo) {
    showFailToast('请先登录')
    subjectReport.value = []
    return
  }

  // const { data } = await getReportListByUser(userStore.userInfo?.id)
  const { data } = await getReportListByUser('66cc7275eba6ca287480d482')
  loading.value = false
  if (data.list.length === 0) {
    empty.value = true
    return
  }
  subjectReport.value = data.list.filter(t => t.type === 'subject')
})
</script>
