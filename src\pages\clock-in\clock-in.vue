<template>
  <TitleBar
    title="督学打卡"
    text-color="white"
    nav-color="#459AF7"
    :flag="false"
    show-back
    fsize="40rpx" />
  <view class="w-full h160rpx pt60rpx bg-#459AF7 relative">
    <view class="flex justify-around">
      <view>
        <view class="text-34rpx text-white fw-600">已连续打卡{{ clockDay }}天</view>
        <view class="mt20rpx text-24rpx text-white">赶快去打卡今日学习计划吧！</view>
      </view>
      <image
        :src="getSystemImg('/6747e9ed0a34815816f11159/66a31de81f1cb273f7dbe9cc')"
        class="w-200rpx h200rpx"></image>
    </view>
    <!-- 创建打卡任务 -->
    <view class="absolute z-999 w-full top-150rpx">
      <SelectDate
        v-if="selectDateFlag"
        :list="clockRecords.map(item => item.date)"
        @select="selectDay">
        <template #right>
          <view class="text-#333333 fw-bold text-28rpx" @click="toServe('record')">
            打卡记录 >
          </view>
        </template>
        <u-button
          text="创建打卡任务"
          shape="circle"
          :plain="true"
          :custom-style="{
            color: '#459AF7',
            borderColor: '#459AF7',
          }"
          @click="toServe('add-task')"></u-button>
      </SelectDate>
    </view>
  </view>
  <view class="w-full h295rpx"> </view>

  <!-- 今日打卡 -->
  <view class="w690rpx mt20rpx min-h-200rpx rounded-20rpx bg-white box-shadow ma p30rpx box-border">
    <view class="text-28rpx text-#333333 fw-bold">今日打卡任务</view>

    <u-skeleton v-if="todayLoading" :loading="todayLoading" :animate="true" rows="4"></u-skeleton>
    <template v-else>
      <TaskItem
        v-for="item in todayClockTask"
        :key="item._id"
        :task-id="item._id"
        :complete-id="item.complete_id"
        :catalog-id="item.catalog_id"
        :topic-id="item.topic_id"
        :section-id="item.section_id"
        :word-id="item.word_id"
        :itype="item.type"
        :title="item.content"
        :status="item.is_complete !== 0" />
      <u-empty v-if="todayClockTask.length === 0" text="暂无打卡"></u-empty>
    </template>
  </view>
  <!-- 今日已完成 -->
  <view class="w690rpx mt20rpx min-h-200rpx rounded-20rpx bg-white box-shadow ma p30rpx box-border">
    <view class="text-28rpx text-#333333 fw-bold">今日已完成</view>
    <u-skeleton v-if="todayLoading" :loading="todayLoading" :animate="true" rows="3"></u-skeleton>
    <template v-else>
      <CompleteItem
        v-for="item in todayCompleteClockTask"
        :key="item._id"
        :title="item.content"
        :time="item.time" />
      <u-empty v-if="todayCompleteClockTask.length === 0" text="暂无已完成"></u-empty>
    </template>
  </view>
</template>

<script setup lang="ts">
import CompleteItem from './CompleteItem/index.vue'
import TaskItem from './TaskItem/index.vue'
import SelectDate from '@/components/SelectDate.vue'
import TitleBar from '@/components/TitleBar.vue'
import { getStartEndTime, getSystemImg } from '@/utils'
import { getCameraClockInRecordsDataList, getOnlineRecordsDataList } from '@/api/project/clock-in'
import type { IClockInRecords } from '@/api/project/clock-in/type'

// 始末时间
const today = getStartEndTime()

const toServe = (url: string) => {
  uni.navigateTo({
    url: `/subPages/clock-in/${url}`,
  })
}

interface IDistributeBase extends IClockInRecords {
  report_id?: string
  complete_id: string
  section_id: string
  topic_id: string
  catalog_id: string
  grade: string
  subject: string
  word_id: string
}
const clockDay = ref(0)
// 打卡记录列表
const clockRecords = ref<IDistributeBase[]>([])
const selectDateFlag = ref(false)
const nowDay = ref(new Date().setHours(0, 0, 0, 0))
// 选择日期
const selectDay = (val: any) => {
  nowDay.value = val
}
// 今日打卡任务
const todayClockTask = computed<IDistributeBase[]>(() => {
  return clockRecords.value.filter(item => item.date === nowDay.value)
})

const todayCompleteClockTask = computed<IDistributeBase[]>(() => {
  return clockRecords.value.filter(item => item.date === nowDay.value && item.is_complete === 1)
})

const handleContinuousDay = () => {
  const list = clockRecords.value.filter(
    item => new Date(item.date).getDate() < new Date().getDate()
  )
  list.sort((x, y) => x.date - y.date)
  // 连续打卡天数
  let day = 0
  list.forEach((item, index) => {
    if (index !== list.length - 1 && item.date !== list[index + 1].date) day++
    if (index !== 0 && new Date(item.date).getDate() - new Date(list[index - 1].date).getDate() > 1)
      day = 0
    if (index === list.length - 1) day++
    if (item.is_complete === 0) day = 0
  })
  clockDay.value = day
}

const todayLoading = ref(true)
const getData = async () => {
  // clockRecords.value.length = 0
  // clockDay.value = (await getContinuousDay()).data.list.length
  clockRecords.value = (
    await getCameraClockInRecordsDataList(today.startTime, today.endTime)
  ).data.list
  // 获取在线刷题和在线学习记录
  const list = (await getOnlineRecordsDataList(today.startTime, today.endTime)).data
  clockRecords.value.push(...(list as []))
  selectDateFlag.value = true
  todayLoading.value = false
  handleContinuousDay()
}

onShow(() => {
  todayLoading.value = true
  getData()
})
</script>

<style>
page {
  background-color: #f4f5f7;
  padding-bottom: 20rpx;
}
</style>

<style lang="scss" scoped></style>
