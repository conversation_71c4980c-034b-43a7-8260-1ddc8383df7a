import type * as MemberType from './type.d'
import { createModelData, dataInterface, getModelList } from '@/api/visual'
import { post } from '@/config/request'
import type { FilterType } from '@/es/request'

// 获取套餐价格列表
export const getMemberPriceList = () => {
  return getModelList<MemberType.IMemberPriceData>({
    menuId: '675a92ad805f686c4209dade',
    pageSize: -1,
  })
}

// 微信下单接口
export const wxPay = (data: MemberType.WxPayParams) => {
  return post<string>('/api/third/wxPayApi/jsapi', { ...data })
}

// 查询支付状态
export const getPayStatus = (outTradeNo: string) => {
  return post<string>(
    `/api/visualdev/dataAssistant/6747e9ed0a34815816f11159/hooks/6760f6097435b668a82b2619?outTradeNo=${outTradeNo}`
  )
}

// 查询会员列表
export const getMemberList = () => {
  return getModelList<MemberType.IMemberData>({
    menuId: '6760f527805f686c4209daf3',
    pageSize: -1,
    filter: [{ enCode: 'user', method: 'eq', type: 'systemField', value: ['currentUser'] }],
  })
}

// 条件查询积分信息
export const getPointsFilterInfo = () => {
  const currentTime = new Date().getTime()
  const oneHourLater = currentTime + 60 * 60 * 1000
  return getModelList<MemberType.IPointsData>({
    menuId: '67623988805f686c4209db04',
    pageSize: -1,
    connect: 'and',
    filter: [
      { enCode: 'user', method: 'eq', type: 'systemField', value: ['currentUser'] },

      { enCode: 'end_time', method: 'gte', type: 'custom', value: [oneHourLater] },
    ],
  })
}

// 查询积分信息
export const getPointsInfo = (actionName: string) => {
  return getModelList<MemberType.IPointsData>({
    menuId: '67623988805f686c4209db04',
    pageSize: -1,
    connect: 'and',
    userInfoConvert: true,
    filter: [
      { enCode: 'user', method: 'eq', type: 'systemField', value: ['currentUser'] },
      { enCode: 'points_source', method: 'eq', type: 'custom', value: [actionName] },
    ],
  })
}

// 查询积分操作记录列表
export const getPointsRecord = () => {
  return getModelList<MemberType.IPointsRecordData>({
    menuId: '67623658805f686c4209db02',
    pageSize: -1,
    filter: [{ enCode: 'user', method: 'eq', type: 'systemField', value: ['currentUser'] }],
  })
}

// 查询积分操作列表
export const getPointsAction = (name: string) => {
  const filter = []
  if (name) {
    filter.push({ enCode: 'action_name', method: 'eq', type: 'custom', value: [name] })
  }
  return getModelList<MemberType.IPointsActionData>({
    menuId: '676234c8805f686c4209db00',
    pageSize: -1,
    filter: filter as FilterType[],
  })
}

// 积分增扣
export const addOrSubtractPoints = (data: MemberType.IPointsRecordData) => {
  return createModelData({
    menuId: '67623658805f686c4209db02',
    data: JSON.stringify(data),
  })
}

// 新增权益
export const addMemberRight = (data: MemberType.IMemberRightData) => {
  return createModelData({
    menuId: '6766306a805f686c4209db33',
    data: JSON.stringify(data),
  })
}

// 查询权益
export const getMemberRight = (id?: string, type?: string) => {
  const filter = [{ enCode: 'user', method: 'eq', type: 'systemField', value: ['currentUser'] }]
  if (id) {
    filter.push({ enCode: 'equity_id', method: 'eq', type: 'custom', value: [id] })
  }
  if (type) {
    filter.push({ enCode: 'equity_type', method: 'eq', type: 'custom', value: [type] })
  }
  return getModelList<MemberType.IMemberRightData>({
    menuId: '6766306a805f686c4209db33',
    pageSize: -1,
    connect: 'and',
    filter: filter as FilterType[],
  })
}

// 查询订单列表
export const getOrderList = ({ pageSize = 10, currentPage = 1, status = '支付成功' }) => {
  return dataInterface({
    id: '67c00956993df796f90f40e2',
    data: {
      '@search': status,
      '@pageSize': pageSize,
      '@currentPage': currentPage,
    },
  })
}
