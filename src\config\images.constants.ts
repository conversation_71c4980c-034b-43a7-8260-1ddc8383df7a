/**
 ** 图片地址常量
 *
 */

const urls = {
  // 登录背景图
  loginImage: '/api/file/previewImage/6747e9ed0a34815816f11159/6769035af8730075feada0b1',
  // 未登录时默认头像
  noLoginAvatar: '/api/file/previewImage/6747e9ed0a34815816f11159/67690385f8730075feada0b3',
  // 开通按钮箭头图标
  openArrow: '/api/file/previewImage/6747e9ed0a34815816f11159/668ca6820bb07d7cd6ed3590',
  // 我的预约
  myAppointment: '/api/file/previewImage/6747e9ed0a34815816f11159/668ce4040bb07d7cd6ed359c',
  //  升本日程、学习打卡、竞赛、线下课程报名、
  upUniversity: '/api/file/previewImage/6747e9ed0a34815816f11159/668ce4040bb07d7cd6ed359d',
  competition: '/api/file/previewImage/6747e9ed0a34815816f11159/668ce4040bb07d7cd6ed359f',
  offlineCourse: '/api/file/previewImage/6747e9ed0a34815816f11159/668ce4040bb07d7cd6ed35a9',
  onlineConsultation: '/api/file/previewImage/6747e9ed0a34815816f11159/668ce4040bb07d7cd6ed359e',
  onlineCourse: '/api/file/previewImage/678755e7b66b52314c3348f3/678dc73840e03a0b4b6271a6',
  // 刷题记录
  myWrongRecord: '/api/file/previewImage/6747e9ed0a34815816f11159/67ad9dad40e03a0b4b627fb0',
  // 打卡： 拍照打卡、在线学习打卡、刷题打卡
  photo: '/api/file/previewImage/6747e9ed0a34815816f11159/66a31de81f1cb273f7dbe9d1',
  online: '/api/file/previewImage/6747e9ed0a34815816f11159/66a31de81f1cb273f7dbe9c9',
  bruQues: '/api/file/previewImage/6747e9ed0a34815816f11159/66a31de81f1cb273f7dbe9ca',
  // 单词页面
  // goto
  gotoIcon: '/api/file/previewImage/6747e9ed0a34815816f11159/672b0953905da85a4298edd5',
  // 词书选择
  chooseBook: '/api/file/previewImage/6747e9ed0a34815816f11159/672b2a0d905da85a4298ee32',
  // 单词练习
  wordTest: '/api/file/previewImage/6747e9ed0a34815816f11159/672b2a06905da85a4298ee2c',
  // 连词造句
  sentence: '/api/file/previewImage/6747e9ed0a34815816f11159/672b2a02905da85a4298ee2a',
  // ai场景练习
  ai: '/api/file/previewImage/6747e9ed0a34815816f11159/672b2a5b905da85a4298ee36',
  // ai对话
  aiChat: '/api/file/previewImage/6747e9ed0a34815816f11159/67ac433540e03a0b4b627f96',
  // 生词
  strange: '/api/file/previewImage/6747e9ed0a34815816f11159/672b2a00905da85a4298ee28',
  // 熟知词
  know: '/api/file/previewImage/6747e9ed0a34815816f11159/672b2a0a905da85a4298ee30',
  // 我的错词
  myWrongWord: '/api/file/previewImage/6747e9ed0a34815816f11159/672b29f9905da85a4298ee22',
  // 已学
  study: '/api/file/previewImage/6747e9ed0a34815816f11159/672b29fe905da85a4298ee26',
  // 未学
  preStudy: '/api/file/previewImage/6747e9ed0a34815816f11159/672b2a08905da85a4298ee2e',

  // 人人学-AI好学未来

  // '我的'页面 - 折扣券、积分兑换、回馈金、个性组卷、押题直播、专属名师
  coupon: '/api/file/previewImage/687758481807a96b974f9916/6878ae5ccfdce7607d9cc1bc',
  exchange: '/api/file/previewImage/687758481807a96b974f9916/6878ae5ccfdce7607d9cc1c2',
  reward: '/api/file/previewImage/687758481807a96b974f9916/6878ae5ccfdce7607d9cc1bb',
  customPaper: '/api/file/previewImage/687758481807a96b974f9916/6878ae5ccfdce7607d9cc1ba',
  webcast: '/api/file/previewImage/687758481807a96b974f9916/6878ae5ccfdce7607d9cc1bd',
  privateTeacher: '/api/file/previewImage/687758481807a96b974f9916/6878ae5ccfdce7607d9cc1b8',
  // '我的'页面 - 学情报告、升本日程、集训成绩、在线课程、我的收藏、我的错题、学习打卡
  examReport: '/api/file/previewImage/687758481807a96b974f9916/6878b820cfdce7607d9cc20c',
  upUniversitySchedule: '/api/file/previewImage/687758481807a96b974f9916/6878b820cfdce7607d9cc20b',
  examscore: '/api/file/previewImage/687758481807a96b974f9916/6878b820cfdce7607d9cc20a',
  lineCourse: '/api/file/previewImage/687758481807a96b974f9916/6878b820cfdce7607d9cc208',
  myCollection: '/api/file/previewImage/687758481807a96b974f9916/6878b820cfdce7607d9cc209',
  myWrong: '/api/file/previewImage/687758481807a96b974f9916/6878b820cfdce7607d9cc205',
  studyClock: '/api/file/previewImage/687758481807a96b974f9916/6878b820cfdce7607d9cc204',
  // 订单、在线咨询、意见反馈、设置 -
  order: '/api/file/previewImage/6747e9ed0a34815816f11159/67ada95840e03a0b4b627fb5',
  special: '/api/file/previewImage/6747e9ed0a34815816f11159/668ce4040bb07d7cd6ed35a9',
  feedback: '/api/file/previewImage/6747e9ed0a34815816f11159/668ce4040bb07d7cd6ed35a8',
  setting: '/api/file/previewImage/6747e9ed0a34815816f11159/67590502f8730075fead9d67',

  // '人人学'页面 - tab滑块、在线课程、智能同步练、学习打卡
  lineBg: '/api/file/previewImage/68633f121807a96b974f8cd9/6864e73ecfdce7607d9c9806',
  allLearnLineCourse: '/api/file/previewImage/687758481807a96b974f9916/68775fd0cfdce7607d9cbf7c',
  syncTrain: '/api/file/previewImage/687758481807a96b974f9916/68775fd0cfdce7607d9cbf7a',
  allLearnStudyClock: '/api/file/previewImage/687758481807a96b974f9916/68775fd0cfdce7607d9cbf78',
  // '人人学'页面 - 篇目记背、文常记背、专项训练、模拟考试、学习报告、单词训练、名校真题、必备考典、一对一解读
  allLearnArticle: '/api/file/previewImage/687758481807a96b974f9916/68776dbbcfdce7607d9cbfd0',
  allLearnWord: '/api/file/previewImage/687758481807a96b974f9916/68776dbacfdce7607d9cbfcd',
  allLearnSpecial: '/api/file/previewImage/687758481807a96b974f9916/68776dbacfdce7607d9cbfcc',
  allLearnSimulate: '/api/file/previewImage/687758481807a96b974f9916/68776dbacfdce7607d9cbfca',
  allLearnReport: '/api/file/previewImage/687758481807a96b974f9916/68776dbccfdce7607d9cbfd2',
  allLearnWordTrain: '/api/file/previewImage/687758481807a96b974f9916/687db836cfdce7607d9cc7d8',
  allLearnExam: '/api/file/previewImage/687758481807a96b974f9916/687db95ccfdce7607d9cc7de',
  allLearnBook: '/api/file/previewImage/687758481807a96b974f9916/687db95ccfdce7607d9cc7dc',
  allLearnOneToOne: '/api/file/previewImage/687758481807a96b974f9916/687db95ccfdce7607d9cc7da',
  // '人人学'页面 - AI在线问答、错题本
  allLearnAi: '/api/file/previewImage/687758481807a96b974f9916/68776f55cfdce7607d9cbfd6',
  allLearnMyWrong: '/api/file/previewImage/687758481807a96b974f9916/68776f54cfdce7607d9cbfd4',
} as Record<string, string>

// 拼接域名地址
for (const key in urls) {
  urls[key] = `${import.meta.env.VITE_SERVE}${urls[key]}`
}

export default urls
