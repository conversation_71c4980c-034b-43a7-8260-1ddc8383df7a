<template>
  <view class="recite-tabs">
    <u-tabs
      class="w-100%"
      scrollable
      :list="list"
      :key-name="keyName || 'name'"
      :current="current"
      :show-bar="true"
      line-width="58rpx"
      line-height="9rpx"
      :line-color="getLineColor()"
      :active-style="getActiveStyle()"
      :inactive-style="{
        color: '#999999',
      }"
      :item-style="{
        fontSize: '32rpx',
        height: '70rpx',
      }"
      @click="handleClick">
    </u-tabs>
  </view>
</template>

<script setup lang="ts" name="recite-tab">
interface Props {
  // 数据类属性
  list: Array<any> // tab列表数据
  keyName?: string // 显示字段名，默认'name'
  current: number // 当前选中的tab索引

  // 控制类属性
  isCancelled?: boolean // 是否取消选择状态
}

interface Emits {
  (e: 'change', data: { index: number }): void
}

const props = withDefaults(defineProps<Props>(), {
  keyName: 'name',
  isCancelled: false,
})

const emit = defineEmits<Emits>()

// 下划线背景图（固定样式）
const lineBg =
  'https://beta.kindoucloud.com/api/file/previewImage/687758481807a96b974f9916/688058dfcfdce7607d9ccefb'

// 获取tab激活样式
function getActiveStyle() {
  if (props.isCancelled) {
    // 取消状态，使用和inactiveStyle一样的样式
    return {
      color: '#999999',
    }
  }
  // 正常激活状态
  return {
    color: '#459AF7',
    fontWeight: '500',
  }
}

// 获取指示器线条颜色
function getLineColor() {
  if (props.isCancelled) {
    // 取消状态，返回透明色让指示器消失
    return 'transparent 100% 100%'
  }
  // 正常状态，显示背景图
  return `url(${lineBg}) 100% 100%`
}

// 处理tab点击事件
function handleClick(data: { index: number }) {
  // 直接触发change事件（包含所有点击，包括重复点击）
  emit('change', data)
}
</script>

<style lang="scss" scoped>
/* #ifdef H5 */
// H5平台的样式穿透
:deep(.u-tabs__wrapper__nav__line) {
  /* 指示器线条过渡 */
  transition: all 0.3s ease;
  -webkit-transition: all 0.3s ease;
}
:deep(.u-tabs__wrapper__nav__item__text) {
  /* 文字颜色过渡 */
  transition: color 0.3s ease, font-weight 0.3s ease;
  -webkit-transition: color 0.3s ease, font-weight 0.3s ease;
}
/* #endif */
</style>
