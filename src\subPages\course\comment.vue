<template>
  <view class="w690rpx ma mt30rpx min-h-416rpx pa30rpx box-border bg-#ffffff rounded-10rpx">
    <view class="text-28rpx text-#333333"> 综合评价 </view>
    <view class="mt10rpx p-y-20rpx b-b-2rpx b-b-solid b-b-#e5e5e5">
      <up-rate v-model="score" size="14" active-color="#ffd747" gutter="1"></up-rate>
    </view>
    <up-textarea
      v-model="content"
      maxlength="200"
      count
      height="90"
      placeholder="展开说说您的想法"></up-textarea>
  </view>
  <view class="w750rpx absolute bottom-60rpx p80rpx box-border">
    <u-button
      :custom-style="{
        letterSpacing: '10rpx',
      }"
      type="primary"
      shape="circle"
      text="发布"
      @click="toComment"></u-button>
  </view>
</template>

<script setup lang="ts" name="course-comment">
import { addCourseComMentData } from '@/api/project/course/index'
import { showToast } from '@/utils'
// 评价的课程
const courseNo = ref('')
// 评价分数
const score = ref(0)
// 评价内容
const content = ref('')

// 发布
const toComment = () => {
  if (content.value === '' || score.value === 0) {
    showToast('评价内容或者评分都不能为空')
    return false
  }
  const params = {
    course_no: courseNo.value,
    evaluation_score: score.value,
    evaluation_content: content.value,
  }
  addCourseComMentData(params).then(res => {
    if (res.code === 200) {
      showToast('发布成功')
      setTimeout(() => {
        uni.navigateBack()
      }, 500)
    }
  })
}

onLoad((val: any) => {
  courseNo.value = val.no
})
// 发布评价
</script>

<style lang="scss">
page {
  background-color: #f8f9fd;
}
</style>
