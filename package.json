{"name": "uniapp-unocss-uview-starter", "version": "1.0.0", "author": {"name": "xyCoder", "email": "<EMAIL>", "url": "https://gitee.com/i1054959069/uniapp-unocss-uview-starter"}, "scripts": {"dev": "uni -p mp-weixin mode=development", "dev:app": "uni -p app", "dev:custom": "uni -p", "dev:h5": "uni", "dev:h5:ssr": "uni --ssr", "dev:mp-alipay": "uni -p mp-alipay", "dev:mp-baidu": "uni -p mp-baidu", "dev:mp-kuaishou": "uni -p mp-kua<PERSON>ou", "dev:mp-lark": "uni -p mp-lark", "dev:mp-qq": "uni -p mp-qq", "dev:mp-toutiao": "uni -p mp-toutiao mode=development", "dev:mp-weixin": "uni -p mp-weixin mode=development", "dev:quickapp-webview": "uni -p quickapp-webview", "dev:quickapp-webview-huawei": "uni -p quickapp-webview-huawei", "dev:quickapp-webview-union": "uni -p quickapp-webview-union", "build:app": "uni build -p app", "build:custom": "uni build -p", "build:h5": "uni build", "build:h5:ssr": "uni build --ssr", "build:mp-alipay": "uni build -p mp-alipay", "build:mp-baidu": "uni build -p mp-baidu", "build:mp-kuaishou": "uni build -p mp-kuaishou", "build:mp-lark": "uni build -p mp-lark", "build:mp-qq": "uni build -p mp-qq", "build:mp-toutiao": "uni build -p mp-toutiao mode=production", "build:mp-weixin": "uni build -p mp-weixin mode=production", "build:quickapp-webview": "uni build -p quickapp-webview", "build:quickapp-webview-huawei": "uni build -p quickapp-webview-huawei", "build:quickapp-webview-union": "uni build -p quickapp-webview-union", "lint:fix": "eslint . --fix", "format": "prettier . --write --ignore-path .gitignore .eslintignore", "release": "bumpp", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs --fix --ignore-path .gitignore .eslintignore", "commitlint": "commitlint --config commitlint.config.js -e -V", "prepare": "husky install", "test": "echo \"No test specified\" && exit 0"}, "dependencies": {"@dcloudio/uni-app": "3.0.0-alpha-4020320240703001", "@dcloudio/uni-app-harmony": "3.0.0-alpha-4020320240703001", "@dcloudio/uni-app-plus": "3.0.0-alpha-4020320240703001", "@dcloudio/uni-components": "3.0.0-alpha-4020320240703001", "@dcloudio/uni-h5": "3.0.0-alpha-4020320240703001", "@dcloudio/uni-mp-alipay": "3.0.0-alpha-4020320240703001", "@dcloudio/uni-mp-baidu": "3.0.0-alpha-4020320240703001", "@dcloudio/uni-mp-jd": "3.0.0-alpha-4020320240703001", "@dcloudio/uni-mp-kuaishou": "3.0.0-alpha-4020320240703001", "@dcloudio/uni-mp-lark": "3.0.0-alpha-4020320240703001", "@dcloudio/uni-mp-qq": "3.0.0-alpha-4020320240703001", "@dcloudio/uni-mp-toutiao": "3.0.0-alpha-4020320240703001", "@dcloudio/uni-mp-weixin": "3.0.0-alpha-4020320240703001", "@dcloudio/uni-mp-xhs": "3.0.0-alpha-4020320240703001", "@dcloudio/uni-quickapp-webview": "3.0.0-alpha-4020320240703001", "@gowiny/js-utils": "^1.0.7", "@gowiny/uni-router": "^1.0.15", "@gowiny/vue-class": "^1.0.59", "@rojer/katex-mini": "^1.1.3", "axios": "0.26.1", "axios-adapter-uniapp": "^0.1.4", "clipboard": "^2.0.11", "dayjs": "^1.11.11", "katex-uniapp": "^1.0.1", "pinia": "2.0.3", "pinia-plugin-persistedstate": "^3.2.1", "ts-md5": "^1.3.1", "uview-plus": "^3.1.32", "vue": "^3.4.31", "vue-i18n": "^9.13.1"}, "devDependencies": {"@antfu/eslint-config": "^0.34.0", "@commitlint/cli": "^19.3.0", "@commitlint/config-conventional": "^19.2.2", "@dcloudio/types": "^3.4.8", "@dcloudio/uni-automator": "3.0.0-alpha-4020320240703001", "@dcloudio/uni-cli-shared": "3.0.0-alpha-4020320240703001", "@dcloudio/uni-stacktracey": "3.0.0-alpha-4020320240703001", "@dcloudio/vite-plugin-uni": "3.0.0-alpha-4020320240703001", "@types/node": "^18.7.15", "@vue/runtime-core": "^3.4.21", "bumpp": "^9.0.0", "commitlint": "^19.3.0", "eslint": "^8.23.0", "eslint-config-prettier": "^8.5.0", "eslint-plugin-prettier": "^4.2.1", "husky": "^8.0.3", "lint-staged": "^15.2.7", "prettier": "^2.7.1", "pretty-quick": "^4.0.0", "sass": "^1.63.3", "sass-loader": "^10.4.1", "typescript": "^4.9.4", "unocss": "^0.50.0", "unocss-preset-weapp": "^0.4.2", "unplugin-auto-import": "^0.12.1", "vite": "^5.2.8", "vite-plugin-optimize-persist": "^0.1.2", "vite-plugin-vue-setup-extend": "^0.4.0"}, "lint-staged": {"**/*.{js,jsx,ts,tsx,vue}": "eslint --ext .js,.jsx,.ts,.tsx,.vue", "**/*.{js,jsx,tsx,ts,less,md,json}": ["prettier --write", "git add"]}}