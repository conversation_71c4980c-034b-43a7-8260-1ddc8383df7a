export interface BookCourseListItem {
  _id: string
  chapter: string
  creatorUserId: UserIdData
  course_name: string
  school: string
  endDate: number
  locations: string
  startTime: string
  endTime: string
  creatorTime: number
  startDate: number
  timeRange: string
  start_class_date: number
  status: string
}
export interface BookingListItem {
  _id: string
  course_id: string
  creatorTime: number
}

export interface MyBookingListItem {
  _id: string
  chapter: string
  course_id: string
  creatorUserId: string
  course_name: string
  school: string
  locations: string
  startTime: string
  endTime: string
  creatorTime: number
  start_class_date: number
}
