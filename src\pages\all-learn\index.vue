<script lang="ts" setup>
import { computed, reactive, ref, onMounted } from 'vue'
import imagesConstants from '@/config/images.constants'
import useUserStore from '@/store/modules/user'
import { getHeadIcon, showToast } from '@/utils'
// import { getIndexSwiperDataList } from '@/api/project/index'

// 返回胶囊按钮距离屏幕顶部的距离
const capsuleHeight = computed(() => uni.getMenuButtonBoundingClientRect().top)

// 获取用户信息
const userInfo = computed(() => {
  return useUserStore().userInfo
})

// 轮播图列表
const swiperList = reactive([])

// tabs
const tabs = ['语文', '数学', '英语', '专业课']
// 当前tab
const currentTab = ref(0)
// 切换tab
const changeTabs = (tabItem: any) => {
  currentTab.value = tabItem.index
}

// 在线学习图标
const leftIcon = imagesConstants.allLearnLineCourse
// 在线课程右侧
const rightBoxs = [
  {
    title: '智能同步练',
    text: '在线精准训练',
    icon: imagesConstants.syncTrain,
  },
  {
    title: '学习打卡',
    text: '学习习惯养成',
    icon: imagesConstants.allLearnStudyClock,
  },
]

// 学习巩固栏目汇总
const studyListAll = {
  article: {
    title: '篇目记背',
    text: '分篇目记背',
    icon: imagesConstants.allLearnArticle,
  },
  word: {
    title: '文常记背',
    text: '分朝代记背',
    icon: imagesConstants.allLearnWord,
  },
  special: {
    title: '专项训练',
    text: '考纲习题训练',
    icon: imagesConstants.allLearnSpecial,
  },
  simulate: {
    title: '模拟考试',
    text: '备考基础摸底',
    icon: imagesConstants.allLearnSimulate,
  },
  report: {
    title: '学习报告',
    text: '学习效果分析',
    icon: imagesConstants.allLearnReport,
  },
  wordTrain: {
    title: '单词训练',
    text: '提高词汇量',
    icon: imagesConstants.allLearnWordTrain,
  },
  exam: {
    title: '名校真题',
    text: '专业课真题在线练习',
    icon: imagesConstants.allLearnExam,
  },
  book: {
    title: '必备考典',
    text: '轻松背考纲知识点',
    icon: imagesConstants.allLearnBook,
  },
  oneToOne: {
    title: '一对一解读',
    text: '及时解答择校疑难',
    icon: imagesConstants.allLearnOneToOne,
  },
}

// 学习巩固
const studyList = [
  [
    studyListAll.article,
    studyListAll.word,
    studyListAll.special,
    studyListAll.simulate,
    studyListAll.report,
  ],
  [studyListAll.special, studyListAll.simulate, studyListAll.report],
  [studyListAll.wordTrain, studyListAll.special, studyListAll.simulate, studyListAll.report],
  [studyListAll.exam, studyListAll.book, studyListAll.simulate, studyListAll.oneToOne],
]

// 学习工具
const studyTools = [
  {
    title: 'AI在线问答',
    image: imagesConstants.allLearnAi,
    url: '/pages/ranking/ranking',
  },
  {
    title: '错题本',
    image: imagesConstants.allLearnMyWrong,
  },
]

// 跳转
const toServe = (val: any) => {
  if (val.func) {
    val.func()
  } else if (val.url) {
    uni.navigateTo({
      url: val.url,
      fail: () => {
        uni.switchTab({
          url: val.url,
        })
      },
    })
  } else {
    showToast('功能开发中，敬请期待')
  }
}

onLoad(async () => {
  // const res = await getIndexSwiperDataList()
  // console.log(res)
})
</script>

<template>
  <view
    class="w-full min-h-100vh px-20rpx pb-20rpx box-border bg-[#f8f8f8]"
    :style="`padding-top:${capsuleHeight}px`">
    <view class="flex items-center mb-30rpx">
      <up-avatar
        :src="userInfo?.headIcon ? getHeadIcon(userInfo.headIcon) : imagesConstants.noLoginAvatar"
        size="68rpx"
        mode="aspectFit"></up-avatar>
      <text class="text-36rpx font-bold text-[#333333] ml-42rpx">{{
        userInfo?.realName ? userInfo.realName : '请登录'
      }}</text>
    </view>
    <up-swiper :list="swiperList" height="180rpx" indicator indicatorMode="dot" loading></up-swiper>
    <!-- tab-bar区域 -->
    <view
      class="flex items-end justify-between ml-24rpx mr-auto mt-40rpx w-60% color-#999999 text-30 font-600">
      <view
        v-for="(tab, index) in tabs"
        :key="index"
        :class="currentTab === index ? 'tab-selected' : ''"
        @click="currentTab = index">
        <text>{{ tab }}</text>
      </view>
    </view>
    <!-- 在线课程 -->
    <view class="w-full flex mt-30rpx">
      <view class="w-345 h-340 bg-white rounded-20rpx p-30rpx box-border relative">
        <p class="learn-title">在线课程</p>
        <p class="learn-tips">同步考纲，夯实备考基础</p>
        <image :src="leftIcon" class="w-100rpx h-100rpx ml-12rpx mt-16rpx" mode="aspectFit"></image>
        <button class="studyButton flex-center" @click="toServe({})">去学习</button>
      </view>
      <view class="ml-20rpx flex flex-col justify-between">
        <view
          v-for="(item, index) in rightBoxs"
          class="studyRightBox"
          :key="index"
          @click="toServe(item)">
          <view>
            <p class="learn-title text-32rpx">{{ item.title }}</p>
            <p class="learn-tips">{{ item.text }}</p>
          </view>
          <view class="m-y-auto ml-auto">
            <image class="w-68rpx h-68rpx" mode="wi" :src="item.icon"></image>
          </view>
        </view>
      </view>
    </view>
    <!-- 学习巩固 -->
    <view class="w-full rounded-20rpx p20rpx pt-30rpx box-border bg-white mt-20rpx">
      <view class="learn-title mb-20rpx" v-if="currentTab !== 3">学习巩固</view>
      <view class="learn-title mb-20rpx" v-else>学习指引</view>
      <view class="w-full grid grid-cols-2 gap-20rpx">
        <view
          v-for="(item, index) in studyList[currentTab]"
          @click="toServe(item)"
          class="h-150 bg-[#F3F8FF] rounded-20rpx p30rpx box-border flex justify-between"
          :key="index">
          <view>
            <p class="text-30rpx text-[#333333] font-bold">{{ item.title }}</p>
            <p class="learn-tips">{{ item.text }}</p>
          </view>
          <view class="m-y-auto ml-auto">
            <image class="w-60rpx h-60rpx" mode="aspectFit" :src="item.icon"></image>
          </view>
        </view>
      </view>
    </view>
    <!-- 学习工具 -->
    <view class="w-full rounded-20rpx p-x-20rpx p-y-30rpx box-border bg-white mt-20rpx">
      <view class="learn-title mb-40rpx">学习工具</view>
      <view class="grid grid-cols-3 gap-y-40rpx box-border">
        <view
          v-for="(item, index) in studyTools"
          :key="index"
          class="flex flex-col items-center"
          @click="toServe(item)">
          <image class="w-60rpx h-60rpx" :src="item.image" mode="aspectFit"></image>
          <text class="text-30rpx text-[#333333] mt-12rpx">{{ item.title }}</text>
        </view>
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
// tabs
.tab-selected {
  transform: scale(1.33);
  font-weight: bold;
  color: #000;
  position: relative;
}
.tab-selected:after {
  content: '';
  display: block;
  position: absolute;
  bottom: -3rpx;
  z-index: 0;
  width: 62rpx;
  height: 17rpx;
  margin: 0 auto;
  left: 50%;
  transform: translateX(-50%);
  background-image: url('https://beta.kindoucloud.com/api/file/previewImage/68633f121807a96b974f8cd9/6864e73ecfdce7607d9c9806');
  background-repeat: no-repeat;
  background-size: contain;
  background-position: bottom;
}

.learn-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #000000;
}

.learn-tips {
  font-size: 24rpx;
  color: #999999;
  margin-top: 12rpx;
}

.studyButton {
  width: 160rpx;
  height: 62rpx;
  background: linear-gradient(90deg, #4effda 0%, #459af7 100%);
  border-radius: 37rpx;
  color: white;
  font-size: 28rpx;
  margin-top: auto;
  margin-left: auto;
  position: absolute;
  bottom: 30rpx;
  right: 30rpx;
}

.studyRightBox {
  width: 345rpx;
  height: 160rpx;
  background: white;
  border-radius: 20rpx;
  box-sizing: border-box;
  padding-top: 22rpx;
  padding-left: 42rpx;
  padding-right: 40rpx;
  display: flex;
}
</style>
