<template>
  <view class="w-full bg-white overflow-hidden">
    <view class="w690rpx h190rpx ma m-y-20rpx flex items-center b-b-2rpx b-b-#EEEEEE b-b-solid">
      <image
        :src="getSystemImg('/6747e9ed0a34815816f11159/66a0b9370bb07d7cd6ed488d')"
        class="w224rpx h165rpx"></image>
      <view class="flex flex-col ml20rpx justify-center">
        <view class="fwbold text-#333333 text-34rpx">一招搞定《大学语文》</view>
        <view class="w-full h30rpx"> </view>
        <view class="text-#969DAB text-28rpx">总共64课时</view>
      </view>
    </view>
    <view class="w690rpx ma flex m-y-20rpx">
      <u-icon name="clock" size="20"></u-icon>
      <view class="text-28rpx text-#333333 ml20rpx"> 购买后永久有效 </view>
    </view>
  </view>
  <view class="w-full h280rpx m-y-20rpx bg-white overflow-hidden">
    <view class="w690rpx ma">
      <view class="w-full flex justify-between m-y-40rpx">
        <view class="text-#333333 text-32rpx">课程价格</view>
        <view class="text-#333333 text-32rpx fw-bold">￥998</view>
      </view>
      <view class="w-full flex justify-between m-y-40rpx">
        <view class="text-#333333 text-32rpx">优惠卷</view>
        <view class="text-#969DAB text-30rpx fw-bold"> 无可用优惠卷&nbsp;></view>
      </view>
      <view class="w-full h-2rpx bg-#EEEEEE"> </view>
      <view class="w-full flex justify-end mt20rpx">
        <view class="text-28rpx text-#666666">实际金额：</view>
        <view class="text-#FF776A text-28rpx fw-bold ml10rpx">￥998</view>
      </view>
    </view>
  </view>
  <view class="w-full h240rpx m-y-20rpx bg-white overflow-hidden">
    <view class="w690rpx ma flex flex-col justify-evenly">
      <u-radio-group v-model="radio" icon-placement="right">
        <view class="w-full h-120rpx flex items-center b-b-2rpx b-b-#EEEEEE b-b-solid">
          <image
            :src="getSystemImg('/6747e9ed0a34815816f11159/66a0cab11f1cb273f7dbe81f')"
            class="w60rpx h60rpx"></image>
          <view class="flex-1 ml20rpx">
            <u-radio
              name="微信支付"
              size="24"
              label="微信支付"
              label-size="34rpx"
              icon-size="20"></u-radio>
          </view>
        </view>
        <view class="w-full h-120rpx flex items-center">
          <image
            :src="getSystemImg('/6747e9ed0a34815816f11159/66a0cab11f1cb273f7dbe81e')"
            class="w60rpx h60rpx"></image>
          <view class="flex-1 ml20rpx">
            <u-radio
              name="支付宝"
              size="24"
              label="支付宝"
              label-size="34rpx"
              icon-size="20"></u-radio>
          </view>
        </view>
      </u-radio-group>
    </view>
  </view>
  <u-tabbar>
    <view class="w-full flex items-center p-x-20rpx justify-between">
      <view class="flex w-60%">
        <view class="text-28rpx text-#666666">实际金额：</view>
        <view class="text-#FF776A text-28rpx fw-bold ml10rpx">￥998</view>
      </view>
      <u-button
        :custom-style="{
          marginLeft: '20rpx',
          padding: '20rpx',
          letterSpacing: '10rpx',
        }"
        type="primary"
        shape="circle"
        text="确认支付"
        @click="confirmPay"></u-button>
    </view>
  </u-tabbar>
</template>

<script setup lang="ts" name="course-confirmorder">
import { getSystemImg } from '@/utils'

const courseId = ref()

onLoad(async (val: any) => {
  courseId.value = val.id
})

const radio = ref('微信支付')

// 确认支付
const confirmPay = () => {
  uni.navigateTo({
    url: `/subPages/course/paysuccess?id=${courseId.value}`,
  })
}
</script>

<style lang="scss">
page {
  background-color: #f4f5f7;
}
</style>
