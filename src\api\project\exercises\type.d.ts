// 章节测试，模拟考试的分类
export interface TestingItemData {
  _id: string
  creatorUserId: UserIdData
  subject: string
  grade: string
  tableField106: PaperItem[]
  creatorTime: number
}
// 卷组
export interface PaperItem {
  testPaperId: string
  duration: string
  name: string
  _id: string
}
// 题目详情
export interface TopicItem {
  _id: string
  chapter: string[]
  creatorUserId: UserIdData
  subject: string
  title: string
  type: string
  analysis: string
  knowledgePoints: string[]
  answer: string[]
  grade: string
  tableField116: any[]
  creatorTime: number
  keyword: string
  option: string[]
  collect: string
  state: string
  testType: string
  user_answer: string[]
}
// 年级
export interface Grades {
  _id: string
  creatorUserId: string
  grade: string
  creatorTime: number
  tableField107: Subjects[]
  lastModifyTime: number
  lastModifyUserId: string
}
// 科目
export interface Subjects {
  subject: string
  type: string[]
}
export interface CreateResultCardType {
  data: TopicResultType
  fid: string
  answerTime: string
  type: string
  state: string
  totalScore: number
  accuracy: number
  tableField112: TopicResultType[]
}
// 子表
export interface TopicResultType {
  _id?: string
  topicId: string
  index?: number
  userAnswer: string
  score?: number
}
// 天数和正确率
export interface DaysAndAccuracy {
  totalDays: number
  accuracy: number
}
// 我的收藏list
export interface CollectListItem {
  _id: string
  total: number
  ids: string[]
}
export interface TestHistoryItem {
  _id: string
  fid: string
  userId: string
  creatorUserId: UserIdData
  batchNo: string
  answerTime: string
  tableField112: TopicResultType[]
  accuracy: number
  state: string
  creatorTime: number
  type: string
  totalScore: number
}
export interface ToDaysInfo {
  totalResults: number
  rightResults: number
  answerRate: number
}
export interface DefeatRate {
  answerTotalPercentage: number
  answerRatePercentage: number
}
// 报告item
interface ReportItem {
  _id: string
  chapter: string[]
  subject: string
  title: string
  type: string
  analysis: string
  knowledgePoints: string[]
  answer: string[]
  grade: string
  tableField116: any[]
  keyword: string
  option: string[]
  answerTime: string
  submitTime: number
  user_answer: string
  user_score: number
}

// 题目明细
export interface TopicDetail {
  _id: string
  creatorUserId: UserIdData
  name: string
  tableField103: {
    score: number
    questionId: string
    _id: string
  }[]
  creatorTime: number
}

// 简答题答案的数据类型
export interface ShortAnswer {
  topicId: string
  questionId: number // 题号
  questionType: string // 题目类型
  answerText: string // 用户输入的文本
  answerHtml?: string // 用户输入的 HTML 内容
  creatorUserId: string // 创建者用户ID
  grade: string // 一级分类
  subject: string // 二级分类科目
}

// 交卷的数据类型
export interface CardSubmitData {
  teamEnCode?: string // 团队编码
  enCode?: string // 编码
  competitionId?: string // 竞赛ID
  tableField104: ScoreItemType[] // 评分项数组
  totalScore: number // 总分
  right_items: number // 正确项数
  error_items: number // 错误项数
  accuracy: number // 准确率
}

interface ScoreItemType {
  scoreItem: string // 评分条目的唯一标识符
  score: number // 分数
  userAnswer: string // 用户的答案
}

// 单词学习报告

export interface SignMissionItem {
  _id: string
  task_name: string
  user_id: string
  user_name: string
  sum_date: string
  task_date: string
  task_id: string
  report_id: string
  is_commit: string
  creatorTime: number
}
export interface SelectedExerciseItem {
  _id: string
  cover: UploadImgData[]
  topicId: string
  subject: string
  price: number
  grade: string
  module: string
  topicName: string
  creatorTime: number
  tags: string
}

// 错题统计
export interface WrongCountBySubject {
  _id: string // 科目名称
  total: number // 错题数量
}
// 错题对象
export interface WrongExercises {
  _id: string
  chapter: string[]
  creatorUserId: string
  subject: string
  title: string
  type: string
  analysis: string
  knowledgePoints: string[]
  answer: string[]
  grade: string
  creatorTime: number
  keyword: string
  option: string[]
}
