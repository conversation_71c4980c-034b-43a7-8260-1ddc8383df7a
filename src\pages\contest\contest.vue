<template>
  <view class="bg-#f4f5f7">
    <!-- 头部区域 -->
    <view class="w-750rpx h-427rpx bg-#459AF7 flex flex-col-reverse pb-10rpx relative">
      <view class="flex flex-center">
        <view class="pb-65rpx mr-80rpx">
          <view class="text-#FFFFFF text-60rpx bold"> 竞赛角逐场 </view>
          <view class="text-#FFFFFF text-24rpx mt-10rpx"> 与全球开发者竞逐比分，挑战排名 </view>
          <view class="flex mt-16rpx">
            <button class="w167rpx h52rpx text-24rpx lh-52rpx text-#459AF7 ml-0 rounded-26rpx">
              我要办赛
            </button>
          </view>
        </view>
        <view class="">
          <image
            src="https://kindoucloud.com/api/file/previewImage/6694ec5cc523aa5a70e0a30f/67340bb4905da85a4299152e"
            class="w-216rpx h-216rpx"></image>
        </view>
      </view>
    </view>
    <!-- 比赛信息 -->
    <view class="transition-transform transform translate-y-[-30rpx]">
      <view class="flex flex-center w-full">
        <Search
          width="620rpx"
          icon-position="prefix"
          placeholder="搜索关键词，回车搜索"
          @search="handleSearch"
          @input="handleInput"></Search>
      </view>
      <!-- 比赛信息区域 -->
      <view
        v-for="contest in contestList"
        :key="contest._id"
        class="w-690rpx p-30rpx bg-#ffffff box-border mx-auto mt-24rpx shadow rounded-10rpx">
        <view class="flex">
          <view>
            <image :src="assembleImgData(contest.image[0])" class="w-160rpx h-116rpx"></image>
          </view>
          <view>
            <view class="u-line-1 bold text-28rpx">{{ contest.title }}</view>
            <view class="mt-18rpx flex">
              <view
                class="w-auto h-40rpx bg-#f7e7cf text-#FCB138 text-24rpx text-center lh-40rpx mr-20rpx">
                进行中
              </view>
              <view class="w-auto h-40rpx bg-#d2e3f6 text-#459AF7 text-24rpx text-center lh-40rpx">
                {{ contest.tableField118[0]?.type }}
              </view>
            </view>
          </view>
        </view>
        <view class="mt-15rpx text-#999999 text-24rpx lh-38rpx u-line-4">
          {{ contest.info }}
        </view>
        <view class="mt-13rpx flex justify-between">
          <view class="flex">
            <view
              v-for="tag in contest.tags"
              :key="tag"
              class="w-auto p-x-10rpx h45rpx text-#666666 text-24rpx bg-#f4f4f4 text-center rounded-22rpx lh-45rpx">
              {{ tag }}
            </view>
          </view>
          <view class="text-#459AF7 text-24rpx lh-45rpx"> 奖励：118,000 </view>
        </view>
        <view class="text-#999999 text-24rpx mt-21rpx">
          竞赛时间: 长期有效 {{ contest.organizer }}
        </view>
        <view>
          <TopicCard :items="contest.tableField118"></TopicCard>
        </view>
      </view>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { onMounted, ref } from 'vue'
import TopicCard from './components/TopicCard.vue'
import type * as VisualType from '@/api/project/contest/type'
import Search from '@/components/Search.vue'
import { getContestList, getContestListByKeyword } from '@/api/project/contest'
import { assembleImgData, showToast } from '@/utils'

// VisualType.ContestListType是从这个@/api/contest/type ts文件中导入的接口类型
type ContestListType = VisualType.ContestListType[] // 定义 contestList 的类型
const contestList = ref<ContestListType>([])

// 用户输入的搜索关键词
const searchKeyword = ref('')

async function handleSearch(value: string) {
  try {
    const result = await getContestListByKeyword(searchKeyword.value) // 调用异步请求方法

    // 判断搜索结果是否为空
    if (!Array.isArray(result.data.list) || result.data.list.length === 0) {
      showToast('没有找到相关的竞赛') // 调用封装好的提示方法
      contestList.value = result.data.list
    } else {
      contestList.value = result.data.list // 将返回的结果赋值给 contestList
    }
  } catch (error) {
    showToast('没有找到相关的竞赛') // 处理错误
  }
}

async function fetchAllContests() {
  try {
    const response = await getContestList()
    contestList.value = response.data.list
  } catch (error) {
    console.error('获取全部数据失败:', error) // 处理错误
  }
}

function handleInput(value: string) {
  searchKeyword.value = value // 更新输入的值
  // console.log('当前输入:', value); // 打印当前输入的值
  if (searchKeyword.value === '') {
    // console.log('搜索关键词为空，重新请求全部数据');
    fetchAllContests() // 清空搜索框时重新请求全部数据
  }
  // else {
  //     handleSearch(searchKeyword.value); // 有输入时进行搜索
  // }
}

// 在组件挂载时请求数据并处理
onMounted(async () => {
  await fetchAllContests() // 初始请求全部数据
})
</script>

<style>
page {
  background: #f4f5f7;
}
</style>

<style scoped lang="scss"></style>
