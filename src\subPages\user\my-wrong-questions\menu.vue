<template>
  <view class="p-30rpx bg-#f5f5f5 min-h-100vh">
    <view class="grid grid-cols-3 gap-30rpx">
      <view
        v-for="(subject, index) in subjectList"
        :key="index"
        class="bg-white rounded-20rpx w223rpx h250rpx flex flex-col justify-center items-center shadow-sm transition-all-300 active:scale-95 active:shadow-md"
        @click="goToSubject(subject)">
        <image
          :src="getSystemImg(subject.iconId)"
          class="w-70rpx h-70rpx mb-24rpx"
          mode="aspectFit" />
        <view class="text-30rpx color-#333 font-bold mb-8rpx text-center">{{ subject.name }}</view>
        <view class="text-26rpx color-#999 text-center">({{ subject.count }}题)</view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts" name="my-wrong-questions-menu">
import { getSystemImg } from '@/utils'
import { getWrongCountBySubject } from '@/api/project/exercises'
import type { WrongCountBySubject } from '@/api/project/exercises/type'

interface SubjectItem {
  name: string
  iconId: string
  count: number
}

// 基础科目配置（静态数据，不包含count）
const baseSubjects = [
  { name: '数学', iconId: '687758481807a96b974f9916/687dbc2bcfdce7607d9cc7e0' },
  { name: '语文', iconId: '687758481807a96b974f9916/687de3a7cfdce7607d9cc861' },
  { name: '英语', iconId: '687758481807a96b974f9916/687de3afcfdce7607d9cc865' },
  { name: '物理', iconId: '687758481807a96b974f9916/687de3a2cfdce7607d9cc85f' },
  { name: '化学', iconId: '687758481807a96b974f9916/687de39ecfdce7607d9cc85d' },
  { name: '生物', iconId: '687758481807a96b974f9916/687de368cfdce7607d9cc857' },
  { name: '地理', iconId: '687758481807a96b974f9916/687de372cfdce7607d9cc859' },
  { name: '历史', iconId: '687758481807a96b974f9916/687de374cfdce7607d9cc85b' },
  { name: '品德与社会', iconId: '687758481807a96b974f9916/687de365cfdce7607d9cc855' },
  { name: '道德与法治', iconId: '687758481807a96b974f9916/687de35fcfdce7607d9cc851' },
  { name: '科学', iconId: '687758481807a96b974f9916/687de362cfdce7607d9cc853' },
  { name: '体育', iconId: '687758481807a96b974f9916/687de3accfdce7607d9cc863' },
  { name: '音乐', iconId: '687758481807a96b974f9916/687de3b2cfdce7607d9cc867' },
  { name: '政治', iconId: '687758481807a96b974f9916/687de35acfdce7607d9cc84d' },
  { name: '信息技术', iconId: '687758481807a96b974f9916/687de35dcfdce7607d9cc84f' },
  { name: '其他', iconId: '687758481807a96b974f9916/687de356cfdce7607d9cc84b' },
] as const

// 错题统计Map，用于查找
const wrongCountsMap = ref<Map<string, number>>(new Map())

// 计算属性：动态生成带count的科目列表
const subjectList = computed<SubjectItem[]>(() => {
  return baseSubjects.map(subject => ({
    ...subject,
    count: wrongCountsMap.value.get(subject.name) || 0,
  }))
})

function goToSubject(subject: SubjectItem) {
  uni.navigateTo({
    url: `/subPages/user/my-wrong-questions/list?subject=${subject.name}`,
  })
}

// 获取错题统计数据
async function fetchWrongQuestionCounts() {
  try {
    const response = await getWrongCountBySubject()
    console.log(response)

    const wrongCounts: WrongCountBySubject[] = response.data || []

    // 清空并重新填充Map
    wrongCountsMap.value.clear()
    wrongCounts.forEach(item => {
      wrongCountsMap.value.set(item._id, item.total)
    })
  } catch (error) {
    console.error('获取错题统计失败:', error)
  }
}

onMounted(async () => {
  await fetchWrongQuestionCounts()
})
</script>
