<template>
  <view
    class="mt-20rpx w-690rpx pl-20rpx pr-10rpx shadow m-auto m-x-auto rounded-10rpx"
    @click="handleClick">
    <!-- 书籍标题行 -->
    <view class="flex items-center h-90rpx box-border">
      <view class="flex items-end flex-1">
        <text class="book-title mr-23rpx" :style="{ color: isSelected ? '#459AF7' : '#333333' }">
          {{ bookInfo.title }}
        </text>
        <text
          class="book-count ml-10rpx text-24rpx"
          :style="{ color: isSelected ? '#459AF7' : '#999999' }">
          {{ wordCount || 0 }}
        </text>
      </view>

      <!-- 词书操作图标 -->
      <view class="w50 text-center" @tap.stop="handleAction">
        <image
          class="w-6rpx h26rpx"
          :src="getSystemImg('687758481807a96b974f9916/688353f8cfdce7607d9cd8ed')"
          mode="scaleToFill" />
      </view>
    </view>
    <!-- 进度条 -->
    <view class="pb-20rpx">
      <u-line-progress
        :percentage="progress"
        height="8rpx"
        active-color="#459AF7"
        :showText="false"
        inactive-color="rgba(69, 154, 247, 0.10)" />
    </view>
  </view>
</template>

<script setup lang="ts" name="private-book-item">
import { getSystemImg } from '@/utils'
import type { BookInfoType } from '@/api/project/recite-words/type'

interface Props {
  bookInfo: BookInfoType
  isSelected?: boolean
  wordCount?: number | string
  progress?: number
}

interface Emits {
  (e: 'click', book: BookInfoType): void
  (e: 'action', book: BookInfoType): void
}

const props = withDefaults(defineProps<Props>(), {
  isSelected: false,
  wordCount: 0,
  progress: 0,
})

const emit = defineEmits<Emits>()

function handleClick() {
  emit('click', props.bookInfo)
}

function handleAction() {
  emit('action', props.bookInfo)
}
</script>

<style lang="scss" scoped>
.book-title {
  font-size: 32rpx;
  font-weight: 500;
}

.book-count {
  font-size: 24rpx;
}
</style>
