<template>
  <view
    v-if="showDropdown"
    class="w-100vw h-100vh absolute top-0 left-0 z-997"
    @click="showDropdown = false"></view>
  <view class="w-100vw min-h-100vh bg-gradient-to-br from-#FFFFFF to-#F4F5F7">
    <!-- 头部区域 -->
    <view class="relative">
      <image
        class="w100%"
        :src="getSystemImg('687758481807a96b974f9916/68873888cfdce7607d9cde39')"
        mode="aspectFill" />
    </view>

    <!-- 搜索区域 -->
    <view class="mx-20rpx relative top-[-55rpx] bg-white rounded-20rpx px-55rpx pt-75rpx pb-91rpx">
      <view class="text-26rpx text-#333333 mb-15rpx"> 请输入专业名称 </view>

      <view class="relative">
        <u-input
          v-model="searchValue"
          font-size="28rpx"
          placeholder="计算机与科学"
          :placeholder-style="{
            color: '#999999',
            fontSize: '26rpx',
          }">
          <template #suffix>
            <u-icon name="search" size="40rpx" color="#999999" />
          </template>
        </u-input>
      </view>

      <!-- 查询按钮 -->
      <view class="mt-40rpx">
        <u-button
          text="统招专升本查询"
          type="primary"
          size="large"
          :custom-style="{
            background: '#459AF7',
            border: 'none',
            height: '94rpx',
            fontSize: '34rpx',
          }"
          @click="handleQuery">
        </u-button>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts" name="major-query">
import type LoadMoreList from '@/components/LoadMoreList.vue'
import { getSystemImg } from '@/utils'

// 搜索相关状态
const searchValue = ref('')
const showDropdown = ref(false)
const loadMoreList = ref<InstanceType<typeof LoadMoreList>>()

// 处理查询按钮点击
const handleQuery = () => {
  if (!searchValue.value.trim()) {
    uni.showToast({
      title: '请输入专业名称',
      icon: 'none',
    })
    return
  }

  // 跳转到专业列表页面
  uni.navigateTo({
    url: `/subPages/index/major/list?major_name=${encodeURIComponent(searchValue.value)}`,
  })
}

// 页面加载时的初始化
onLoad(() => {
  uni.setNavigationBarTitle({
    title: '专业查询',
  })
})
</script>
