<template>
  <view class="bg-white">
    <view class="grid grid-cols-2 gap-2">
      <view
        v-for="(item, index) in gridList"
        :key="index"
        class="grid-item"
        :style="`background-color: ${item.bgColor}`">
        <view class="ml20rpx">
          <image :src="item.url" class="w52rpx h52rpx"></image>
        </view>

        <view class="flex flex-col ml10rpx">
          <text class="text-size-32rpx" :style="index === 0 ? 'color: white' : ''">{{
            item.title
          }}</text>
          <text class="c-#8d95a9 text-size-xs">{{ item.desc }}</text>
        </view>
      </view>
    </view>
    <view class="flex flex-col">
      <span class="text-center text-size-32rpx text-#333333 text-bold">总体分析</span>
      <span class="text-size-24rpx text-#777C8B mt-10rpx p-2">
        {{ reportJson.user_info[0].user_name }}同学，{{
          reportJson.knowledge[0].subject
        }}学科综合知识点掌握已达{{
          reportJson.knowledge[0].knowledge_rate
        }}%，希望接下来你能突破⾃我，继续努⼒提⾼成绩。目前全校平均掌握度为{{
          reportJson.knowledge[0].avg_knowledge_rate
        }}，而最高纪录为{{ reportJson.knowledge[0].max_knowledge_rate }}%
      </span>
      <span class="text-center text-size-26rpx text-#333333 text-bold mt-10rpx">【成绩分布】</span>
      <view class="charts-box">
        <qiun type="column" :chart-data="chartData" :opts="opts"></qiun>
      </view>
    </view>

    <view class="h-88rpx w-full bg-#F2F5F8 flex items-center px-1">
      <u-link
        text="错题本"
        font-size="18"
        :under-line="true"
        @click="click('wrong_question_book')"></u-link
    ></view>
    <view class="mt-2 h-88rpx w-full bg-#F2F5F8 flex items-center px-1">
      <u-link
        text="个性化推荐习题"
        font-size="18"
        :under-line="true"
        @click="click('recommend_question_book')"></u-link
    ></view>
  </view>
</template>

<script setup lang="ts" name="report-info">
import qiun from './components/Charts/qiun.vue'
import { getTopicListByIds } from '@/api/project/exercises'
import useExercisesStore from '@/store/modules/exercises'
const useExercises = useExercisesStore()

const gridList = ref([
  {
    url: 'https://kindoucloud.com/api/file/previewImage/6747e9ed0a34815816f11159/66f525c64dfd0e1c49e05658',
    title: '张三',
    desc: '',
    bgColor: '#459AF7',
  },
  {
    url: 'https://kindoucloud.com/api/file/previewImage/6747e9ed0a34815816f11159/66f525c64dfd0e1c49e05659',
    title: '101',
    desc: '总题数量',
    bgColor: '#FDF2EC',
  },
  {
    url: 'https://kindoucloud.com/api/file/previewImage/6747e9ed0a34815816f11159/66f525c64dfd0e1c49e0565a',
    title: '91',
    desc: '错误数量',
    bgColor: '#F2F5FE',
  },
  {
    url: 'https://kindoucloud.com/api/file/previewImage/6747e9ed0a34815816f11159/66f525c64dfd0e1c49e0565b',
    title: '99.69%',
    desc: '准确率',
    bgColor: '#F1FFFB',
  },
])
const reportJson = ref(<any>{})
const chartData = ref([])
const opts = ref({})
onLoad((e: any) => {
  const json = JSON.parse(decodeURIComponent(e.data))

  reportJson.value = json
  gridList.value[0].title = json.user_info[0].user_name
  gridList.value[1].title = json.question[0].question_count
  gridList.value[2].title = json.question[0].wrong_count
  gridList.value[3].title = json.question[0].user_acc

  const categories = json.school_acc.map((e: any) => e.user_acc_rate)
  const data = json.school_acc.map((e: any) => e.user_acc_rate_num)
  setTimeout(() => {
    const res = {
      categories,
      series: [
        {
          name: '人数',
          data,
          color: '#a1ccfb',
        },
      ],
    }
    opts.value = {
      type: 'column',
      xAxis: {
        rotateLabel: true,
        rotateAngle: -45,
        fontSize: 10,
        marginTop: 6,
      },
      legend: {
        show: false,
      },
    }
    chartData.value = JSON.parse(JSON.stringify(res))
  }, 500)
})
async function click(key: string) {
  const keyObj = reportJson.value[key]
  const questionIds = keyObj.map((e: any) => e.question_id)

  const result = await getTopicListByIds(questionIds)
  const topicList = result.data.list
  topicList.forEach((e: any) => {
    const topic = keyObj.find((item: any) => item._id === e.question_id)
    e.rate = topic.rate
  })

  useExercises.setListData(topicList)
  if (key === 'wrong_question_book') {
    uni.navigateTo({ url: '/subPages/exercises/topic-transform?mode=open&title=错题分析' })
  } else {
    uni.navigateTo({ url: '/subPages/exercises/topic-transform?mode=open&title=个性化推荐习题' })
  }
}
</script>

<style lang="scss" scoped>
.grid-item {
  height: 120rpx;
  display: flex;
  align-items: center;
}
.charts-box {
  width: 100%;
  height: 300px;
}
</style>
