<template>
  <view>
    <view class="wrap">
      <view class="u-tabs-box">
        <up-tabs
          ref="tabs"
          active-color="#f29100"
          :list="list"
          :current="currentTab"
          :scrollable="false"
          :active-style="{
            color: '#4d9ef7',
            fontWeight: 'bold',
            transform: 'scale(1.05)',
          }"
          :inactive-style="{
            color: '#333333',
            transform: 'scale(1)',
          }"
          swiper-width="750"
          @change="change"></up-tabs>
      </view>
      <swiper
        class="swiper-box"
        :current="swiperCurrent"
        @transition="transition"
        @animationfinish="onAnimationFinish">
        <swiper-item class="swiper-item">
          <scroll-view scroll-y style="height: 100%; width: 100%">
            <view class="p-30rpx">
              <Introduction :list="introductionList"></Introduction>
            </view>
          </scroll-view>
        </swiper-item>
        <swiper-item class="swiper-item">
          <scroll-view scroll-y style="height: 100%; width: 100%" @scrolltolower="reachBottom">
            <view class="p-30rpx">
              <DataView></DataView>
            </view>
          </scroll-view>
        </swiper-item>
        <swiper-item class="swiper-item">
          <scroll-view scroll-y style="height: 100%; width: 100%" @scrolltolower="reachBottom">
            <view class="p-30rpx">
              <TeamInfo></TeamInfo>
              <!-- <up-loadmore :status="loadStatus[3]" bg-color="#f2f2f2"></up-loadmore> -->
            </view>
          </scroll-view>
        </swiper-item>
        <swiper-item class="swiper-item">
          <scroll-view scroll-y style="height: 100%; width: 100%" @scrolltolower="reachBottom">
            <view class="p-30rpx">
              <RaceDynamic></RaceDynamic>
              <!-- <up-loadmore :status="loadStatus[3]" bg-color="#f2f2f2"></up-loadmore> -->
            </view>
          </scroll-view>
        </swiper-item>
        <swiper-item class="swiper-item">
          <scroll-view scroll-y style="height: 100%; width: 100%" @scrolltolower="reachBottom">
            <view class="p-30rpx">
              <Introduction :list="questionList"></Introduction>
            </view>
          </scroll-view>
        </swiper-item>
      </swiper>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import Introduction from './Introduction.vue'
import DataView from './DataView.vue'
import TeamInfo from './TeamInfo.vue'
import RaceDynamic from './RaceDynamic.vue'
const list = ref([
  {
    name: '介绍',
  },
  {
    name: '数据',
  },
  {
    name: '团队',
  },
  {
    name: '赛事动态',
  },
  {
    name: '常见问题',
  },
])
// 当前选中的tab
const currentTab = ref(0)
// 当前选中的swiper
const swiperCurrent = ref(0)
// 加载状态
const loadStatus = ref(['loadmore', 'loadmore', 'loadmore', 'loadmore'])

// 介绍
const introductionList = ref([
  {
    title: '任务说明',
    content:
      '5G将给人们的生活环境带来翻天覆地的变化，一方面通过高带宽提升普通用户的上网体验，另一方面5G的低时延、高可靠、大连接特性，可以赋能各个行业，垂直行业将成为运营商新的收入增长点。客户是一个企业最宝贵的财富，但是，5G前夕，存量用户已经被挖潜殆尽，流量增量不增收的处境愈发尴尬。因此预测可能流失的用户',
  },
  {
    title: '任务目标',
    content:
      '5G将给人们的生活环境带来翻天覆地的变化，一方面通过高带宽提升普通用户的上网体验，另一方面5G的低时延、高可靠、大连接特性，可以赋能各个行业，垂直行业将成为运营商新的收入增长点。客户是一个企业最宝贵的财富，但是，5G前夕，存量用户已经被挖潜殆尽，流量增量不增收的处境愈发尴尬。因此预测可能流失的用户',
  },
  {
    title: '任务规则及提交说明',
    content:
      '5G将给人们的生活环境带来翻天覆地的变化，一方面通过高带宽提升普通用户的上网体验，另一方面5G的低时延、高可靠、大连接特性，可以赋能各个行业，垂直行业将成为运营商新的收入增长点。客户是一个企业最宝贵的财富，但是，5G前夕，存量用户已经被挖潜殆尽，流量增量不增收的处境愈发尴尬。因此预测可能流失的用户',
  },
  {
    title: '注意事项',
    content:
      '参赛者在分析数据和构建模型时，需遵循国家法律法规、行业规定和道德规范，不得侵犯他人知识产权、隐私权等权益；参赛者在比赛结束后，需同意将优秀作品和解决方案分享给其他参赛者，以便于大家共同学习和进步；严禁使用任何作弊手段，如发现作弊行为，将取消比赛资格；组织者保留对本次比赛的最终解释权。',
  },
])
// 常见问题
const questionList = ref([
  {
    title: '比赛结果的提交格式是什么？',
    content: '“提交结果”页面会对提交格式要求进行说明，选手还可以参考“文件示例”。',
  },
  {
    title: '数据在哪里下载？',
    content: '在竞赛页面点击“报名参赛”，参赛成功后出现“下载数据”，根据提示进行下载操作即可。',
  },
  {
    title: '如何参赛？',
    content:
      '1、报名参赛：选手选择竞赛，点击“报名参赛”完成注册或者登录即可报名成功。选手可以单人建队参赛，也可以与他人组队参赛。' +
      '\r2、提交结果：报名后选手“下载数据”，按照比赛要求进行数据处理，根据要求“提交结果”并获得分数。' +
      '\n3、赛后获奖：比赛结束后官方工作人员会在竞赛圈公布获奖队伍名单，并联系获奖选手。' +
      '\n比赛过程中请仔细阅读赛程安排、参赛规则、赛题数据说明等竞赛信息，如有疑问请联系竞赛工作人员。',
  },
  {
    title: '注意事项',
    content:
      '参赛者在分析数据和构建模型时，需遵循国家法律法规、行业规定和道德规范，不得侵犯他人知识产权、隐私权等权益；参赛者在比赛结束后，需同意将优秀作品和解决方案分享给其他参赛者，以便于大家共同学习和进步；严禁使用任何作弊手段，如发现作弊行为，将取消比赛资格；组织者保留对本次比赛的最终解释权。',
  },
])

function change(e: { index: number }) {
  swiperCurrent.value = e.index
}
function transition() {
  // this.$refs.tabs.setDx(dx);
}
// 动画完成
function onAnimationFinish({ detail }: { detail: { current: number } }) {
  swiperCurrent.value = detail.current
  currentTab.value = detail.current
}
function reachBottom() {
  console.log('触底')
}
</script>

<style lang="scss" scoped>
.wrap {
  background-color: #ffffff;
  margin-top: 31rpx;
  display: flex;
  flex-direction: column;
  height: calc(100vh - var(--window-top));
  width: 100%;
}
.swiper-box {
  flex: 1;
}
.swiper-item {
  height: 100%;
}
</style>
