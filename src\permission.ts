import { hideLoading, showToast } from './utils'
import useUserStore from '@/store/modules/user'
// 定义需要验证权限的拦截器类型
const jumpInterceptorList = ['navigateTo', 'redirectTo', 'reLaunch', 'switchTab']

const isCheckLogin = (path: string) => {
  // 指定需要登录才能跳转的页面
  const pathList = ['/subPages/user/invite', '/subPages/clock-in/record']

  if (pathList.filter(item => item === path).length === 0) {
    return true
  }

  const userStore = useUserStore()

  if (userStore.token !== '') {
    return true
  }
  return false
}

// 应用权限校验至拦截器
jumpInterceptorList.forEach(item => {
  uni.addInterceptor(item, {
    // 拦截前触发
    invoke(args) {
      hideLoading()
      if (!isCheckLogin(args.url)) {
        showToast('请登录后访问')
        return false
      }
      return true
    },
  })
})
