<template>
  <view class="plan-pages container">
    <view class="plan-list">
      <view class="plan-list-header">
        <span class="plan-list-title">每日新词</span>
        <span class="plan-list-title">完成天数</span>
      </view>

      <view class="plan-list-content">
        <picker-view
          :indicator-style="`height: ${pvIndicatorHeight}`"
          :value="pvValue"
          immediate-change
          indicator-class="picker-view-indicator"
          class="picker-view"
          @change="changeHandler">
          <picker-view-column>
            <view v-for="(item, index) in planList" :key="index" class="item">
              <view class="plan-item">
                <text class="item-name"> {{ item.word }}个 </text>
                <text class="item-name"> {{ item.day }}天 </text>
              </view>
            </view>
          </picker-view-column>
        </picker-view>
      </view>
      <view class="plan-time"> 预计完成日期: {{ completeTime }} </view>
    </view>

    <view class="plan-set">
      <view class="set-item">
        <text class="set-item-title">
          背诵顺序
          <text v-if="isModifyMode" class="text-18rpx">（不可更改）</text>
        </text>
        <view class="set-item-tags">
          <view
            v-for="(tag, index) in tags"
            :key="tag"
            class="tag"
            :class="{ 'tag-disabled': isModifyMode }"
            @click="!isModifyMode && (type = index)">
            {{ tag }}
          </view>
          <span class="empty-tag" :style="{ transform: `translateX(${translateX}rpx)` }">
            {{ tags[type] }}
          </span>
        </view>
      </view>

      <view class="set-item">
        <text class="set-item-title">学习计划表</text>
        <view class="set-item-value flex">
          总{{
            isModifyMode
              ? learnedDay + Math.ceil(totalCount / userSelectCount)
              : Math.ceil(totalCount / userSelectCount)
          }}list
          <u-icon name="arrow-right" size="24rpx" color="#BFBFBF" class="ml-10rpx"></u-icon>
        </view>
      </view>
      <view class="set-item">
        <text class="set-item-title">词书单词列表</text>
        <view class="set-item-value"> {{ totalCount }}词 </view>
        <u-icon name="arrow-right" size="24rpx" color="#BFBFBF" class="ml-10rpx"></u-icon>
      </view>
      <view class="set-item">
        <text class="set-item-title">学习计划表</text>
        <view class="set-item-value"> {{ userSelectCount }} 词/天 </view>
      </view>
    </view>

    <view class="plan-btn">
      <u-button
        type="primary"
        :loading="loading"
        :disabled="loading"
        shape="circle"
        :text="isModifyMode ? '确认修改计划' : '开启你的单词记忆之旅'"
        @click="toReciteWords"></u-button>
    </view>
  </view>
</template>

<script setup lang="ts" name="plan-pages">
import {
  createRecitePlan,
  getReciteWordCount,
  getUserReciteBook,
  getRecitePlan,
  getReciteWordCountByBook,
} from '@/api/project/recite-words'
import { updateModelData } from '@/api/visual'

// 最小天数
const MIN = 30
// 词书标题
const title = ref('')
// 词书Id
const id = ref('')
// 顺序标签
const tags = ['原书序', '乱序', '字母 a-z']
const outTags = ['原始排序', '随机乱序', '字母正序']
// 单词总量
const totalCount = ref(0)
// pick-view 选中的值
const pvValue = ref([0])
// tag类型
const type = ref(0)
// 是否加载中
const loading = ref(false)
// 已背诵天数
const learnedDay = ref(0)
// 是否为修改模式
const isModifyMode = ref(false)
// 当前计划信息
const currentPlan = ref<any>(null)
// 已学习的单词数
const learnedWordCount = ref(0)
// pv指示器的高度
const pvIndicatorHeight = computed(() => {
  return uni!.$u.addUnit(30)
})
// 位移X的大小
const translateX = computed(() => {
  // 初始值20
  return {
    0: 20,
    1: 120,
    2: 240,
  }[type.value]
})

// 计划列表
const planList = computed(() => {
  let startNum = MIN
  const result = []
  while (true) {
    const day = Math.ceil(totalCount.value / startNum)
    result.push({
      word: startNum,
      day,
    })
    startNum += 10
    if (day <= 10 && result.length > 3) break
    if (result.length > 20 || startNum > 250) break
  }
  return result
})

// 总天数
const allDays = computed(() => {
  return learnedDay.value + planList.value[pvValue.value[0]].day
})

// 用户选择的词书单词数量
const userSelectCount = computed(() => {
  return planList.value[pvValue.value[0]].word
})

// 预计完成日期
const completeTime = computed(() => {
  const daysToAdd = planList.value[pvValue.value[0]].day
  const currentDate = new Date() // 获取当前日期
  currentDate.setDate(currentDate.getDate() + daysToAdd) // 计算新日期
  return uni.$u.timeFormat(currentDate.getTime(), 'yyyy年mm月dd日')
})

// 初始化修改模式
const initModifyMode = async () => {
  try {
    // 获取当前用户的学习计划
    const planRes = await getRecitePlan()
    const userPlans = planRes.data.list

    // 查找当前词书的学习计划
    currentPlan.value = userPlans.find(plan => plan.book === id.value)

    if (!currentPlan.value) {
      uni.showToast({
        title: '未找到学习计划',
        icon: 'none',
      })
      uni.navigateBack()
      return
    }

    // 获取词书总单词数
    const totalRes = await getReciteWordCount(id.value)
    const total = totalRes.data?.[0]?.totalCount || 0

    // 获取打卡记录
    const studyRecordsRes = await getReciteWordCountByBook(id.value)
    const studyRecords = studyRecordsRes.data.list || []

    // 从打卡记录中计算已学习的单词总数（从 tableField102 字段）
    let totalLearnedWords = 0
    studyRecords.forEach(record => {
      if (record.tableField102 && Array.isArray(record.tableField102)) {
        totalLearnedWords += record.tableField102.length
      }
    })

    learnedWordCount.value = totalLearnedWords

    // 计算剩余单词数
    totalCount.value = total - totalLearnedWords

    // 获取实际已学习天数
    // 数组最后一个元素是最新天，检查最新天是否已签到
    if (studyRecords.length > 0) {
      const latestRecord = studyRecords[studyRecords.length - 1] // 最新的一天
      if (latestRecord.sign === 1) {
        // 最新天已签到，已学习天数就是最新天的天数
        learnedDay.value = latestRecord.days
      } else {
        // 最新天未签到，已学习天数是最新天数减1
        learnedDay.value = latestRecord.days - 1
      }
    } else {
      learnedDay.value = 0
    }

    // 根据当前计划的背诵顺序设置type值
    const orderIndex = outTags.findIndex(tag => tag === currentPlan.value.order)
    if (orderIndex !== -1) {
      type.value = orderIndex
    }

    console.log('修改模式初始化完成:', {
      currentPlan: currentPlan.value,
      learnedWordCount: learnedWordCount.value,
      totalCount: totalCount.value,
      remainingWords: totalCount.value,
    })
  } catch (error) {
    console.error('初始化修改模式失败:', error)
    uni.showToast({
      title: '获取计划信息失败',
      icon: 'none',
    })
    uni.navigateBack()
  }
}

onLoad(async (e: any) => {
  title.value = decodeURIComponent(e.title)
  id.value = e.id

  // 检查是否为修改模式（通过URL参数判断）
  isModifyMode.value = e.modify === 'true'

  // 在修改模式下，learnedDay 会在 initModifyMode 中动态获取
  // 在创建模式下，learnedDay 保持为 0
  if (!isModifyMode.value) {
    learnedDay.value = Number(e.learnedDay) || 0
  }

  // 使用词书标题
  uni.setNavigationBarTitle({
    title: isModifyMode.value ? `修改计划 - ${title.value}` : title.value,
  })

  // 获取单词总量
  const { totalCount: total = 0 } = (await getReciteWordCount(e.id))?.data?.[0] || {}

  if (isModifyMode.value) {
    // 修改模式：获取当前计划和已学习单词数
    await initModifyMode()
  } else {
    // 创建模式：使用原有逻辑
    totalCount.value = e.learned ? total - e.learned : total
  }
})

// 创建或修改记忆计划
const toReciteWords = async () => {
  if (loading.value) return

  // 获取当前选中的计划
  const current = planList.value[pvValue.value[0]]
  loading.value = true

  try {
    if (isModifyMode.value) {
      // 修改模式：更新现有计划
      await updateModelData({
        menuId: '668e3212c523aa5a70e0a2a9',
        _id: currentPlan.value._id,
        data: JSON.stringify({
          word: current.word,
          bookName: title.value,
          day: learnedDay.value + current.day, // 当前学习天数 + 剩余天数
          order: outTags[type.value],
          book: id.value,
        }),
      })

      uni.showToast({
        title: '计划修改成功',
        icon: 'success',
      })

      // 延迟返回，让用户看到成功提示
      setTimeout(() => {
        uni.navigateBack()
      }, 1500)
    } else {
      // 创建模式：创建新计划
      await createRecitePlan({
        word: current.word,
        bookName: title.value,
        day: allDays.value,
        order: outTags[type.value],
        book: id.value,
      })

      // 获取用户记忆计划
      const result = await getUserReciteBook()
      uni.$u.route({
        type: 'redirectTo',
        url: '/subPages/recite-words/word-clock',
        params: {
          id: id.value,
          wordNum: current.word,
          bookName: title.value,
          order: outTags[type.value],
          totalDay: allDays.value,
          userReciteId: result.data.list[0]._id,
        },
      })
    }
  } catch (error) {
    console.error('操作失败:', error)
    uni.showToast({
      title: isModifyMode.value ? '修改计划失败' : '创建计划失败',
      icon: 'none',
    })
  }

  loading.value = false
}

// pv change事件
const changeHandler = ({ detail }: { detail: { value: number[] } }) => {
  pvValue.value = detail.value
}
</script>

<style></style>

<style lang="scss" scoped>
.plan-pages {
  height: 100vh;
  background-color: #f4f5f7;
  overflow: hidden;
  .plan-list {
    margin: 20rpx;
    background-color: #fff;
    border-radius: 20rpx;
    .plan-list-header {
      display: flex;
      padding: 20rpx 0;
      .plan-list-title {
        font-size: 28rpx;
        flex: 1;
        text-align: center;
        font-weight: 600;
        color: #333333;
      }
    }
    .plan-list-content {
      .picker-view {
        height: 90px;
        margin-bottom: 20rpx;
        font-size: 28rpx;
        .plan-item {
          height: 30px;
          display: flex;
          .item-name {
            display: flex;
            justify-content: center;
            align-items: center;
            flex: 1;
            text-align: center;
          }
        }
      }
    }
    .plan-time {
      color: $uni-text-color-grey;
      text-align: center;
      border-top: 1rpx solid $uni-border-color;
      padding: 20rpx 0;
      color: #c9c9c9;
      font-size: 24rpx;
    }
  }
  .plan-set {
    margin-top: 20rpx;
    background-color: $uni-bg-color;
    .set-item {
      display: flex;
      padding: 30rpx;
      justify-content: space-between;
      align-items: center;
      .set-item-title {
        font-size: 30rpx;
        display: flex;
        align-items: center;
        flex: 1;
        font-weight: bold;
      }
      .set-item-value {
        font-size: 24rpx;
        color: #bfbfbf;
      }
      .set-item-tags {
        background: rgba(69, 154, 247, 0.1);
        padding: 6rpx 26rpx;
        color: #bfbfbf;
        right: 0;
        position: relative;
        display: flex;
        align-items: center;
        gap: 43rpx;
        border-radius: 27rpx 27rpx 27rpx 27rpx;
        height: 54rpx;
        .tag {
          margin: 0 8rpx;
          white-space: nowrap;
          text-align: center;
          font-size: 24rpx;

          &.tag-disabled {
            opacity: 0.5;
            cursor: not-allowed;
          }
        }
        .empty-tag {
          content: '';
          $emptyHeight: 43rpx;
          position: absolute;
          transition-property: transform;
          transition-duration: 0.3s;
          left: 0;
          text-align: center;
          color: #000;
          z-index: 1;
          width: 130rpx;
          font-size: 24rpx;
          line-height: $emptyHeight;
          height: $emptyHeight;
          border-radius: 27rpx 27rpx 27rpx 27rpx;
          background-color: #fff;
          box-shadow: 0rpx 3rpx 6rpx 1rpx rgba(3, 44, 90, 0.23);

          .modify-tip {
            font-size: 18rpx;
            opacity: 0.7;
            margin-left: 8rpx;
          }
        }
      }
    }
  }
  .plan-btn {
    position: absolute;
    bottom: 100rpx;
    width: 80%;
    left: 50%;
    transform: translateX(-50%);
  }
}
</style>
