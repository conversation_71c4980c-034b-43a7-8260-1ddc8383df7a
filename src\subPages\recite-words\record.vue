<template>
  <view>
    <LoadMoreList ref="loadMoreList" style="height: 100vh" :request-fn="getReciteWordHistoryList">
      <template #default="{ list }">
        <view class="exercise-record bg-white">
          <u-cell
            v-for="item in (list as TestHistoryItem[])"
            :key="item._id"
            is-link
            :border="false"
            @tap="goReport(item)">
            <template #title>
              <view class="record-title flex">
                <u-tag :text="item.type || '未知'" size="mini"></u-tag>
                <text style="margin-left: 18rpx">
                  {{ item.practiceNo }}
                </text>
              </view>
              <view class="record-note text-note flex">
                <text class="record-create-time">
                  提交时间: {{ formatDate(item.creatorTime) }}
                </text>
                <text class="record-answer-time">
                  总耗时: {{ formatTime(Number(item.answerTime)) }}
                </text>
              </view>
            </template>
          </u-cell>
        </view>
      </template>
    </LoadMoreList>
  </view>
</template>

<script setup lang="ts" name="record">
import LoadMoreList from '@/components/LoadMoreList.vue'
import type { TestHistoryItem } from '@/api/project/recite-words/type'
import { getReciteWordHistoryList } from '@/api/project/recite-words/index'
import useDateFormatter from '@/hooks/useDateFormatter'
const { formatDate } = useDateFormatter('YYYY-MM-DD')
const loadMoreList = ref<InstanceType<typeof LoadMoreList>>()

onLoad(() => {})
onMounted(() => {})
onReachBottom(() => {
  loadMoreList.value!.onReachBottom()
})
const formatTime = (time: number) => {
  function padStart(num: number) {
    return (num | 0).toString().padStart(2, '0') || '00'
  }
  return `${padStart(time / 60 / 60)}:${padStart((time / 60) % 60)}:${padStart(time % 60)}`
}
function goReport(item: any) {
  uni.navigateTo({
    url: `/subPages/recite-words/word-report?reportId=${item._id}`,
  })
}
</script>

<style>
page {
  background: #f3f5f7;
}
</style>

<style lang="scss" scoped>
@import '@/common/css/index.scss';
@import '@/common/css/project.scss';
.exercise-record {
  .record-note {
    margin-top: 12rpx;
    .record-create-time {
      width: 60%;
    }
    .record-answer-time {
      width: 40%;
    }
  }
}
</style>
