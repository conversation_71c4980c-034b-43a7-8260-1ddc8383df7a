export interface IMemberPriceData {
  _id: string
  creatorUserId: UserIdData
  price: number
  integral: number
  creatorTime: number
  title: string
  is_recommend: 0 | 1
}

// 微信下单
export interface WxPayParams {
  description: string
  appId: string
  code: string
  out_order_no: string
  goods_module_id: string
  goods_price_field: string
  goods_id: string
}

// 会员表实体
export interface IMemberData {
  _id: string
  creatorTime: number
  points: number
  user: UserIdData[]
  member_name: string
}

// 积分信息
export interface IPointsData {
  _id: string
  points_num: number
  start_time: number
  points_source: string
  points_day: number
  creatorTime: number
  creatorUserId: UserIdData
  user: UserIdData[]
  end_time: number
}

// 积分操作记录
export interface IPointsRecordData {
  _id?: string
  points_num: number
  action_id: string
  action_name: string
  points_day: number
  remark?: string
  creatorTime?: number
  points_type: string
  user?: UserIdData[] | string[]
}

// 积分操作
export interface IPointsActionData {
  _id: string
  points_num: number
  action_type: string
  action_name: string
  creatorTime: number
  points_type: string
  points_day: number
}

// 权益
export interface IMemberRightData {
  _id?: string
  equity_id: string
  equity_type: '刷题' | '单词' | '课程'
  creatorTime?: number
  user: UserIdData[] | string[]
}

// 订单
export interface IOrderData {
  _id: string
  out_order_no: string
  goods_module_id: string
  goods_price_field: string
  goods_id: string
  goodsInfo: IGoodsData
  description: string
  pay_status: string
  pay_price: number
  pay_type: string
  pay_channel: string
  requestData: IRequestData
  creatorTime: number
  creatorUserId: string
  search: string
}

interface IGoodsData {
  _id: string
}

interface IRequestData {
  appid: string
  mchid: string
  description: string
  out_trade_no: string
  time_expire: string
  attach: string
  notify_url: string
  payer: {
    openid: string
  }
  amount: {
    total: number
    currency: string
  }
}
