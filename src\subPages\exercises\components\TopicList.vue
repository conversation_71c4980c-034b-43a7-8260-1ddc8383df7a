<template>
  <view class="topic-list flex-col" :style="fullHeight">
    <view v-if="showBanner" class="topic-list-decorate flex-shrink-0">
      <image
        class="w-full h-full"
        :src="getSystemImg('6747e9ed0a34815816f11159/669a0a930bb07d7cd6ed3ac3')"
        mode="aspectFit" />
      <view class="topic-list-decorate-content">
        <test class="title u-main-color inline-block">真题领取</test>
        <button class="w-fit arrow-right circle-button" @tap="goPastExamPaper"></button>
      </view>
    </view>
    <view class="topic-list-main flex flex-grow overflow-hidden">
      <scroll-view class="sidebar flex-shrink-0" scroll-y scroll-with-animation>
        <view
          v-for="(item, index) in list"
          :key="index"
          class="sidebar-item text-ellipsis before font-bold"
          :class="{ active: index === activeIndex }"
          @tap="sideClickHandler(index)">
          {{ item.subject }}
        </view>
      </scroll-view>
      <view class="section-context flex-grow overflow-hidden bg-white flex-col">
        <view>
          <!-- 搜索框插槽 -->
          <slot name="search"></slot>
        </view>
        <!-- 列表插槽 -->
        <slot name="list" :active="activeIndex.toString()"></slot>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts" name="topic-list">
import { getStatusBarHeight, getSystemImg, showToast } from '@/utils/index'
import type { Subjects } from '@/api/project/exercises/type'
const props = defineProps<{
  typeName?: string
  showBanner?: boolean
  list: Subjects[]
}>()
const emit = defineEmits(['pickTitle'])
// 选择的类别下标
const activeIndex = ref(0)
// 全屏 100vh-44px-状态栏
const fullHeight = ref({
  height: `calc(100vh - ${(Number(getStatusBarHeight()) + 44) * 2}rpx)`,
})
// 真题跳转
function goPastExamPaper() {
  uni.navigateTo({
    url: '/subPages/exercises/past-paper-practice',
  })
}
// 分类选择
function sideClickHandler(index: number) {
  activeIndex.value = index
  emit('pickTitle', activeIndex.value)
}
</script>

<style lang="scss" scoped>
@import '@/common/css/index.scss';
@import '@/common/css/project.scss';
.container-topic {
  height: calc(100vh - (88rpx));
}
.topic-list-decorate {
  width: 100%;
  height: 208rpx;
  background-color: #e6edff;

  &-content {
    position: absolute;
    top: 32rpx;
    left: 18%;

    .title {
      font-size: 50rpx;
      margin-bottom: 16rpx;
    }

    button {
      line-height: 2;
      margin-left: 0;

      &::before {
        font-family: uicon-iconfont;
        content: 'Go \e605';
      }
    }
  }
}

.topic-list-main {
  .sidebar {
    width: 30vw;
    min-width: 242rpx;
    padding: 27rpx 0;
    background: #f4f7ff;

    .sidebar-item {
      position: relative;
      text-align: center;
      padding: 18rpx 0.5em;
      font-size: 30rpx;
      color: #666;

      &.active {
        color: $u-theme-color;
        background-color: #fff;

        &::before {
          width: 6rpx;
          height: 60%;
          left: 0;
          top: 0;
          bottom: 0;
          margin: auto;
          border-radius: 25rpx;
          background-color: $u-theme-color;
        }
      }
    }
  }
  .section-context {
    .u-list {
      height: 0 !important;
      flex-grow: 1;
    }
  }
}
</style>
