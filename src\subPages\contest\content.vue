<template>
  <view>
    <TopicProgress :en-code="enCode" />
    <TabsContainer></TabsContainer>
  </view>
</template>

<script setup lang="ts" name="topic-progress">
import TopicProgress from './components/TopicProgress.vue'

import TabsContainer from './components/TabsContainer.vue'
import { useExamStore } from '@/store/modules/useExamStore.ts' // 导入保存当前的encode的 store
/**
 * onLoad是uniapp的页面加载函数，在页面加载时触发
 * 这里接收的参数是页面跳转时传递过来的参数
 * 是从src\pages\contest\components\TopicCard.vue传递过来的真题编码
 */
const enCode = ref('') // 定义响应式变量来存储 enCode
const examStore = useExamStore() // 使用 Pinia store
onLoad(options => {
  if (options) {
    console.log('获取到的参数', options)
    // 获取传递的 enCode 参数
    enCode.value = options.enCode // 将 enCode 存储到响应式变量中
    // 更新 store 中的 enCode
    examStore.updateEnCode(options.enCode)
    console.log('传递过来的赛题编码', examStore.enCode)
  } else {
    console.error('options is undefined')
  }
})

// 使用provide/inject传递enCode
provide('enCode', enCode)
</script>

<style>
page {
  background-color: #f4f5f7;
}
</style>

<style lang="scss" scoped></style>
