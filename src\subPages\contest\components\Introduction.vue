<template>
  <view>
    <view v-for="(item, index) in list" :key="index" class="mb-20rpx">
      <view class="introduction-title">{{ item.title }}</view>
      <view class="text-24rpx text-#999999 mt10rpx" v-html="item.content"> </view>
    </view>
  </view>
</template>

<script setup lang="ts" name="Introduction">
defineProps({
  list: {
    type: Array as any,
    required: true,
  },
})
</script>

<style lang="scss" scoped>
.introduction-title {
  height: 42rpx;
  font-family: PingFang SC, PingFang SC;
  font-weight: 400;
  font-size: 30rpx;
  color: #333333;
  line-height: 42rpx;
  text-align: left;
  font-style: normal;
  text-transform: none;
  padding-left: 20rpx;
  position: relative;
}

.introduction-title::before {
  content: '';
  display: inline-block;
  width: 8rpx;
  height: 20rpx;
  position: absolute;
  background: #459af7;
  border-radius: 10rpx;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
}
</style>
