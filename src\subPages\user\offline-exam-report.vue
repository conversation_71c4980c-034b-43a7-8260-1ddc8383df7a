<template>
  <view class="w750rpx p20rpx box-border">
    <LoadMoreList
      ref="loadMoreList"
      :request-fn="getCurrentUserExamScore"
      :request-params="{ filter }">
      <template #default="{ list }">
        <view
          v-for="(item, index) in (list as ExamScoreType[])"
          :key="index"
          class="w690rpx ma mt20rpx bg-white rounded-10rpx pb20rpx">
          <view class="title text-#FFFFFF text-30rpx font-bold lh-58rpx">
            <view class="text-center">人人学教育</view>
            <view class="text-center">
              {{ `${new Date().getFullYear()}年湖南省统招专升本${item.exam_degree}` }}</view
            >
          </view>
          <view
            class="mt10rpx flex justify-between p-x-60rpx p-y-15rpx box-border text-28rpx text-#333333">
            <view class="text-#999999">姓名</view>
            <view>{{ item.name }}</view>
          </view>
          <view class="flex justify-between p-x-60rpx p-y-15rpx box-border text-28rpx text-#333333">
            <view class="text-#999999">意向专业类别</view>
            <view>{{ item.major_category }}</view>
          </view>
          <view class="flex justify-between p-x-60rpx p-y-15rpx box-border text-28rpx text-#333333">
            <view class="text-#999999">专业类别排名</view>
            <view>{{ item.sort }}</view>
          </view>
          <view class="w-full p-x-20rpx box-border text-28rpx">
            <u-transition :show="item.show">
              <u-divider></u-divider>
              <view class="flex justify-around">
                <view class="flex-1 text-#333333 text-center"> 科目 </view>
                <view class="flex-1 text-#333333 text-center"> 成绩 </view>
              </view>
              <u-divider v-if="item.english"></u-divider>
              <view v-if="item.english" class="flex justify-around">
                <view class="flex-1 text-#999999 text-center"> 大学英语 </view>
                <view class="flex-1 text-#999999 text-center"> {{ item.english }} </view>
              </view>
              <u-divider v-if="item.math"></u-divider>
              <view v-if="item.math" class="flex justify-around">
                <view class="flex-1 text-#999999 text-center"> 高等数学 </view>
                <view class="flex-1 text-#999999 text-center"> {{ item.math }} </view>
              </view>
              <u-divider v-if="item.chinese"></u-divider>
              <view v-if="item.chinese" class="flex justify-around">
                <view class="flex-1 text-#999999 text-center"> 大学语文 </view>
                <view class="flex-1 text-#999999 text-center"> {{ item.chinese }} </view>
              </view>
              <u-divider></u-divider>
              <view class="flex justify-around">
                <view class="flex-1 text-#999999 text-center"> 公共课总分 </view>
                <view class="flex-1 text-#999999 text-center">
                  {{ getTotalScore(item) }}
                </view>
              </view>
            </u-transition>
            <u-divider></u-divider>
            <view class="flex-center" :class="{ 'rotate-180': item.show }" @click="toggle(item)">
              <u-icon name="arrow-down" size="30rpx"></u-icon>
            </view>
          </view>
        </view>
      </template>
    </LoadMoreList>
  </view>
</template>

<script setup lang="ts" name="user-offlineExamReport">
import LoadMoreList from '@/components/LoadMoreList.vue'
import type { ExamScoreType } from '@/api/user/type'
import type { FilterType } from '@/es/request'
import { getCurrentUserExamScore } from '@/api/user'
import useUserStore from '@/store/modules/user'
const loadMoreList = ref<InstanceType<typeof LoadMoreList>>()
onReachBottom(() => {
  loadMoreList.value?.onReachBottom()
})

const filter = ref<FilterType[]>([
  {
    enCode: 'phone',
    method: 'eq',
    type: 'custom',
    value: [useUserStore().userInfo?.phone],
  },
])

const getTotalScore = (item: ExamScoreType) => {
  let totalScore = 0
  if (item.english) totalScore += Number(item.english)
  if (item.math) totalScore += Number(item.math)
  if (item.chinese) totalScore += Number(item.chinese)
  return totalScore
}

const toggle = (val: ExamScoreType) => {
  val.show = !val.show
}

onLoad(() => {})
</script>

<style>
page {
  background-color: #f4f5f7;
}
</style>

<style lang="scss" scoped>
.title {
  width: 100%;
  height: 117rpx;
  background: #459af7;
  border-radius: 10rpx 0rpx 60rpx 50rpx;
}
</style>
