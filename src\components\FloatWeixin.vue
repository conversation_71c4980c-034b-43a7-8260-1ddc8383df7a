<template>
  <view class="fixed right-20rpx bottom-130rpx z-9999">
    <!-- <view
      class="w180rpx h60rpx bg-white box-shadow rounded-40rpx flex flex-center p10rpx p-x-20rpx items-center"
      @click="TargetWeChatService">
      <u-icon name="weixin-fill" color="#00c700" size="28"></u-icon>
      <view class="w10rpx h-full"></view>
      <view class="text-22rpx text-#333333">
        退伍士兵
        <br />
        免试咨询
      </view>
    </view> -->
    <view
      class="w180rpx h60rpx mt30rpx bg-#459AF7 shadow-[0px_3px_6px_1px_rgba(69,154,247,0.4)] rounded-40rpx flex flex-center p10rpx p-x-20rpx items-center">
      <!-- @click="TargetWeChatService"> -->
      <image
        :src="getSystemImg('687758481807a96b974f9916/68786296cfdce7607d9cc11d')"
        mode="scaleToFill"
        class="w35 h-30" />
      <view class="w10rpx h-full"></view>
      <view class="text-22rpx text-#FFFFFF"> 公开课预约 </view>
    </view>
    <view
      class="w180rpx h60rpx mt30rpx bg-white box-shadow rounded-40rpx flex flex-center p10rpx p-x-20rpx items-center"
      @click="TargetWeChatService">
      <u-icon name="weixin-fill" color="#00c700" size="28"></u-icon>
      <view class="w10rpx h-full"></view>
      <view class="text-22rpx text-#333333">
        考前集训
        <br />
        点击咨询
      </view>
    </view>
  </view>
</template>

<script setup lang="ts" name="float-weixin">
import { TargetWeChatService, getSystemImg } from '@/utils'
</script>

<style lang="scss" scoped></style>
