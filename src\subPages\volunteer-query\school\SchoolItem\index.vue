<template>
  <view
    class="w730rpx h180rpx rounded-10rpx bg-white flex justify-between items-center p30rpx box-border ma m-y-20rpx relative">
    <view
      v-if="flag"
      class="flex absolute top-10rpx right-10rpx text-26rpx text-#969DAB items-center"
      @click="isCollect(collect as boolean)">
      <u-icon
        :name="collect ? 'star-fill' : 'star'"
        size="20"
        :color="collect ? 'orange' : ''"></u-icon>
      收藏
    </view>
    <u-image
      :src="assembleImgData(itemData.school_badge?.[0])"
      :width="imgSize?.width || '110rpx'"
      :height="imgSize?.height || '180rpx'"
      mode="aspectFit"></u-image>
    <view class="flex-1 h-140rpx ml20rpx flex flex-col justify-between">
      <view class="text-30rpx text-#333333 fw-bold">{{ itemData.school_name || '湖南大学' }}</view>
      <view class="flex w-full m-y-4rpx">
        <view class="h36rpx bg-#feefd7 mr-20rpx p2rpx p-x-6rpx rounded-6rpx">
          <u-icon
            name="home"
            size="18"
            :label="itemData.school_type"
            space="5"
            color="#FCB138"
            label-color="#FCB138"
            label-size="22rpx"></u-icon>
        </view>
        <view class="h36rpx bg-#feefd7 mr-20rpx p2rpx p-x-6rpx rounded-6rpx">
          <u-icon
            name="home"
            size="18"
            :label="itemData.academy_type"
            space="5"
            color="#FCB138"
            label-color="#FCB138"
            label-size="22rpx"></u-icon>
        </view>
        <view class="h36rpx bg-#feefd7 mr-20rpx p2rpx p-x-6rpx rounded-6rpx">
          <u-icon
            name="home"
            size="18"
            :label="itemData.rank_type"
            space="5"
            color="#FCB138"
            label-color="#FCB138"
            label-size="22rpx"></u-icon>
        </view>
      </view>
      <view>
        <u-icon
          name="map-fill"
          size="12"
          space="4"
          color="#969dab"
          :label="itemData.school_address || '长沙市芙蓉区农大路一号'"
          label-color="#969dab"
          label-size="22rpx"></u-icon>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts" name="school-item">
import type { IBKSchoolData } from '@/api/project/index/type'
import { assembleImgData, getSystemImg } from '@/utils'

defineProps<{
  itemData: IBKSchoolData
  imgSize?: {
    width: string
    height: string
  }
  collect?: boolean
  flag: boolean
}>()

const emits = defineEmits(['clickCollect'])

const isCollect = (flag: boolean) => {
  emits('clickCollect', flag)
}
</script>

<style lang="scss" scoped></style>
