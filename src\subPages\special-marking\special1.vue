<template>
  <view v-if="!showCropper">
    <view class="w750rpx p30rpx box-border">
      <view class="text-32rpx text-#333333">试卷名:{{ topicDetail?.name }}</view>
      <view class="flex flex-wrap mt20rpx gap-30rpx justify-between">
        <view
          v-for="(item, index) in imgList"
          :key="index"
          class="w330rpx h400rpx rounded-20rpx b-8rpx b-#c7e0fd b-solid box-border flex-center">
          <view v-if="!item.url" class="flex-center flex-col" @click="handleClick(index)">
            <image
              :src="getSystemImg('/6747e9ed0a34815816f11159/670c8c434dfd0e1c49e0a94d')"
              class="w102rpx h82rpx">
            </image>
            <view class="text-26rpx text-#333333 mt20rpx">{{ item.name }}</view>
          </view>
          <image
            v-else
            class="w-full h-full"
            :src="getHeadIcon(item.url)"
            @click="handleClick(index)"></image>
        </view>
      </view>
      <view class="text-center text-30rpx text-#999999 mt20rpx">
        请使用手机原相机拍摄清晰图，再进行上传。
      </view>
    </view>
    <view class="w-full fixed bottom-30rpx p30rpx box-border">
      <u-button text="完成上传" shape="circle" type="primary" @click="handleButton"></u-button>
    </view>
  </view>

  <view v-if="showCropper && tempImagePath" class="container">
    <cropper
      :src="tempImagePath"
      class="cropper-overlay"
      @crop="onCropComplete"
      @cancel="showCropper = false"
      @choose-image="handleClick(currentImageIndex)" />
  </view>
  <up-modal :show="showModal" title="拍照注意事项">
    <view class="slot-content flex flex-col items-center">
      <image
        :src="getSystemImg('/6747e9ed0a34815816f11159/6757e70bf8730075fead9d54')"
        mode="aspectFit"></image>
    </view>
    <template #confirmButton>
      <u-button type="primary" :disabled="buttonDisabled" text="关闭" @click="closeModal" />
    </template>
  </up-modal>
  <canvas
    canvas-id="tempCanvas"
    :style="canvasStyle"
    style="position: absolute; z-index: -1; opacity: 0"></canvas>
</template>

<script setup lang="ts" name="special-marking-special">
import {
  getHeadIcon,
  getSystemImg,
  hideLoading,
  miniProgramUploadFile,
  showLoading,
  showToast,
  showToastBack,
  uploadBase64Img,
} from '@/utils'
import cropper from '@/components/bt-cropper/cropper.vue'
import type { ISpecialClockIn } from '@/api/project/clock-in/type.d'
import { specialClockIn } from '@/api/project/clock-in'
import { addMarkingTopicCut, getPaperCut } from '@/api/project/special-marking'
import { getTopicDetail } from '@/api/project/exercises'
import type { MarkingSubmitData } from '@/api/project/special-marking/type.d'
import useUserStore from '@/store/modules/user'
import type { TopicDetail } from '@/api/project/exercises/type.d'
// 专项练习打卡的数据
const specialClockInData = ref<ISpecialClockIn>({
  code: '',
  images: [],
  special_type: '专项打卡',
  grade: '',
  subject: '',
  paperId: '',
})

// 弹窗
const showModal = ref(false)
const closeModal = () => {
  showModal.value = false
  uni.setStorageSync('isLook', true)
}
const buttonDisabled = ref(true)

const paperId = ref('')
const currentImageIndex = ref(0)
const imgList = ref<UploadImgData[]>([
  {
    name: '上传第一页',
    url: '',
  },
  {
    name: '上传第二页',
    url: '',
  },
  {
    name: '上传第三页',
    url: '',
  },
  {
    name: '上传第四页',
    url: '',
  },
])
// 裁剪后的base64
const base64List = ref<string[]>([])

// 题目切割回写
const markingTopicCut = ref<MarkingSubmitData>({
  paperId: '',
  tableField102: [],
  userId: '',
})

// 题目明细
const topicDetail = ref<TopicDetail>()

// 裁剪
const showCropper = ref(false)
const tempImagePath = ref('')

const canvasStyle = ref({
  width: '',
  height: '',
})
const handleClick = (index: number) => {
  currentImageIndex.value = index
  uni.chooseImage({
    count: 1,
    sizeType: ['original'],
    sourceType: ['album'],
    success: res => {
      const tempFilePath = res.tempFilePaths[0]
      uni.getImageInfo({
        src: tempFilePath,
        success: async res => {
          if (res.width > res.height) {
            canvasStyle.value = {
              width: `${res.height}px`,
              height: `${res.width}px`,
            }
            const ctx = uni.createCanvasContext('tempCanvas')
            if (res.orientation === 'up') {
              showToast('请上传竖图')
              return
            }
            ctx.drawImage(tempFilePath, 0, 0, res.height, res.width)
            ctx.draw()
            uni.canvasToTempFilePath({
              canvasId: 'tempCanvas',
              success: canvasRes => {
                console.log('转换成功')
                tempImagePath.value = canvasRes.tempFilePath
                showCropper.value = true
              },
              fail: err => {
                console.error('转换失败', err)
                showToast('处理失败')
              },
            })
          } else {
            showCropper.value = true
            tempImagePath.value = tempFilePath
          }
        },
      })
    },
  })
}

// 存在的问题 用户拍的是一张横图 通过图片软件改了显示方式为竖图 但其实图片本身就是一张横图 图片裁剪框在裁剪时只记录了裁剪的宽高 所以会根据这个宽高去裁剪原图片 就会出现宽高的裁剪互换的情况。
// 这个问题是由于图片的EXIF方向信息导致的。当用户使用图片软件将横图旋转为竖图时，实际上只是修改了图片的显示方向，而图片的原始数据仍然是横图
const onCropComplete = (filePath: string) => {
  // 将图片转为base64
  uni.getFileSystemManager().readFile({
    filePath,
    encoding: 'base64',
    success: base64Res => {
      base64List.value[currentImageIndex.value] = base64Res.data as string
    },
  })
  showCropper.value = false
  showLoading('图片上传中')
  miniProgramUploadFile(filePath).then(res => {
    imgList.value[currentImageIndex.value].url = res.data.url
    hideLoading()
  })
}

// 检查图片列表
const checkImgList = (list: UploadImgData[]) => {
  let count = 4
  list.forEach(item => {
    if (!item.url) {
      showToast('请上传所有图片')
      count--
    }
  })
  return count === 4
}

const handleButton = async () => {
  if (!checkImgList(imgList.value)) return
  specialClockInData.value.images = imgList.value
  specialClockInData.value.paperId = paperId.value

  // 新增专项打卡记录
  await specialClockIn(specialClockInData.value)
  uni.$emit('special')
  showToastBack('上传成功')

  // showLoading.value = true

  // const res: any = await getPaperCut({
  //   images_base64_list: base64List.value,
  //   isAi: 1,
  //   topicLength: topicDetail.value?.tableField103.length || 0,
  // })
  // console.log(res, 'res')
  // const list = await Promise.all(
  //   res.data.map(async (item: any) => {
  //     const index = parseInt(item.order_num)
  //     const base64Img = ((await uploadBase64Img(item.block_base64_list)) as any).data.data
  //     return {
  //       index,
  //       topic_id: topicDetail.value?.tableField103[index - 1].questionId,
  //       topicImg: [base64Img],
  //       isCorrect: '',
  //       userAnswer: '',
  //       ai_isCorrect: '',
  //       ai_score: 0,
  //       judges: [],
  //     }
  //   })
  // )
  // markingTopicCut.value.paperId = paperId.value
  // markingTopicCut.value.userId = useUserStore().userInfo?.id || ''
  // markingTopicCut.value.tableField102 = list as any
  // console.log(markingTopicCut.value)
  // showLoading.value = false
  // addMarkingTopicCut(markingTopicCut.value).then(res => {
  //   uni.$emit('special')
  //   showToastBack('上传成功')
  // })
}

onLoad((e: any) => {
  paperId.value = e.topicId
  // 获取题目明细
  getTopicDetail(paperId.value).then(res => {
    topicDetail.value = res.data
  })
  // 判断是否已查看弹窗
  const isLook = uni.getStorageSync('isLook')
  if (isLook) {
    showModal.value = false
  } else {
    showModal.value = true
    setInterval(() => {
      buttonDisabled.value = false
    }, 3000)
  }
})
</script>

<style lang="scss" scoped>
.container {
  position: relative;
  height: 100%;
  width: 100%;
}

.cropper-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 999;
  /* 确保裁剪组件在最上面 */
}

.history-record {
  margin-top: 20rpx;
  display: flex;
  width: 690rpx;
  height: 95rpx;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx;
  box-sizing: border-box;
  background: #ffffff;
  box-shadow: 0rpx 6rpx 18rpx 1rpx rgba(209, 209, 209, 0.4);
  border-radius: 15rpx 15rpx 15rpx 15rpx;
}
</style>
