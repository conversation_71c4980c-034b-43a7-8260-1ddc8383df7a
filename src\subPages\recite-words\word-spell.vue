<template>
  <view class="word-spell">
    <view class="spell-header">
      <view class="spell-no">
        <text class="current">{{ index + 1 }}</text>
        <text class="total"> /{{ wordList.length }}</text>
      </view>
      <view class="familiarize" :style="[familiarizeStyle]" @click="onActionClick('熟词')">
        熟
      </view>
    </view>
    <view class="spell-pronunciation flex items-center" @click="playWord">
      <view> 英 [{{ currentWord?.pronunciation || '' }}] </view>
      <image class="audio" :src="getSystemImg('6747e9ed0a34815816f11159/669490630bb07d7cd6ed3701')">
      </image>
    </view>
    <view v-for="item in currentWord?.meaning" :key="item" class="spell-meaning">
      {{ item }}
    </view>
    <view class="spell-input" :class="{ 'error-border-color': error }">
      <text>拼写：</text>
      <input v-model="userWord" type="text" @input="onAnsChange" />
    </view>
    <view class="spell-foot flex items-center">
      <view class="flex flex-col flex-center" @click="onActionClick('生词')">
        <u-icon :name="starIcon" size="44rpx" :color="starColor"></u-icon>
        <text class="text-24rpx">生词</text>
      </view>
      <view class="flex-1 ml-44rpx">
        <u-button
          type="primary"
          :loading="loading"
          :disabled="loading || !userWord"
          color="rgba(69, 154, 247, 0.20)"
          :custom-style="{ color: '#459AF7', border: 'none' }"
          :text="btnText"
          shape="circle"
          @click="confirmSpell"></u-button>
      </view>
    </view>
    <u-modal
      :show="showModal"
      title="提示"
      content="单词全部拼写完成，是否查看报告"
      confirm-text="确认"
      cancel-text="返回"
      content-text-align="center"
      show-cancel-button
      @confirm="onModalConfirm"
      @cancel="onModalCancel"></u-modal>
  </view>
</template>

<script setup lang="ts">
import { getCurrentInstance } from 'vue'
import { getSystemImg } from '@/utils'
import useCommonStore from '@/store/modules/common'
import type { IReciteWordInfo, SubmitReciteWordType } from '@/api/project/recite-words/type'
import {
  deleteReciteWordStatus,
  getReciteWordErrorById,
  insertReciteWordError,
  insertReciteWordStatus,
  submitReciteWord,
  updateReciteWordErrorCount,
  updateReciteWordLog,
  updateReciteWordStatus,
} from '@/api/project/recite-words'

const commonStore = useCommonStore()

type QueryType = {
  name: string
  userDetailId: string
}

type RecordType = {
  bookId: string
  type: string
}

const wordList = ref<IReciteWordInfo[]>([])
const index = ref(0)
const userWord = ref('')
const loading = ref(false)
const error = ref(false)
const showModal = ref(false)
const btnText = ref('下一题')
const query = ref<QueryType>()

const record = ref<RecordType>()
const answerRecords = ref<
  Array<{
    wordId: string
    right: number
  }>
>([])

const startTime = ref(Date.now())

// 当前单词信息
const currentWord = computed(() => wordList.value[index.value])
const familiarizeStyle = computed(() => {
  if (!currentWord.value) return {}
  const isActive = currentWord.value?.type === '熟词'
  return {
    background: isActive ? '#459AF7' : '#fff',
    color: isActive ? '#fff' : '#333',
    borderColor: isActive ? '#459AF7' : '#333',
  }
})
const starColor = computed(() => (currentWord.value?.type === '生词' ? '#FCB138' : '#333'))
const starIcon = computed(() => (currentWord.value?.type === '生词' ? 'star-fill' : 'star'))

onLoad((e: any) => {
  query.value = e as QueryType
  const instance = getCurrentInstance()!.proxy as any
  startTime.value = Date.now()

  const eventChannel = instance.getOpenerEventChannel()
  // 获取单词列表
  eventChannel.once('emitWordList', (data: IReciteWordInfo[]) => {
    wordList.value = data.sort(() => Math.random() - 0.5)
    playWord()
  })
  // 获取词书和练习类型
  eventChannel.once('emitRecord', (data: RecordType) => {
    record.value = data
  })
})

async function onActionClick(setType: '熟词' | '生词') {
  if (!currentWord.value) return
  const { type, wordbookId, _id, typeId } = currentWord.value
  if (!typeId) {
    const result = await insertReciteWordStatus({
      book: wordbookId,
      wordId: _id,
      type: setType,
    })
    uni.$u.toast(`添加${setType}成功`)
    currentWord.value.typeId = result.data
    currentWord.value.type = setType
  } else if (type === setType) {
    // 取消
    await deleteReciteWordStatus(typeId)
    uni.$u.toast(`取消${setType}成功`)
    delete currentWord.value.type
    delete currentWord.value.typeId
  } else {
    // 更新
    await updateReciteWordStatus({
      id: typeId,
      type: setType,
    })

    uni.$u.toast(`更新${setType}成功`)
    currentWord.value.type = setType
  }
}
// 确认拼写
async function confirmSpell() {
  loading.value = true
  try {
    const { word, _id } = currentWord.value
    if (!userWord.value) return
    if (btnText.value === '下一题') {
      nextWord()
      return
    }

    const isCorrect = userWord.value.trim() === word

    // 日志添加
    await updateReciteWordLog({
      wordId: _id,
      correct: isCorrect ? 1 : 0,
      testType: '单词拼写',
    })

    if (!isCorrect) {
      const hasErrorWord = await getReciteWordErrorById(_id)
      if (!hasErrorWord.data.list.length) {
        await insertReciteWordError({
          wordId: _id,
          wrongTimes: 1,
          word,
        })
      } else {
        // 更新错误次数
        const { _id, wrongTimes } = hasErrorWord.data.list[0]
        await updateReciteWordErrorCount({
          id: _id,
          wrongTimes: wrongTimes + 1,
        })
      }
      playWord()
      uni.$u.toast('拼写错误')
      btnText.value = '下一题'
      error.value = true
      return
    }
    nextWord()
  } catch (error) {
    console.error(error)
  } finally {
    loading.value = false
  }
}
// 计算正确率
function calculateAccuracy(): number {
  if (!answerRecords.value.length) return 0
  const correctCount = answerRecords.value.filter(record => record.right === 1).length
  return Number(((correctCount / answerRecords.value.length) * 100).toFixed(2))
}
async function onModalConfirm() {
  if (!record.value?.bookId) return

  const submitData: SubmitReciteWordType = {
    practiceNo: '',
    type: record.value.type,
    state: '提交',
    accuracy: calculateAccuracy(),
    bookID: record.value.bookId,
    tableField106: answerRecords.value,
    answerTime: Math.floor((Date.now() - startTime.value) / 1000),
  }

  try {
    // 调用提交API
    const res = await submitReciteWord(submitData)
    if (res.code === 200) {
      uni.redirectTo({ url: `/subPages/recite-words/word-report?reportId=${res.data}` })
    }
  } catch (error) {
    uni.$u.toast('提交失败')
    console.error(error)
  }
}
function onModalCancel() {
  uni.navigateBack()
}
function nextWord() {
  // 添加答题记录用于提交
  addAnswerRecord()

  if (index.value === wordList.value.length - 1) {
    showModal.value = true
    return
  }
  userWord.value = ''
  error.value = false
  index.value++
  btnText.value = '确认拼写'
  playWord()
}
function onAnsChange() {
  btnText.value = '确认拼写'
}
// 检查答案并添加答题记录
function addAnswerRecord() {
  const existingIndex = answerRecords.value.findIndex(
    record => record.wordId === currentWord.value._id
  )

  if (existingIndex !== -1) {
    answerRecords.value[existingIndex].right =
      userWord.value.trim() === currentWord.value.word ? 1 : 0
  } else {
    answerRecords.value.push({
      wordId: currentWord.value._id,
      right: userWord.value.trim() === currentWord.value.word ? 1 : 0,
    })
  }
}
// 播放音频
function playWord() {
  commonStore.playWord(currentWord.value.word)
}
</script>

<style lang="scss" scoped>
.word-spell {
  background-color: $uni-bg-color;
  height: 100%;
  padding: 40rpx 60rpx;
  .audio {
    width: 25rpx;
    height: 25rpx;
    margin-left: 40rpx;
  }
  .spell-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    .spell-no {
      display: flex;
      align-items: center;
      font-size: 32rpx;
      .current {
        color: #333;
      }
      .total {
        color: #666;
      }
    }
    .familiarize {
      border-radius: 10rpx;
      border: 6rpx solid #0f2237;
      height: 46rpx;
      width: 46rpx;
      font-size: 26rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: bold;
    }
  }
  .spell-pronunciation {
    background: rgba(69, 154, 247, 0.1);
    border-radius: 24rpx;
    margin-top: 105rpx;
    padding: 8rpx 50rpx 8rpx 30rpx;
    font-size: 24rpx;
    color: #999999;
    width: fit-content;
  }
  .spell-meaning {
    font-size: 30rpx;
    color: #333333;
    margin-top: 20rpx;
  }
  .spell-input {
    margin-top: 20%;
    background: #ffffff;
    padding-bottom: 20rpx;
    display: flex;
    border-bottom: 1px solid #e5e5e5;
    align-items: center;
    &.error-border-color {
      border-color: #f00;
    }
    text {
      font-size: 30rpx;
      color: #000;
    }
    input {
      border: none;
      flex: 1;
      outline: none;
      font-size: 30rpx;
      width: 100%;
    }
  }
  .spell-foot {
    position: absolute;
    bottom: 0;
    width: 90%;
    box-sizing: border-box;
    padding-bottom: 50rpx;
    view {
      box-sizing: border-box;
    }
    ::v-deep .u-button--active {
      background-color: #e8eef5;
    }
  }
}
</style>
