<template>
  <view class="mb10rpx">
    <u-tabs
      :current="tabCurrent"
      :line-color="`url(${getSystemImg(
        '/6747e9ed0a34815816f11159/67590691f8730075fead9d71'
      )}) 100% 100%`"
      :list="tabList"
      :scrollable="true"
      line-height="8"
      line-width="40"
      @change="tabChange"></u-tabs>
  </view>
  <up-line color="#DEDEDE"></up-line>
  <view class="w-full p30rpx box-border">
    <LoadMoreList
      ref="loadMoreList"
      :request-fn="getTestHistoryList"
      :request-params="{
        filter,
      }">
      <template #default="{ list }">
        <view
          v-for="item in (list as TestHistoryItem[])"
          :key="item._id"
          class="w-690rpx my-20rpx item"
          @click="toReport(item)">
          <view class="flex items-center">
            <view class="tag"></view>
            <view class="title">
              {{ item.batchNo }}
            </view>
          </view>
          <view class="flex justify-between my30rpx">
            <view class="score">总：{{ item.totalScore }}分</view>
            <u-icon name="arrow-right" size="16"></u-icon>
          </view>
          <view class="text-#999999 text-24rpx flex justify-between">
            <view>上传时间：{{ formatDate(item.creatorTime) }}</view>
            <view>耗时：{{ formatHours(Number(item.answerTime)) }}</view>
          </view>
        </view>
      </template>
    </LoadMoreList>
  </view>
</template>

<script setup lang="ts" name="grades-report-list">
import { formatHours, getSystemImg } from '@/utils'
import LoadMoreList from '@/components/LoadMoreList.vue'
import type { TestHistoryItem } from '@/api/project/special-marking/type'
import { getTestHistoryList } from '@/api/project/special-marking/index'
import useDateFormatter from '@/hooks/useDateFormatter'
import type { FilterType } from '@/es/request'
import useUserStore from '@/store/modules/user'
const { formatDate } = useDateFormatter('YYYY-MM-DD')

const userStore = useUserStore()

const loadMoreList = ref<InstanceType<typeof LoadMoreList>>()

onReachBottom(() => {
  loadMoreList.value!.onReachBottom()
})

// 跳转报告
const toReport = (item: any) => {
  console.log(item, 'item')
  uni.navigateTo({
    url: `/subPages/exercises/test-report?reportId=${encodeURIComponent(JSON.stringify(item))}`,
  })
}

// 当前tab
const tabCurrent = ref(0)
// tab列表
const tabList = ref([
  { name: '随机刷题', id: 1 },
  { name: '章节练习', id: 2 },
  { name: '真题练习', id: 3 },
  { name: '模拟考试', id: 4 },
  { name: '专项打卡', id: 5 },
])
// 过滤条件1
const filter = ref<FilterType[]>([
  { enCode: 'type', method: 'eq', type: 'custom', value: [tabList.value[tabCurrent.value].name] },
  { enCode: 'creatorUserId', method: 'eq', type: 'systemField', value: ['currentUser'] },
])

// tab切换
const tabChange = (val: any) => {
  tabCurrent.value = val.index
  filter.value[0].value = [tabList.value[tabCurrent.value].name]
  if (tabCurrent.value === 4) {
    filter.value[1] = {
      enCode: 'userId',
      method: 'eq',
      type: 'custom',
      value: [userStore.userInfo!.id],
    }
  } else {
    filter.value[1] = {
      enCode: 'creatorUserId',
      method: 'eq',
      type: 'systemField',
      value: ['currentUser'],
    }
  }
  loadMoreList.value?.refresh()
}
</script>

<style lang="scss" scoped>
.item {
  border-bottom: 1rpx dashed #cfcfcf;
  padding-bottom: 30rpx;
  .tag {
    width: 24rpx;
    height: 28rpx;
    background: linear-gradient(90deg, #459af7 0%, rgba(255, 255, 255, 0) 100%);
  }
  .title {
    font-size: 30rpx;
    color: #333333;
  }
  .title1 {
    padding-left: 30rpx;
    font-size: 34rpx;
    color: #333333;
    background: linear-gradient(90deg, #459af7 0%, rgba(255, 255, 255, 0) 30%);
  }
  .score {
    width: 128rpx;
    height: 45rpx;
    background: rgba(69, 154, 247, 0.1);
    border-radius: 0rpx 23rpx 23rpx 0rpx;
    color: #459af7;
    font-size: 24rpx;
    text-align: center;
    line-height: 45rpx;
  }
}
</style>
