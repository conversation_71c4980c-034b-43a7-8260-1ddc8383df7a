<template>
  <view
    class="w750rpx bg-gradient-to-b from-#F2F8FF to-#F4F5F7"
    style="background: linear-gradient(179deg, #f2f8ff 0%, #f4f5f7 400rpx)">
    <!-- 试卷列表 -->
    <view
      v-for="(item, index) in paperList"
      :key="item._id"
      class="ma my-20rpx w-710rpx bg-white p-20rpx box-border rounded-20rpx">
      <!-- 题目图片 -->
      <view class="relative mb-20rpx">
        <image
          v-if="item.topicImg && item.topicImg.length > 0"
          :src="getSystemImg(item.topicImg[0].url)"
          class="w-full h-auto min-h-113rpx bg-#707070"
          mode="widthFix">
        </image>
      </view>

      <!-- 正确答案 -->
      <view class="flex items-start mb-15rpx text-28rpx text-#333333 font-bold">
        <view class="mr-20rpx whitespace-nowrap">【正确答案】</view>
        <view class="flex-1">
          <rich-text :nodes="item.correctAnswer || 'B'"></rich-text>
        </view>
      </view>

      <!-- AI阅卷结果 -->
      <view class="flex font-bold items-center m-17rpx justify-between">
        <view>
          <text class="text-26rpx text-#333333 mr-10rpx">AI阅卷结果：</text>
          <text
            class="text-26rpx mr-40rpx"
            :style="{ color: item.ai_isCorrect === '对' ? '#00aa00' : '#ff4444' }">
            {{ item.ai_isCorrect || '错误' }}
          </text>
        </view>
        <view>
          <text class="text-26rpx text-#333333 mr-10rpx">分数：</text>
          <text class="text-26rpx" :style="{ color: item.ai_score > 0 ? '#00aa00' : '#ff4444' }">
            {{ item.ai_score }}
          </text>
        </view>
      </view>

      <!-- 老师阅卷结果 -->
      <view class="flex font-bold items-center m-17rpx justify-between">
        <view>
          <text class="text-26rpx text-#333333 mr-10rpx">老师阅卷结果：</text>
          <text
            class="text-26rpx mr-40rpx"
            :style="{
              color:
                item.judges.length === 0
                  ? '#999999'
                  : item.isCorrect === '对'
                  ? '#00aa00'
                  : '#ff4444',
            }">
            {{ item.judges.length === 0 ? '未阅' : item.isCorrect || '错误' }}
          </text>
        </view>
        <view>
          <text class="text-26rpx text-#333333 mr-10rpx">分数：</text>
          <text
            class="text-26rpx"
            :style="{
              color:
                item.judges.length === 0
                  ? '#999999'
                  : (item.score || 0) > 0
                  ? '#00aa00'
                  : '#ff4444',
            }">
            {{ item.judges.length === 0 ? '-' : item.score || 0 }}
          </text>
        </view>
      </view>

      <!-- 解析展开按钮（集成在分隔线中） -->
      <view class="flex items-center my-30rpx">
        <view class="flex-1 h-2rpx bg-#e0e0e0"></view>
        <view
          class="mx-20rpx w32rpx h30rpx text-#459AF7 text-30rpx cursor-pointer transition-transform duration-300 ease-in-out relative top-[-10rpx] left-[12rpx]"
          :class="item.showAnalysis ? 'rotate-90' : 'rotate--90'"
          style="transform-origin: 5rpx 25rpx"
          @click="toggleAnalysis(item)">
          》
        </view>
        <view class="flex-1 h-2rpx bg-#e0e0e0"></view>
      </view>

      <!-- 题目解析 -->
      <view
        class="mt-20rpx px-20rpx overflow-hidden transition-all duration-300 ease-in-out"
        :class="{
          'analysis-expanded': item.showAnalysis,
          'analysis-collapsed': !item.showAnalysis,
        }">
        <!-- 解析标题 -->
        <view class="flex items-center mb-15rpx">
          <image
            :src="getSystemImg('/687758481807a96b974f9916/687f49bfcfdce7607d9cccc5')"
            class="w-29rpx h-19rpx mr-10rpx">
          </image>
          <text class="text-30rpx text-#333333 lh-58rpx font-bold">解析</text>
        </view>
        <!-- 解析内容 -->
        <view
          class="text-26rpx leading-relaxed text-#333333 whitespace-pre-wrap break-words overflow-hidden">
          <rich-text :nodes="item.topicDetail?.analysis || '暂无解析'" class="w-full"> </rich-text>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts" name="student-grading-papers">
import { getSystemImg } from '@/utils'
import { getSpecialClockInScoreDetail } from '@/api/project/clock-in'
import { getTopicListByIds } from '@/api/project/exercises'
import type { ISpecialClockInTopic } from '@/api/project/clock-in/type.d'
import type { TopicItem } from '@/api/project/exercises/type.d'

// 定义题目数据类型（扩展接口类型）
interface PaperItem extends ISpecialClockInTopic {
  correctAnswer?: string
  score?: number
  topicDetail?: TopicItem // 题目详情
  showAnalysis?: boolean // 是否展开解析
}

// 题目列表数据
const paperList = ref<PaperItem[]>([])

// 加载数据
const loadData = async (id: string) => {
  let data: any

  try {
    const response = await getSpecialClockInScoreDetail(id)
    data = response.data
  } catch (error) {
    // 处理API返回的错误，比如 {"code":400,"msg":"数据不存在"}
    console.error('获取成绩详情失败:', error)
    uni.showModal({
      title: '提示',
      content: '成绩记录还没有出来，请稍后再查看',
      showCancel: false,
      confirmText: '确定',
      success: () => {
        uni.navigateBack()
      },
    })
    return
  }

  // 检查是否有成绩记录
  if (!data.tableField102 || data.tableField102.length === 0) {
    uni.showModal({
      title: '提示',
      content: '成绩记录还没有出来，请稍后再查看',
      showCancel: false,
      confirmText: '确定',
      success: () => {
        uni.navigateBack()
      },
    })
    return
  }

  // 获取所有题目ID
  const topicIds = data.tableField102.map((item: ISpecialClockInTopic) => item.topic_id)

  // 获取题目详情
  const topicDetailsResult = await getTopicListByIds(topicIds)
  const topicDetails = topicDetailsResult.data.list as TopicItem[]

  // 处理题目数据，添加正确答案等扩展字段
  paperList.value = data.tableField102.map((item: ISpecialClockInTopic) => {
    const topicDetail = topicDetails.find(detail => detail._id === item.topic_id)

    // 预处理解析内容，为图片添加样式
    let processedTopicDetail = topicDetail
    if (topicDetail?.analysis) {
      const processedAnalysis = topicDetail.analysis.replace(
        /<img([^>]*?)>/gi,
        (match, attributes) => {
          // 检查是否已经有style属性
          if (attributes.includes('style=')) {
            // 如果已有style属性，在其中添加样式
            return match.replace(/style=["']([^"']*?)["']/gi, (_, styleContent) => {
              const newStyle = `${styleContent};width:100%;height:auto;`
              return `style="${newStyle}"`
            })
          } else {
            // 如果没有style属性，直接添加
            return `<img${attributes} style="width:100%;height:auto;">`
          }
        }
      )
      processedTopicDetail = { ...topicDetail, analysis: processedAnalysis }
    }

    return {
      ...item,
      correctAnswer: topicDetail?.answer?.[0] || '未知', // 从题目详情获取正确答案
      score: item.judges.length > 0 ? (item.isCorrect === '对' ? 10 : 0) : undefined, // 老师阅卷分数
      topicDetail: processedTopicDetail, // 题目详情（含预处理的解析）
      showAnalysis: false, // 默认不展开解析
    }
  })
}

// 切换解析显示
const toggleAnalysis = (item: PaperItem) => {
  item.showAnalysis = !item.showAnalysis
}

// 页面加载
onLoad((options: any) => {
  if (options.id) {
    loadData(options.id)
  } else {
    console.error('缺少必要参数 id')
    uni.showToast({
      title: '参数错误',
      icon: 'error',
    })
  }
})
</script>

<style>
page {
  background-color: #f2f8ff;
  padding-bottom: 20rpx;
}
</style>

<style lang="scss" scoped>
.analysis-collapsed {
  max-height: 0;
  padding-top: 0;
  padding-bottom: 0;
  margin-top: 0;
  opacity: 0;
}

.analysis-expanded {
  max-height: 1000rpx;
  padding-top: 20rpx;
  padding-bottom: 20rpx;
  opacity: 1;
}
</style>
