<template>
  <view class="user-page">
    <!-- 顶部区域S -->
    <view class="top-container">
      <view :style="{ height: `${safeTopHeight}px` }"> </view>
      <view class="header-container">
        <!-- 用户信息区域S -->

        <!-- 顶部背景 -->
        <view class="top-bg"></view>
        <!-- 未登录S -->
        <view v-if="!userInfo?.id" class="flex items-center mb-30rpx">
          <image
            :src="imagesConstants.noLoginAvatar"
            mode="aspectFit"
            class="w-120rpx h-120rpx rounded-full" />
          <view class="flex flex-col ml-30rpx">
            <view class="text-xl font-bold" @click="toLogin">立即登录 ></view>
            <view class="text-#969DAB text-28rpx mt-4rpx">登录享受更多权益</view>
          </view>
        </view>
        <!-- 未登录E -->

        <!-- 已登录区域S -->
        <view v-else class="flex justify-between mb-30rpx">
          <view class="flex items-center">
            <u-avatar
              :src="
                userInfo.headIcon ? getHeadIcon(userInfo.headIcon) : imagesConstants.noLoginAvatar
              "
              size="120rpx"></u-avatar>
            <view class="flex flex-col justify-between ml-25rpx">
              <view class="text-xl mb6rpx font-bold">{{ userInfo.realName }}</view>
              <view
                v-if="isVip"
                class="relative flex min-w-160rpx h44rpx p-15rpx box-border rounded-22rpx bg-#141615 items-center justify-center mt-8rpx"
                @click="
                  toServe({
                    url: '/subPages/user/points',
                  })
                ">
                <view
                  class="absolute top-[-10rpx] left-[-20rpx] w60rpx h60rpx rounded-30rpx bg-#141615 flex-center">
                  <image
                    :src="getSystemImg('/6747e9ed0a34815816f11159/67590525f8730075fead9d6f')"
                    class="w-56rpx h50rpx"></image>
                </view>
                <view class="text-24rpx text-#FBE1A3 pl20rpx">VIP·壹</view>
              </view>
              <view v-else class="text-24rpx">普通用户</view>
            </view>
          </view>
          <u-icon name="arrow-right" size="24" @click="toInfo"></u-icon>
        </view>
        <!-- 已登录区域E -->

        <!-- 我的积分/优惠券/邀请好友S -->
        <view v-if="userInfo?.id" class="w-full mb-30rpx flex h110rpx justify-around">
          <view
            v-for="item in tabList"
            :key="item.name"
            class="flex flex-col justify-between items-center fw-bold text-#111111"
            @click="toServe(item)">
            <view class="text-46rpx">{{ item.count }}</view>
            <view class="text-32rpx">{{ item.name }}</view>
          </view>
        </view>
        <!-- 我的积分/优惠券/邀请好友E -->

        <view
          v-if="!isVip"
          class="no-vip-container"
          @click="toServe({ url: '/subPages/user/member' })">
          <view class="flex flex-col">
            <view class="vip-word">
              <view>S</view>
              <view>VIP</view>
              <view>专享</view>
            </view>
            <view class="tip"> 开通畅享会员权益 </view>
          </view>
          <view class="open-vip">
            <view class="text-[#240D00] font-bold text-32">立即开通</view>
            <image :src="imagesConstants.openArrow" class="w-25rpx h-25rpx ml-15rpx"></image>
          </view>
        </view>
        <!-- 用户信息区域E -->
      </view>
    </view>
    <!-- 顶部区域E -->
    <!-- 功能区域S -->
    <view class="body-container">
      <!-- 报告S -->
      <!-- <view class="report-container flex justify-between gap-x-4">
        <view
          v-for="item in reportList"
          :key="item.name"
          class="relative bg-white rounded-3 p-30rpx flex-1"
          @click="toServe(item)">
          <view class="font-bold text-32 text-#111111">{{ item.name }}</view>
          <view class="mt-12rpx text-#999999 text-24">查看 ></view>
          <image class="absolute right-0 h-152 w-152 top-0" :src="item.icon"></image>
        </view>
      </view> -->
      <!-- 报告E -->

      <!-- 订单菜单按钮S -->
      <view class="menu-container">
        <view class="menu-title">会员专属</view>
        <view class="menu-list">
          <view
            v-for="item in memberBlong"
            :key="item.name"
            class="flex flex-col justify-center items-center"
            @click="toServe(item)">
            <image :src="item.icon" class="w-45rpx h-45rpx" mode="aspectFit"></image>
            <view class="text-xs mt-10rpx text-24 text-#111111">{{ item.name }}</view>
          </view>
        </view>
      </view>

      <!-- 学习菜单按钮S -->
      <view class="menu-container">
        <view class="menu-title">我的学习</view>
        <view class="menu-list">
          <view
            v-for="item in studyMenuList"
            :key="item.name"
            class="flex flex-col justify-center items-center"
            @click="toServe(item)">
            <image :src="item.icon" class="w-40rpx h-40rpx"></image>
            <view class="text-xs mt-10rpx text-24 text-#111111">{{ item.name }}</view>
          </view>
        </view>
      </view>
      <!-- 菜单按钮E -->

      <!-- 底部菜单S -->
      <view class="p-y-40rpx p-x-20rpx bg-white rounded-20rpx grid grid-cols-4 gap-40rpx">
        <view
          v-for="(item, index) in bottomMenuList"
          :key="index"
          class="flex-center flex-col"
          @click="toServe(item)">
          <image :src="item.icon" class="w-45rpx h-45rpx" mode="aspectFit"></image>
          <view class="text-24rpx text-#333333 mt-12rpx">{{ item.name }}</view>
        </view>
      </view>
      <!-- 底部菜单E -->
    </view>

    <!-- 功能区域E -->
  </view>
</template>

<script setup lang="ts" name="user-page">
import imagesConstants from '@/config/images.constants'
import useUserStore from '@/store/modules/user'
import useMemberStore from '@/store/modules/member'
import { getPointsInfo } from '@/api/project/member'
import { TargetWeChatService, getHeadIcon, getSystemImg, showToast } from '@/utils'

const userInfo = computed(() => {
  return useUserStore().userInfo
})

const pointsNum = computed(() => {
  return useMemberStore().points_num
})

const inviteList = ref<any[]>([])

const inviteNum = computed(() => {
  return inviteList.value.length
})

const isVip = computed(() => {
  return useMemberStore().isVip
})

const logout = () => {
  uni.showModal({
    title: '提示',
    content: '是否确认退出',
    success: success => {
      if (success.confirm) {
        useUserStore().userLogout()
        useMemberStore().memberLogout()
      }
    },
  })
}

// 跳转个人信息
const toInfo = () => {
  uni.navigateTo({
    url: '/subPages/user/information',
  })
}

// 跳转教师阅卷
const toGradingPapers = () => {
  const flag = true
  if (flag) {
    uni.navigateTo({
      url: '/subPages/special-marking/marking-list',
    })
  } else {
    uni.navigateTo({
      url: '/subPages/special-marking/student-grading-papers',
    })
  }
}

const tabList = ref([
  {
    name: '我的积分',
    count: pointsNum,
    url: '/subPages/user/points',
  },
  {
    name: '优惠卷',
    count: 0,
  },
  {
    name: '邀请好友',
    count: inviteNum,
    url: '/subPages/user/invite',
  },
])

// const reportList = [
//   {
//     name: '成绩查询',
//     icon: imagesConstants.personalizedReport,
//     url: '/subPages/special-marking/grades-report-list',
//   },
//   {
//     name: '学习报告',
//     icon: imagesConstants.studyReport,
//     url: '/subPages/exercises/exam-report-list',
//   },
// ]

const memberBlong = [
  {
    name: '折扣券',
    icon: imagesConstants.coupon,
    url: '',
  },
  {
    name: '积分兑换',
    icon: imagesConstants.exchange,
    url: '',
  },
  {
    name: '回馈金',
    icon: imagesConstants.reward,
    url: '',
  },
  {
    name: '个性组卷',
    icon: imagesConstants.customPaper,
    url: '',
  },
  {
    name: '押题直播',
    icon: imagesConstants.webcast,
    url: '',
  },
  {
    name: '专属名师',
    icon: imagesConstants.privateTeacher,
    url: '',
  },
]

const studyMenuList = [
  {
    name: '学情报告',
    icon: imagesConstants.examReport,
    url: '/subPages/exercises/exam-report-list',
  },
  {
    name: '升本日程',
    icon: imagesConstants.upUniversitySchedule,
    url: '/subPages/volunteer-query/schedule',
  },
  {
    name: '集训成绩',
    icon: imagesConstants.examscore,
    url: '/subPages/user/offline-exam-report',
  },
  {
    name: '在线课程',
    icon: imagesConstants.lineCourse,
    url: '/pages/course/course',
  },
  {
    name: '我的收藏',
    icon: imagesConstants.myCollection,
    url: '/subPages/exercises/my-collect',
  },
  {
    name: '我的错题',
    icon: imagesConstants.myWrong,
    url: '/subPages/exercises/my-wrong-questions/menu',
  },
  {
    name: '学习打卡',
    icon: imagesConstants.studyClock,
    url: '/pages/clock-in/clock-in',
  },
]

const bottomMenuList = [
  {
    name: '订单',
    icon: imagesConstants.order,
    url: '/subPages/order/list',
  },
  {
    name: '意见反馈',
    icon: imagesConstants.feedback,
    url: '/subPages/feedback/list',
  },
  {
    name: '在线咨询',
    icon: imagesConstants.onlineConsultation,
    func: TargetWeChatService,
  },
  {
    name: '退出登录',
    icon: imagesConstants.setting,
    func: logout,
  },
]

const toServe = (val: any) => {
  if (val.func) {
    val.func()
  } else if (val.url) {
    uni.navigateTo({
      url: val.url,
      fail: () => {
        uni.switchTab({
          url: val.url,
        })
      },
    })
  } else {
    showToast('功能开发中，敬请期待')
  }
}

function toLogin() {
  uni.navigateTo({
    url: '/subPages/user/login',
  })
}
const safeTopHeight = computed(() => {
  const menuBtnInfo = uni.getMenuButtonBoundingClientRect()
  return menuBtnInfo.bottom
})

onShow(async () => {
  useUserStore().getUserInfo()
  useMemberStore().getMemberInfo()
  inviteList.value = (await getPointsInfo('邀请用户')).data.list
})
</script>

<style lang="scss" scoped>
.user-page {
  display: flex;
  flex-direction: column;
  background: #f4f5f7;
  min-height: 100vh;
  .top-container {
    display: flex;
    flex-direction: column;
    position: relative;
    z-index: 0;
    .header-container {
      flex: 1;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      padding: 0 30rpx;
      height: 40rpx;
      // 顶部背景
      .top-bg {
        width: 100%;
        height: 633rpx;
        background: linear-gradient(231deg, #2b8efb 0%, #4edff6 100%);
        opacity: 0.2;
        position: absolute;
        top: 0;
        left: 0;
        z-index: -1;
      }
      .no-vip-container {
        box-sizing: border-box;
        background-image: url('https://kindoucloud.com/api/file/previewImage/668b9011e8661c0a3fbe0285/668ba48a0bb07d7cd6ed3588');
        background-size: 100% 100%;
        background-repeat: no-repeat;
        overflow: hidden;
        display: flex;
        justify-content: space-between;
        align-items: center;
        width: 100%;
        padding: 25rpx 40rpx;
        .vip-word {
          display: flex;
          view:first-child {
            font-size: 40rpx;
            font-weight: bold;
            color: #fae3ac;
            font-style: normal;
            text-transform: none;
          }

          view:nth-child(2) {
            background-color: #fae3ac;
            font-size: 40rpx;
            color: #000;
            font-style: italic;
            border-radius: 4rpx;
            padding-right: 15rpx;
            font-weight: bold;
            margin: 0 8rpx;
          }
          view:last-child {
            font-size: 40rpx;
            color: #fae3ac;
            font-style: normal;
            text-transform: none;
          }
        }
        .tip {
          font-weight: bold;
          font-size: 24rpx;
          color: #8a8a8a;
          margin-top: 10rpx;
        }
        .open-vip {
          display: flex;
          justify-content: center;
          align-items: center;
          width: 228rpx;
          height: 69rpx;
          background: linear-gradient(90deg, #fbe09e 0%, #fbe9cc 100%);
          border-radius: 35rpx 35rpx 35rpx 35rpx;
        }
      }
    }
  }
  .body-container {
    flex: 1;
    position: relative;
    padding: 20rpx;
    padding-top: 0;
  }
  .menu-container {
    padding: 30rpx;
    padding-bottom: 50rpx;
    margin-bottom: 20rpx;
    background-color: #fff;
    border-radius: 20rpx;
    .menu-title {
      font-size: 32rpx;
      font-weight: bold;
      color: #111111;
    }
    .menu-list {
      margin-top: 30rpx;
      display: grid;
      grid-template-columns: repeat(4, 1fr);
      gap: 40rpx;
    }
  }
  .bottom-menu-container {
    padding: 0rpx 30rpx;
    background-color: #fff;
    margin-top: 30rpx;
    border-radius: 24rpx;
    .menu-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 24rpx 0;
      border-bottom: 1px solid #e5e5e5;
      &:last-child {
        border-bottom: none;
      }
    }
  }
}
</style>
