<template>
  <view v-if="majorList.length > 0" class="p30rpx text-26rpx text-#999999">
    <text class="text-#459AF7">{{ majorName }}</text>
    可以报名的专业类别有：
  </view>
  <Item
    v-for="(item, index) in majorList"
    :key="index"
    :title="item._id.Undergraduate_type_name"
    :is-rotate="false"
    @tap="toServe(item._id.Undergraduate_type_code)" />
  <u-modal :show="showPopup" title="专业设置" @confirm="saveMajor">
    <view class="w-full">
      <u-input
        v-model="searchValue"
        font-size="28rpx"
        placeholder="请输入你的专科专业"
        shape="circle"
        border="surround"></u-input>
      <view v-if="searchValue" class="w560rpx h300rpx box-shadow rounded-10rpx">
        <scroll-view scroll-y="true" class="h300rpx" @scrolltolower="loadMore">
          <LoadMoreList
            ref="loadMoreList"
            :request-fn="getZKmajorDataList"
            :request-params="{ filter }">
            <template #default="{ list }">
              <view
                v-for="item in (list as IZKMajorData[])"
                :key="item._id"
                class="p20rpx text-#333333"
                @click="seleMajor(item.college_name)">
                <rich-text :nodes="handleLight(item.college_name)"> </rich-text>
              </view>
            </template>
          </LoadMoreList>
        </scroll-view>
      </view>
    </view>
  </u-modal>
</template>

<script setup lang="ts" name="search-list">
import Item from './Item/index.vue'
import { getCrossMajorDataList, getZKmajorDataList } from '@/api/project/index'
import type { IZKCrossMajorData, IZKMajorData } from '@/api/project/index/type'
import useUserStore from '@/store/modules/user'
import { showToast } from '@/utils'
import LoadMoreList from '@/components/LoadMoreList.vue'
import type { FilterType } from '@/es/request'

const userStore = useUserStore()

const majorName = ref('')

const majorList = ref<IZKCrossMajorData[]>([])

const toServe = (code: string) => {
  uni.navigateTo({
    url: `/subPages/index/report-major/list?code=${code}`,
  })
}

// 专业设置弹窗
const showPopup = ref(false)
const searchValue = ref('')
const loadMoreList = ref<InstanceType<typeof LoadMoreList>>()
const loadMore = () => {
  loadMoreList.value!.onReachBottom()
}
const filter = ref<FilterType[]>([
  {
    enCode: 'college_name',
    method: 'like',
    type: 'custom',
    value: [searchValue.value],
  },
])
// 专业关键词替换
const handleLight = (val: string) => {
  const query = searchValue.value
  return val.replace(query, `<span style="color:#459af7">${query}</span>`)
}

const seleMajor = (name: string) => {
  searchValue.value = name
  majorName.value = name
}
watch(searchValue, () => {
  filter.value[0].value = [searchValue.value]
})

const getData = async () => {
  const { data } = await getCrossMajorDataList(majorName.value)
  majorList.value = data
}

// 保存专业
const saveMajor = () => {
  uni.setStorageSync('majorName', searchValue.value)
  showPopup.value = false
  getData()
}

onLoad((val: any) => {
  if (val.name) {
    majorName.value = val.name
  } else {
    const major = uni.getStorageSync('majorName')
    if (major) {
      majorName.value = major
    } else {
      showPopup.value = true
    }
  }
  getData()
})
</script>

<style lang="scss">
page {
  background-color: #f4f5f7;
}
</style>
