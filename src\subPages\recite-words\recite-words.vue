<template>
  <view class="exercises-pages">
    <view class="exercises-pages-main">
      <view class="menu-container default-shadow">
        <view class="card-item" @click="checkBook">
          <view class="flex pl-20rpx">
            <view class="flex flex-center flex-col mr-34rpx">
              <image :src="imagesConstants.chooseBook" class="w-59rpx h-69rpx" />
            </view>
            <view class="">
              <view class="text-28rpx">词书选择</view>
              <view class="text-26rpx u-line-1 text-#999999 mt-6rpx">{{
                bookInfoStorage?.title || '请选择词书'
              }}</view>
            </view>
          </view>
          <view>
            <image :src="imagesConstants.gotoIcon" class="w-13rpx h-24rpx" />
          </view>
        </view>
      </view>

      <view class="exercises-options margin-t-24 u-main-color">
        <view
          v-for="(item, index) in options"
          :key="index"
          class="exercises-options-item bg-white default-shadow flex-center btn-active"
          :class="{ g3: index > 3 }"
          @tap="optionClickHandler(item)">
          <image :src="item.icon" mode="aspectFit" />
          <text>{{ item.title }}</text>
        </view>
      </view>
      <view class="menu-container default-shadow mt-20rpx">
        <template v-for="item in menuList" :key="item.name">
          <view
            class="card-item card-item1"
            @click="jumpReciteWordPage({ title: item.name, bookId })">
            <view class="card-icon flex">
              <view class="flex items-center mr-23rpx">
                <image :src="item.icon" class="w-35rpx h-35rpx" />
              </view>
              <text>{{ item.name }}</text>
            </view>
            <view class="text-#BFBFBF">
              {{ item.count }} 词
              <image :src="imagesConstants.gotoIcon" class="w-13rpx h-24rpx ml-rpx" />
            </view>
          </view>
        </template>
      </view>
    </view>

    <!-- 底部导航 -->
    <ReciteTabbar current-page="words" />
  </view>
</template>

<script setup lang="ts">
import ReciteTabbar from './components/ReciteTabbar.vue'
import {
  getLearnedReciteWordCount,
  getRecitePlan,
  getReciteWordCount,
  getUserReciteBook,
} from '@/api/project/recite-words'
import { showFailToast, showToast, toURLSearchParams } from '@/utils'
import imagesConstants from '@/config/images.constants'
import type { BookInfoType, RecitePlanResultType } from '@/api/project/recite-words/type'
import useUserStore from '@/store/modules/user'
const userReciteId = ref('')
const userInfo = computed(() => {
  return useUserStore().userInfo
})
// 缓存的词书
const bookInfoStorage = computed(() => {
  return useUserStore().bookInfo
})
// 用户词书id
const bookId = computed(() => {
  return bookInfoStorage.value?._id as string
})
// 词书单词总数
const totalCount = ref('')
// 用户计划表
const recitePlanList = ref<RecitePlanResultType[]>([])
// 分类单词查询
const jumpReciteWordPage = ({ title, bookId }: { title: string; bookId: string }) => {
  uni.navigateTo({
    url: `/subPages/recite-words/word-list?title=${title}&bookId=${bookId}`,
  })
}
// card选项
const options = [
  ['单词练习', onBookClick, imagesConstants.wordTest],
  [
    '连词造句',
    () => {
      jumpReciteWordPage({ title: '连词造句', bookId: bookId.value })
    },
    imagesConstants.sentence,
  ],
  [
    'AI对话',
    () => {
      uni.navigateTo({
        url: `/subPages/ai-chat/chat`,
      })
    },
    imagesConstants.aiChat,
  ],
  [
    '生词',
    () => {
      jumpReciteWordPage({ title: '生词', bookId: bookId.value })
    },
    imagesConstants.strange,
  ],
  [
    '熟知词',
    () => {
      jumpReciteWordPage({ title: '熟知词', bookId: bookId.value })
    },
    imagesConstants.know,
  ],
  ['我的错词', '', imagesConstants.myWrongWord],
].map(([title, path, icon]) => ({
  title,
  path,
  icon,
}))

// 学习进度列表
const menuList = ref([
  {
    name: '本书已学',
    count: 0,
    icon: imagesConstants.study,
  },
  {
    name: '本书待学',
    count: 0,
    icon: imagesConstants.preStudy,
    url: '/pages/user/setup',
  },
])

// 点击
const optionClickHandler = (item: any) => {
  if (!item.path) return showToast('前方道路施工中~', 'none')
  if (item.path instanceof Function) {
    item.path.call()
  } else {
    uni.navigateTo({
      url: `/subPages/exercises/${item.path}`,
    })
  }
}

// 每次进入页面获取用户词书
onShow(async () => {
  if (!userInfo.value) {
    return
  }
  userPlanes()
  if (bookInfoStorage.value) {
    await getCountInfo()
  }
})

// 获取用户的学习计划
function userPlanes() {
  // 获取已选计划表
  getRecitePlan()
    .then(res => {
      recitePlanList.value = res.data.list
      if (!recitePlanList.value.length) return
      return getUserReciteBook()
    })
    .then(res => {
      if (!res || res.data.list.length === 0) return
      const book = res.data.list[0]
      userReciteId.value = book._id
    })
}
// 获取用户词书数量
async function getCountInfo() {
  const bookId = bookInfoStorage.value!._id
  const result = await getReciteWordCount(bookId)
  totalCount.value = String(result.data?.[0]?.totalCount || 0)
  const learnedRes = await getLearnedReciteWordCount(bookId)
  menuList.value[0].count = Number(learnedRes.data?.[0]?.totalCount || 0)
  menuList.value[1].count = Number(totalCount.value) - Number(menuList.value[0].count)
}

// 前往选择词书
function checkBook() {
  uni.navigateTo({ url: '/subPages/recite-words/choose-book/choose-book' })
}
// 单词练习
function onBookClick() {
  const book = bookInfoStorage.value
  if (!book) {
    showFailToast('请先选择词书')
    return
  }

  const bookInfo = recitePlanList.value.find(item => item.book === book._id)
  if (bookInfo) {
    const query = {
      id: bookInfo.book,
      wordNum: bookInfo.word,
      bookName: bookInfo.bookName,
      order: bookInfo.order,
      totalDay: bookInfo.day,
      userReciteId: userReciteId.value,
    }
    uni.navigateTo({
      url: `/subPages/recite-words/word-clock?${toURLSearchParams(query)}`,
    })
  } else {
    const query = {
      id: book._id,
      title: book.title,
    }
    uni.navigateTo({
      url: `/subPages/recite-words/plan?${toURLSearchParams(query)}`,
    })
  }
}
</script>

<style lang="scss" scoped>
@import '@/common/css/index.scss';
@import '@/common/css/project.scss';

.exercises-pages {
  margin: 0;
  height: 100vh;
  background-color: #f4f5f7;
  &-main {
    padding: 0 30rpx;
    padding-top: 20rpx;
    padding-bottom: 120rpx; // 为底部导航留出空间
    .exercises-options {
      display: grid;
      grid-template-columns: repeat(12, 1fr);
      gap: 20rpx;
      &-item {
        flex-direction: column;
        border-radius: 20rpx;
        height: 184rpx;
        grid-column: span 4;
        > image {
          width: 53rpx;
          height: 53rpx;
          margin-bottom: 12rpx;
        }
        // &:not(.g3) {
        //   font-size: 26rpx;
        // }
        // &.g3 {
        //   grid-column: span 4;
        //   height: 218rpx;
        //   font-weight: bold;
        //   > image {
        //     width: 64rpx;
        //     height: 64rpx;
        //   }
        // }
      }
    }
  }
}
.menu-container {
  padding: 0rpx 31rpx;
  background-color: #fff;
  border-radius: 24rpx;
}
.card-item {
  display: flex;
  justify-content: space-between;
  background-color: #ffffff;
  align-items: center;
  height: 170rpx;
  box-sizing: border-box;
  border-bottom: 1px solid #f2f5f9;
  .card-icon {
    margin-right: 21rpx;
  }
  &:nth-last-child(1) {
    border-bottom: none;
  }
}
.card-item1 {
  height: 107rpx;
}
.card-box {
  margin-top: 20rpx;
  background: #ffffff;
  border-radius: 20rpx;
  overflow: hidden;
  padding: 0 32rpx;
  box-sizing: border-box;
  width: 710rpx;
  &:nth-child(1) {
    margin-top: 0;
  }
}
</style>
