<template>
  <view
    class="w685rpx h214rpx bg-white ma mt20rpx p40rpx box-border text-24rpx text-#333333"
    @click="onClick">
    <view class="flex items-center">
      <u-image
        :src="getSystemImg('/6747e9ed0a34815816f11159/66ac86741f1cb273f7dbebae')"
        width="36rpx"
        height="45rpx"></u-image>
      <view class="ml40rpx">{{ name || '加载中' }} </view>
    </view>
    <view class="mt30rpx">
      以上数据来源:学校官网、网报平台公示，为人工整理，仅供参考，感谢您的支持理解!
    </view>
  </view>
</template>

<script setup lang="ts" name="file-item">
import { downloadFile, getSystemImg } from '@/utils'
const { url, name } = defineProps<{
  name: string
  url: string
}>()
function onClick() {
  downloadFile(url, name)
}
</script>

<style lang="scss" scoped></style>
