<template>
  <view class="w-full bg-white relative z-9999 pb10rpx overflow-hidden">
    <SearchInput placeholder="请输入招生院校" @on-search="onSearch" />
  </view>
  <FoldPage
    :tags="tags"
    :current-tag="currentTag"
    :center-tag="seleSchoolName"
    :left-tag="seleYear"
    :right-tag="sort"
    @click-tags="clickTags"
    @click-tag="clickTag" />
  <view class="p20rpx box-border">
    <MajorTable :titles="titles" :list="scoreData" />
  </view>
</template>

<script setup lang="ts" name="score-list">
import SearchInput from '../components/SearchInput/index.vue'
import FoldPage from '../school/FoldPage/index.vue'
import MajorTable from './MajorTable/index.vue'
import { getBKSchoolMajorDataList } from '@/api/project/index'
import type { IBKSchoolMajorData } from '@/api/project/index/type'

// 总本科招生数据
const majorList = ref<IBKSchoolMajorData[]>([])

// 条件
const seleYear = ref(`${new Date().getFullYear()}年`)
const seleSchoolName = ref('')
const sort = ref('按学校排序')
const searchValue = ref('')

const currentTag = ref('')
const tags = ref([
  {
    name: '招生年份',
    key: 'sum_year',
    tags: [],
  },
  {
    name: '学校名称',
    key: 'school_name',
    tags: [],
  },
  {
    name: '排序方式',
    key: 'sort',
    tags: ['按学校排序', '按分数排序'],
  },
])

const clickTags = (key: string) => {
  if (currentTag.value === key) {
    currentTag.value = ''
    return 0
  }
  currentTag.value = key
}

const clickTag = (key: string, val: string) => {
  switch (key) {
    case 'sum_year':
      if (seleYear.value === val) {
        seleYear.value = ''
        break
      }
      seleYear.value = val
      break
    case 'school_name':
      if (seleSchoolName.value === val) {
        seleSchoolName.value = ''
        break
      }
      seleSchoolName.value = val
      break
    case 'sort':
      if (sort.value === val) {
        sort.value = ''
        break
      }
      sort.value = val
      break
  }
  currentTag.value = ''
}

const onSearch = (val: string) => {
  searchValue.value = val
}

const titles = ref(['学校名称', '专业名称', '录取分数'])

const scoreData = computed(() => {
  let list = majorList.value
  if (seleYear.value !== '') {
    list = list.filter(item => item.sum_year === seleYear.value)
  }
  if (seleSchoolName.value !== '') {
    list = list.filter(item => item.Undergraduate_name === seleSchoolName.value)
  }

  if (searchValue.value !== '') {
    list = list.filter(item => item.school_name.includes(searchValue.value))
  }
  const data = list.map(({ school_name, Undergraduate_name, score }) => {
    const formatScore = !score || score === '—' ? '0' : score
    return [school_name, Undergraduate_name, score]
  })
  if (sort.value === '按学校排序') {
    return data
  } else {
    return data.sort((x, y) => parseInt(y[2]) - parseInt(x[2]))
  }
})
onLoad(async () => {
  majorList.value = (await getBKSchoolMajorDataList()).data.list
  tags.value[0].tags = [...new Set(majorList.value.map(item => item.sum_year))]
  tags.value[1].tags = [...new Set(majorList.value.map(item => item.school_name))]
  seleYear.value = tags.value[0].tags.at(-1) || `${new Date().getFullYear()}年`
})
</script>

<style lang="scss" scoped></style>
