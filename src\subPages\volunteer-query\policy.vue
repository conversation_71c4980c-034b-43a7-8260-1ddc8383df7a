<template>
  <view class="w-full p20rpx box-border">
    <view class="text-34rpx text-center fw-bold">
      {{ detail?.title }}
    </view>
    <view class="flex m-y-20rpx justify-between text-#969DAB text-26rpx">
      <view>来源： {{ detail?.source }}</view>
      <view>发布日期： {{ formatDate(detail?.release_time) }} </view>
    </view>
    <!-- <rich-text :nodes="getValue(detail?.content)" @click="showImg"> </rich-text> -->
    <mpHtml :content="getValue(detail?.content)"></mpHtml>
  </view>
</template>

<script setup lang="ts" name="index-policy">
import mpHtml from '@/components/mp-html/mp-html.vue'
import { getSbPolicyData } from '@/api/project/index'
import type { ISbPolicyData } from '@/api/project/index/type'
import useDateFormatter from '@/hooks/useDateFormatter'

const { formatDate } = useDateFormatter('YYYY-MM-DD')

const detail = ref<ISbPolicyData>()

const getValue = (str?: string) => {
  return str?.replaceAll('img', 'img class="imggg"')
}

const showImg = (val: any) => {
  console.log(val)
}

onLoad(async (val: any) => {
  detail.value = (await getSbPolicyData(val.id)).data
})
</script>

<style>
page {
  background-color: #f4f5f7;
}
</style>

<style lang="scss" scoped>
::v-deep .imggg {
  margin-top: 20rpx;
  width: 100%;
  height: 100%;
}
</style>
