//处理大赛首页的数据的数据逻辑类型标注
export interface ContestListType {
  _id: string
  image: ContestListImage[]
  creatorUserId: string
  competitionEnCode: string
  organizer: string
  creatorTime: number
  title: string
  tableField118: ContestListTableField[]
  tags: string[]
  info: string
  lastModifyTime: number
  lastModifyUserId: ContestListLastModifyUserId
}

export interface ContestListLastModifyUserId {
  id: string
  phone: string
  fullName: string
}

export interface ContestListTableField {
  scoreType: string
  team: number
  title: string
  type: string
  way: string
  tags: string[]
  enCode: string
  topicId: string
  enrollTime: number[]
  result_com_type: string
  startTime: number[]
  endTime: number
  _id: string
  submitTimes: number
  total_scoure_type: string
  info: string
  annexInfo: string
}

export interface ContestListImage {
  uid: number
  name: string
  url: string
  status: string
}
//处理大赛首页的数据的数据逻辑类型标注

//处理团队信息的数据逻辑类型标注
export interface ParticipatingTeamsList {
  list: List[]
  pagination: Pagination
}

interface Pagination {
  currentPage: number
  pageSize: number
  total: number
}

interface List {
  _id: string
  enCode: string
  creatorUserId: string
  introduce: string
  name: string
  captain: any[]
  creatorTime: number
  teamEnCode: string
  lastModifyTime?: number
  lastModifyUserId?: string
}
//处理团队信息的数据逻辑类型标注

// 详细资讯的数据逻辑类型标注
export interface CompetitionNews {
  _id: string
  creatorUserId: CreatorUserId
  member: string
  description: string
  creatorTime: number
  title: string
  image: image[]
  shortDescription: string
  isDisplayed: number // 0:不显示 1:显示
}
export interface CreatorUserId {
  id: string
  headIcon: string
  phone: string
  fullName: string
}
interface image {
  uid: number
  name: string
  url: string
  status: string
}
