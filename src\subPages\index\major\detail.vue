<template>
  <view class="major-details-header bg-white margin-b-24">
    <!-- 专业详情 -->
    <view class="major-name text_ellipsis">{{ detail?.major_name }}</view>
    <view class="tags-box margin-t-24">
      <view v-for="(val, i) in splitArray(detail?.degree_type)" :key="i" class="tags-item">
        {{ val }}
      </view>
    </view>
    <!-- 专业数据面板 -->
    <view class="major-panel">
      <view
        v-for="(item, i) in panelist"
        :key="i"
        class="major-panel-item padding-x-24 flex justify-between items-center">
        <u-image :src="getSystemImg(item.icon)" width="96rpx" height="96rpx" />
        <view class="major-note overflow-hidden text-right">
          <view class="text_ellipsis">{{ getValue(item.value) }}</view>
          <view class="text-note">{{ item.name }}</view>
        </view>
      </view>
    </view>
  </view>
  <view class="bg-white mt20rpx">
    <up-tabs
      :list="tabs"
      :item-style="{ width: '350rpx', height: '100rpx' }"
      :line-width="40"
      :line-height="6"
      :current="tabIndex"
      :activeStyle="{ color: '#459AF7', 'font-weight': '700' }"
      :inactiveStyle="{ color: '#333333' }"
      @change="tabChange"></up-tabs>
  </view>
  <swiper class="bg-white text-24rpx text-#969DAB" :current="tabIndex" @change="swiperChange">
    <swiper-item item-id="111" class="pt25rpx pb37rpx pl52rpx pr33rpx box-border">
      {{ detail?.major_intro }}
    </swiper-item>
    <swiper-item item-id="222" class="pt25rpx pb37rpx pl52rpx pr33rpx box-border">{{
      detail?.train_objective
    }}</swiper-item>
    <swiper-item item-id="333" class="pt25rpx pb37rpx pl52rpx pr33rpx box-border">{{
      detail?.core_curriculum
    }}</swiper-item>
  </swiper>
  <up-sticky>
    <view class="bg-white mt20rpx sticky">
      <up-tabs
        :list="tabs1"
        :item-style="{ width: '350rpx', height: '100rpx' }"
        :line-width="40"
        :line-height="6"
        :current="tabIndex1"
        @change="tabChange1"></up-tabs>
    </view>
  </up-sticky>

  <swiper :style="`height:${swiperHeight}px`" :current="tabIndex1" @change="swiperChange1">
    <!-- 招生院校 -->
    <swiper-item>
      <view>
        <Section hei="30" title="招生院校" />
        <SchoolItem
          v-for="(item, index) in schoolList"
          :key="index"
          :item-data="item.school_info[0]" />
        <u-empty v-if="schoolList.length === 0" text="暂无可报院校"></u-empty>
        <view class="text-#FC3838 p10rpx p-x-20rpx text-left" style="font-size: 18rpx">
          招生计划和网报数据来源于网报平台（2023年网报数据为网报平台3月26日12:00显示数
          据），仅供参考
        </view>
      </view>
    </swiper-item>
    <!-- 考研方向 -->
    <swiper-item>
      <view>
        <Section hei="30" title="考研方向" />
        <view class="flex flex-wrap p20rpx">
          <view
            v-for="(ite, index) in splitArray(detail?.graduate_exam_direction)"
            :key="index"
            class="m10rpx">
            <up-tag :text="ite" bg-color="#feefd7" plain size="mini" type="warning"></up-tag>
          </view>
        </view>
      </view>
    </swiper-item>
    <!-- 就业去向 -->
    <swiper-item>
      <view>
        <Section hei="30" title="就业去向" />
        <view class="flex flex-wrap p20rpx">
          <view
            v-for="(ite, index) in splitArray(detail?.employment_direction)"
            :key="index"
            class="m10rpx">
            <up-tag :text="ite" bg-color="#feefd7" plain size="mini" type="warning"></up-tag>
          </view>
        </view>
      </view>
    </swiper-item>
  </swiper>
</template>

<script setup lang="ts" name="major-detail">
import SchoolItem from './SchoolItem/index.vue'
import Auchor from '@/components/Auchor.vue'
import Section from '@/components/Section.vue'
import { getBKMajorQuerySchool, getBkMajorData } from '@/api/project/index'
import type { IBKMajorData, IBKSchoolData } from '@/api/project/index/type'
import { getSystemImg, splitArray } from '@/utils'

interface IMajorSchool {
  school_info: IBKSchoolData[]
}

const detail = ref<IBKMajorData>()
const schoolList = ref<IMajorSchool[]>([])

const panelist = ref({
  major_code: {
    name: '专业代码',
    icon: '687758481807a96b974f9916/6878729ccfdce7607d9cc132',
    value: 'major_code',
  },
  study_term: {
    name: '学习年限',
    icon: '687758481807a96b974f9916/6878729ccfdce7607d9cc134',
    value: 'study_term',
  },
  degree_type: {
    name: '授予学位',
    icon: '687758481807a96b974f9916/6878729ccfdce7607d9cc136',
    value: 'degree_type',
  },
  average_salary: {
    name: '平均薪资(参考)',
    icon: '687758481807a96b974f9916/6878729ccfdce7607d9cc135',
    value: 'average_salary',
  },
})

const getValue = (key: string) => {
  if (detail.value !== undefined) {
    return splitArray((detail.value as any)[key])?.[0]
  }
}

// tabs导航
const tabs = ref([
  {
    id: 1,
    name: '专业介绍',
  },
  {
    id: 2,
    name: '培养目标',
  },
  {
    id: 3,
    name: '核心课程',
  },
])
// 当前tab
const flag = ref(true)
const tabIndex = ref(0)
const tabChange = (val: any) => {
  tabIndex.value = val.index
}
const swiperChange = (val: any) => {
  tabIndex.value = val.detail.current
}

// tabs1导航
const tabs1 = ref([
  {
    id: 1,
    name: '招生院校',
    url: '#school',
    top: 0,
  },
  {
    id: 2,
    name: '考研方向',
    url: '#kaoyan',
    top: 0,
  },
  {
    id: 3,
    name: '就业去向',
    url: '#obtain',
    top: 0,
  },
])
const tabIndex1 = ref(0)
const tabChange1 = (val: any) => {
  tabIndex1.value = val.index
  // 不再使用页面滚动，直接切换swiper
}
// 保留锚点引用但简化逻辑
const auchor1 = ref<InstanceType<typeof Auchor>>()
const auchor2 = ref<InstanceType<typeof Auchor>>()
const auchor3 = ref<InstanceType<typeof Auchor>>()

const toId = (id: number, val: any) => {
  tabs1.value[id].top = val
}

const swiperChange1 = (val: any) => {
  tabIndex1.value = val.detail.current
}
const swiperHeight = ref(280)

watchEffect(() => {
  if (tabIndex1.value === 1 || tabIndex1.value === 2) {
    swiperHeight.value = 280
  } else {
    swiperHeight.value = schoolList.value.length === 0 ? 280 : 100 * schoolList.value.length + 160
  }
})

onLoad(async (val: any) => {
  detail.value = (await getBkMajorData(val.name)).data.list[0]
  schoolList.value = (await getBKMajorQuerySchool(val.code)).data
})
</script>

<style>
page {
  background-color: #f4f5f7;
  padding-bottom: 30rpx;
}
</style>

<style lang="scss" scoped>
@import '../../../common/css/project.scss';
@import '../../../common/css/index.scss';
.major-details {
  &-header {
    padding: 40rpx;
    box-sizing: border-box;
    .major-name {
      font-size: 34rpx;
      font-weight: bold;
    }
    .major-panel {
      margin-top: 20rpx;
      display: grid;
      grid-template-columns: 48% 48%;
      grid-gap: 30rpx;
      &-item {
        background-color: #f9f9f9;
        border-radius: 10rpx;
        height: 136rpx;
        .major-note {
          margin-left: 10rpx;
          > view:first-child {
            font-size: 34rpx;
            font-weight: bold;
            margin-bottom: 16rpx;
          }
        }
      }
    }
  }
}
</style>
