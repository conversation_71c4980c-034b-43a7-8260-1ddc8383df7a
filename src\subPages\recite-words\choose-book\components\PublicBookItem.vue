<template>
  <view class="book-card" :class="{ 'book-card-selected': isSelected }" @tap="handleClick">
    <view class="book-card-content">
      <view class="book-image">
        <image
          :src="
            getHeadIcon(bookInfo.cover && bookInfo.cover.length > 0 ? bookInfo.cover[0].url : '')
          "
          class="book-cover"
          mode="aspectFill">
        </image>
      </view>
      <view class="book-info">
        <view class="book-title" :style="{ color: isSelected ? '#459AF7' : '#333' }">
          {{ bookInfo.title }}
        </view>
        <view>
          <view v-if="bookInfo.tags && bookInfo.tags !== ''" class="book-tags">
            {{ bookInfo.tags }}
          </view>
        </view>
        <!-- 词书卡片项底部 -->
        <view class="flex items-center justify-between">
          <!-- 进度条 -->
          <view class="flex-1 mr-20rpx">
            <u-line-progress
              :percentage="progress || 0"
              height="8rpx"
              active-color="#459AF7"
              :showText="false"
              inactive-color="rgba(69, 154, 247, 0.10)" />
          </view>
          <!-- 词书操作图标 -->
          <view class="w50 text-center" @tap.stop="handleAction">
            <image
              class="w-6rpx h26rpx"
              :src="getSystemImg('687758481807a96b974f9916/688353f8cfdce7607d9cd8ed')"
              mode="scaleToFill" />
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts" name="public-book-item">
import { getHeadIcon, getSystemImg } from '@/utils'
import type { BookInfoType } from '@/api/project/recite-words/type'

interface Props {
  bookInfo: BookInfoType
  progress?: number
  isSelected?: boolean
}

interface Emits {
  (e: 'click', book: BookInfoType): void
  (e: 'action', book: BookInfoType): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

function handleClick() {
  emit('click', props.bookInfo)
}

function handleAction() {
  emit('action', props.bookInfo)
}
</script>

<style lang="scss" scoped>
.book-card {
  margin: 20rpx auto;
  width: 690rpx;
  background: #fff;
}

.book-card-content {
  display: flex;
  padding-bottom: 20rpx;
}

.book-image {
  width: 220rpx;
  height: 150rpx;
  border-radius: 8rpx;
  overflow: hidden;
  margin-right: 20rpx;
}

.book-cover {
  width: 100%;
  height: 100%;
}

.book-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.book-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 10rpx;
}

.book-tags {
  font-size: 22rpx;
  color: #537eef;
  margin-bottom: 10rpx;
  display: inline-block;
  background-color: #e9f6fe;
  padding: 1rpx 8rpx;
  border-radius: 5rpx;
}
</style>
