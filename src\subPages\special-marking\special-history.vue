<template>
  <LoadMoreList :request-fn="getSpecialClockInHistoryList">
    <template #default="{ list }">
      <view class="w-full p30rpx box-border">
        <view
          v-for="item in (list as ISpecialClockIn[])"
          :key="item._id"
          class="w-690rpx my-20rpx item"
          @click="toDetail(item._id as string)">
          <view class="title">
            {{ item.grade + item.subject + formatDate(item.creatorTime, 'MM.DD') }}
          </view>
          <view class="flex justify-between my8rpx">
            <view></view>
            <u-icon name="arrow-right" size="14"></u-icon>
          </view>
          <view class="text-#999999 text-24rpx">
            上传时间：{{ formatDate(item.creatorTime) }}
          </view>
        </view>
      </view>
    </template>
  </LoadMoreList>
</template>

<script setup lang="ts" name="special-history">
import LoadMoreList from '@/components/LoadMoreList.vue'
import { getSpecialClockInHistoryList } from '@/api/project/clock-in'
import type { ISpecialClockIn } from '@/api/project/clock-in/type'
import useDateFormatter from '@/hooks/useDateFormatter'

const { formatDate } = useDateFormatter()

const loadModeList = ref<InstanceType<typeof LoadMoreList>>()

onReachBottom(() => {
  loadModeList.value?.onReachBottom()
})

const toDetail = (id: string) => {
  uni.navigateTo({
    url: `/subPages/special-marking/student-grading-papers?id=${id}`,
  })
}
</script>

<style lang="scss" scoped>
.item {
  border-bottom: 1rpx dashed #cfcfcf;
  padding-bottom: 30rpx;
  .title {
    padding-left: 34rpx;
    font-size: 34rpx;
    color: #333333;
    background: linear-gradient(90deg, #459af7 0%, rgba(255, 255, 255, 0) 30%);
  }
}
</style>
