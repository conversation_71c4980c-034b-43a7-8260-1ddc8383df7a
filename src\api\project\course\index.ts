import type * as CourseType from './type.d'
import { createModelData, getModelDataDetail, getModelList } from '@/api/visual'
import type { FilterType } from '@/es/request'

// 获取所有课程列表
export const getCourseList = ({ filter = [] as FilterType[] }) => {
  return getModelList<CourseType.ICourseItemResultType>(
    {
      menuId: '66987748c523aa5a70e0a329',
      association: false,
      currentPage: 1,
      pageSize: -1,
      filter,
    },
    {
      unAuth: true,
    }
  )
}

// 分页获取课程列表
export const getPageCourseList = ({ pageSize = 20, currentPage = 1, filter = [] }) => {
  return getModelList<CourseType.ICourseItemResultType>(
    {
      menuId: '66987748c523aa5a70e0a329',
      association: false,
      currentPage,
      pageSize,
      connect: 'and',
      filter,
    },
    {
      unAuth: true,
    }
  )
}
// 获取课程详情
export const getCourseData = (id: string) => {
  return getModelDataDetail<CourseType.ICourseItemResultType>({
    menuId: '66987748c523aa5a70e0a329',
    _id: id,
  })
}

// 获取课程目录
export const getDirecToryList = ({ _id = '' }) => {
  return getModelList<CourseType.ICourseDirecToryResuleType>({
    menuId: '669878b8c523aa5a70e0a32d',
    currentPage: 1,
    pageSize: 10,
    filter: [
      {
        enCode: 'course_id',
        method: 'eq',
        type: 'custom',
        value: [_id],
      },
    ],
    association: false,
  })
}

// 新增or修改播放历史记录
export const updateCourseDirecTime = (params: CourseType.ICatalogRequestData) => {
  return createModelData(
    {
      menuId: '66987b8dc523aa5a70e0a334',
      data: JSON.stringify(params),
    },
    {
      unMessage: true,
    }
  )
}

// 获取章节播放历史记录
export const getHistoryCourseTime = (id: string) => {
  return getModelList<CourseType.ICatalogHistoryData>({
    association: false,
    connect: 'and',
    currentPage: 1,
    pageSize: 20,
    menuId: '66987b8dc523aa5a70e0a334',
    userInfoConvert: true,
    filter: [
      {
        enCode: 'creatorUserId',
        method: 'eq',
        type: 'systemField',
        value: ['currentUser'],
      },
      {
        enCode: 'catalog_id',
        method: 'eq',
        type: 'custom',
        value: [id],
      },
    ],
  })
}

// 新增课程评论
export const addCourseComMentData = (params: CourseType.ICourseCommentRequestData) => {
  return createModelData({
    menuId: '66987a33c523aa5a70e0a32f',
    data: JSON.stringify(params),
  })
}

// 获取课程评论列表
export const getCourseComMentData = ({ id = '' }) => {
  return getModelList<CourseType.ICourseCommentItemData>({
    menuId: '66987a33c523aa5a70e0a32f',
    userInfoConvert: true,
    currentPage: 1,
    pageSize: 10,
    association: false,
    filter: [
      {
        enCode: 'course_no',
        method: 'eq',
        type: 'custom',
        value: [id],
      },
    ],
    sort: { creatorTime: 'desc' },
  })
}

// 获取资料列表
export const getCourseInfomationData = (id: string) => {
  return getModelList<CourseType.ICourseInfomationType>({
    menuId: '669a2f37c523aa5a70e0a39f',
    userInfoConvert: true,
    currentPage: 1,
    pageSize: -1,
    association: false,
    filter: [
      {
        enCode: 'course_id',
        method: 'eq',
        type: 'custom',
        value: [id],
      },
    ],
    sort: { creatorTime: 'desc' },
  })
}

// 获取当前课程的推荐课程列表
export const getCourseRecommendData = (id: string) => {
  return getModelList<CourseType.ICourseRecommendType>({
    menuId: '66987bc3c523aa5a70e0a336',
    currentPage: 1,
    pageSize: -1,
    association: false,
    filter: [
      {
        enCode: 'select_course',
        method: 'eq',
        type: 'custom',
        value: [id],
      },
    ],
    sort: { creatorTime: 'desc' },
  })
}
