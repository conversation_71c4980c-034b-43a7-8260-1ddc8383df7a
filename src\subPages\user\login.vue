<template>
  <view class="login flex flex-col justify-center items-center">
    <view class="w-full">
      <view class="login-header w-full">
        <image :src="imagesConstants.loginImage" mode="widthFix" class="w-full" />
      </view>
      <view class="login-body">
        <view class="login-buttons margin-t-24">
          <button
            class="bg-#459af7 text-white"
            open-type="getPhoneNumber"
            @getphonenumber="wxLogin">
            手机号快捷登录
          </button>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import useUserStore from '@/store/modules/user'
import imagesConstants from '@/config/images.constants'
import { showLoading } from '@/utils'
import { switchCorp } from '@/api/permission'
import { getCorpList, getCurrentUserCorpList, joinCorpById, login } from '@/api/user'
import useMemberStore from '@/store/modules/member'
import { getPointsInfo } from '@/api/project/member'
onMounted(() => {})

async function wxLogin(res: { detail: { code: string } }) {
  showLoading('登录中')
  const inviteUserId = uni.getStorageSync('InviteCode')
  console.log(!inviteUserId ? { code: res.detail.code } : { code: res.detail.code, inviteUserId })
  login(!inviteUserId ? { code: res.detail.code } : { code: res.detail.code, inviteUserId })
    .then(async res => {
      useUserStore().setToken(res.data.token)

      const { VITE_TENANT_ID, VITE_INVITE_CODE } = import.meta.env

      // 登录成功前的租户校验
      const corpList = await getCorpList() // 机构小程序
      const getCorpListRes = await getCurrentUserCorpList().catch(() => {})
      uni.setStorageSync('corpId', getCorpListRes?.data?.corpId || '')

      const currentUserCorp = getCorpListRes?.data?.corpId || ''
      const institutionCorpId = corpList.data.map(o => o.corpId)
      const availableCorp = [...institutionCorpId, VITE_TENANT_ID]
      const { corpList: apiCorpList = [], joinCorpList = [] } = getCorpListRes?.data || {}
      // 用户加入到全部租户
      const userAllCorp = [...apiCorpList, ...joinCorpList]

      // 没有机构租户 也没有加入 主体租户 则添加进主体租户 并且切换到主体
      const hasAvailableCorp = userAllCorp.find(o => availableCorp.includes(o.corpId))
      if (!hasAvailableCorp) {
        await joinCorpById(VITE_INVITE_CODE)
        await switchCorp(VITE_TENANT_ID)
      } else {
        // 如果有加入 主体 或者 机构
        // 当前租户是否为机构租户
        const isInstitution = institutionCorpId.includes(currentUserCorp)
        // 当前租户是否为主体租户
        const isMain = currentUserCorp === VITE_TENANT_ID

        // 是否加入了机构租户 - 查找第一个机构租户
        const institutionCorp = institutionCorpId.find(o =>
          userAllCorp.map(o => o.corpId).includes(o)
        )
        console.log(institutionCorpId, userAllCorp)

        // 是否加入了主体租户 - 查找主体租户
        const mainCorp = userAllCorp.find(o => o.corpId === VITE_TENANT_ID)
        console.log(isInstitution, isMain, institutionCorp, mainCorp)

        // 优先切换机构租户
        if (!isInstitution && institutionCorp) {
          await switchCorp(institutionCorp)
        } else if (!isMain && mainCorp && !isInstitution) {
          await switchCorp(mainCorp.corpId)
        }
      }

      useUserStore().getUserInfo()

      // 判断是否是新用户
      const pointsUserInfo = await getPointsInfo('新用户')
      // 判断是否是第一次邀请用户
      const pointsInviteInfo = await getPointsInfo('邀请用户')
      const inviteUserInfo = pointsInviteInfo.data.list.filter(item => {
        return item.user[0].id === inviteUserId
      })

      if (pointsUserInfo.data.list.length === 0) {
        useMemberStore().pointsAction('新用户')
      }

      uni.removeStorageSync('InviteCode')
      if (inviteUserId && inviteUserInfo.length === 0) {
        useMemberStore().pointsAction('邀请用户', 0, '', inviteUserId)
      }
      uni.$u.toast('登录成功')
      setTimeout(() => {
        uni.switchTab({ url: '/pages/index/index' })
      }, 800)
    })
    .catch(err => {
      uni.$u.toast('登录失败')
      console.log(err)
    })
}
</script>

<style lang="scss" scoped>
.login {
  background: #f5f7fb;
  height: 96vh;
  overflow: auto;

  &-wrap {
    height: 100%;
  }

  &-header > image {
    margin-top: -120rpx;
    width: 100%;
  }

  &-buttons {
    margin-top: 64rpx;

    > button {
      width: 80%;
      padding: 4rpx 0;
    }

    .login-phone {
      margin-bottom: 48rpx;
      background: #1959f6;
    }
  }

  ::v-deep .agreement-box {
    position: absolute;
    bottom: 32rpx;
  }
}
</style>
