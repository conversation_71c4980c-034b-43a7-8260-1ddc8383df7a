<template>
  <view class="exercises-pages container">
    <view class="exercises-pages-main">
      <view class="user-info flex items-center mb-12rpx px20rpx">
        <u-avatar size="90rpx" :src="getImgByUrl(userInfo?.headIcon || '')" mode="circle" />
        <b class="user-name margin-l-24 color-#333333 ml-20rpx text-32rpx">
          {{ userInfo?.realName || '请先登录' }}
        </b>
      </view>
      <view class="out-time color-white flex items-center justify-between text-24rpx">
        <view>距离{{ examYear }}年湖南专升本考试</view>
        <view>
          倒计时: <b style="margin: 0 12rpx">{{ daysCount }}</b> 天
        </view>
      </view>
      <view class="info-panel bg-white u-main-color default-shadow">
        <view class="font-bold">每日统计</view>
        <view class="flex info-line padding-20 divider divider-dashed">
          <view class="flex-1">
            <view class="text-center font-size-24">坚持天数</view>
            <view class="data-line text-center">
              <text class="value">{{ totalDays }}</text>
              <text class="unit font-size-22">天</text>
            </view>
          </view>
          <view class="flex-1">
            <view class="text-center font-size-24">刷题正确率</view>
            <view class="data-line text-center">
              <text class="value">{{ accuracy }}</text>
              <text class="unit font-size-22">%</text>
            </view>
          </view>
        </view>
      </view>
    </view>
    <view class="m-y-40rpx pl-20rpx flex justify-between items-center">
      <up-tabs
        :list="subjectTabs"
        line-width="0"
        line-color="#f56c6c"
        :active-style="{
          color: '#459AF7',
          fontWeight: 'bold',
          transform: 'scale(1.2)',
          transition: 'transform 0.3s ease-in-out',
        }"
        :inactive-style="{
          color: '#606266',
          transform: 'scale(1)',
          transition: 'transform 0.3s ease-in-out',
          fontSize: '28rpx',
        }"
        item-style="padding-left: 15px; padding-right: 15px; height: 34px;"
        @change="debouncedHandleSubjectChange">
      </up-tabs>
      <view class="p-r-20rpx">
        <!-- <up-switch v-model="arrange"></up-switch> -->
      </view>
    </view>
    <view class="p-x-20rpx">
      <TabContainer
        :current-subject-module="currentSubjectModule"
        :current-subject="currentSubject"
        :arrange="arrange" />
    </view>
  </view>
</template>

<script setup lang="ts">
import TabContainer from './components/TabContainer.vue'
import { useDebounce } from '@/hooks/useDebounce'
import { getImgByUrl, showToast } from '@/utils/index'
import useUserStore from '@/store/modules/user'
import {
  getDaysAndAccuracy,
  getExamTime,
  getSubject,
  getSubjectModule,
} from '@/api/project/exercises/index'

// 用户信息
const userInfo = computed(() => {
  return useUserStore().userInfo
})
const accuracy = ref(0)
const totalDays = ref(0)
const daysCount = ref(0)
const examYear = ref(0)
const subjectModule = ref()
// 科目模块
const subjectTabs = computed(() => {
  return subjectModule.value?.map((item: any) => ({
    name: item.subject,
    value: item.subject,
  }))
})
// 当前科目的模块
const currentSubjectModule = ref()
const currentSubject = ref()
const arrange = ref(false)

const debouncedHandleSubjectChange = useDebounce(handleSubjectChange, 300)

onShow(() => {
  getSubjectInfo()
  if (userInfo.value) {
    getDaysAndAccuracyInfo()
    getExamTimeInfo()
  } else {
    showToast('请先登录')
  }
})
// 科目切换
async function handleSubjectChange(currentItem: any) {
  if (currentItem.value === currentSubject.value) {
    return
  }
  currentSubject.value = currentItem.value
  console.log(currentSubject.value)
  await getModuleInfo(currentItem.value)
}
// 获取天数和正确率
function getDaysAndAccuracyInfo() {
  getDaysAndAccuracy().then(({ data }) => {
    totalDays.value = data[0]?.totalDays || 0
    accuracy.value = data[0]?.accuracy || 0
  })
}
// 获取考试时间
function getExamTimeInfo() {
  getExamTime().then(({ code, data }) => {
    const year = new Date(data.list[0].exam_time).getFullYear()
    const examTime = new Date(data.list[0].exam_time) // 获取考试时间
    const currentTime = new Date() // 获取当前时间
    // 计算时间差（毫秒）
    const timeDifference = examTime.getTime() - currentTime.getTime()
    // 将时间差转换为天数 // 1天 = 1000毫秒 * 3600秒 * 24小时
    const daysRemaining = Math.ceil(timeDifference / (1000 * 3600 * 24))
    daysCount.value = daysRemaining
    examYear.value = year
  })
}

// 查询专升本科目模块
async function getSubjectInfo() {
  const subject = await getSubject()
  subjectModule.value = subject.data.list[0].tableField107
  currentSubject.value = subject.data.list[0].tableField107[0].subject
  // 获取科目模块
  getModuleInfo(subject.data.list[0].tableField107[0].subject)
}
// 获取科目模块
async function getModuleInfo(subject: string) {
  const Modules = await getSubjectModule('精选刷题', subject)
  currentSubjectModule.value = Modules.data.list
}
</script>

<style lang="scss" scoped>
@import '@/common/css/project.scss';

.exercises-pages {
  background: linear-gradient(to bottom, #f8f8f8, #f4f5f7);

  &-header {
    padding: 0 50rpx;

    .user-info {
      margin-top: 12rpx;

      .user-name {
        font-size: 32rpx;
        letter-spacing: 3rpx;
      }
    }
  }

  &-main {
    padding: 0 20rpx;

    .out-time {
      margin: 0 20rpx;
      padding: 0 30rpx;
      height: 68rpx;
      background: #459af7;
      border-radius: 20rpx 20rpx 0 0;

      > view {
        font-size: 24rpx;
        display: flex;
        line-height: 30rpx;
      }
    }

    .info-panel {
      padding: 30rpx 20rpx;
      border-radius: 20rpx;
      border: none !important;

      .data-line > .value {
        font-size: 50rpx;
        line-height: 8 0rpx;
        padding-right: 8rpx;
      }
    }
  }
}
</style>
