<template>
  <u-input
    :custom-style="style"
    :value="innerValue"
    :font-size="fontSize"
    :placeholder="placeholder"
    :disabled="disabled"
    :shape="shape"
    :border="border"
    :clearable="clearable"
    @change="handleInput"
    @confirm="handleSearchClick('confirm')">
    <template #[iconPosition]>
      <u-icon class="search-icon" name="search" size="54rpx" @tap="handleSearchClick('click')" />
    </template>
  </u-input>
</template>

<script setup lang="ts" name="search">
interface Props {
  width?: string
  customStyle?: Record<string, any> | string
  disabled?: boolean
  fontSize?: string
  shape?: string
  value?: string
  shadow?: boolean
  clearable?: boolean
  placeholder?: string
  iconPosition?: string
}

// 定义 props 并提供默认值
const props = withDefaults(defineProps<Props>(), {
  fontSize: '28rpx',
  shape: 'circle',
  value: '',
  shadow: false,
  clearable: true,
  placeholder: '请输入搜索内容',
  iconPosition: 'suffix',
})

// 定义 emit 事件
const emit = defineEmits(['search', 'input'])

const innerValue = ref(props.value)

// 计算样式
const defaultStyle = {
  width: '580rpx',
  padding: '14rpx 36rpx',
  background: '#fff',
  color: '#9999999',
}

const style = computed(() => {
  const style = { ...defaultStyle }
  if (props.width) style.width = `${props.width}`
  if (props.customStyle) {
    if (typeof props.customStyle === 'string') {
      const customStyles = props.customStyle.split(';').reduce((acc, cur) => {
        const [key, value] = cur.split(':').map(s => s.trim())
        if (key && value) acc[key] = value
        return acc
      }, {} as Record<string, string>)
      Object.assign(style, customStyles)
    } else {
      Object.assign(style, props.customStyle)
    }
  }
  return style
})

const border = computed(() => (props.shadow ? 'none' : 'surround'))

function handleSearchClick(type: string) {
  emit('search', innerValue.value, type)
}

function handleInput(value: string) {
  emit('input', value)
}
</script>

<style lang="scss">
/* 调整后的 WXSS */
.search {
  display: block;
}

.search > .search-icon {
  margin: 0 auto;
}
</style>
