import type { RequestResponse } from '@/es/request.d'
import type { MenuItem } from '../menu/type'

export interface loginParams {
  code: string
  inviteUserId?: string
}
export interface codeLoginParams {
  phoneNumber: string
  code: string
}

export interface UserInfo {
  id: string
  phone: string
  realName: string
  departmentId?: string[]
  roleId?: string[]
  systemAdministrator: boolean
  isAdministrator: boolean
  corpList: string[]
  joinCorpList: string[]
  corpId: string
  headIcon?: string
}
export interface CurrentUserType extends RequestResponse {
  data: {
    userInfo: UserInfo
    menuList: MenuItem[]
  }
}
export interface CorpType {
  corpId: string
  corpIcon: string
  corpName: string
}

export interface JoinCorpType {
  corpList: any[]
  joinCorpList: CorpType[]
  corpId: string
}

export interface ExamScoreType {
  _id: string
  phone: string
  exam_degree: string
  name: string
  english: number
  chinese: number
  math: number
  sort: string
  creatorTime: number
  major_category: string
  show: boolean
}
