<template>
  <view
    :style="{ backgroundColor: bg || '#F0F0F0' }"
    class="w675rpx h94rpx rounded-47rpx ma mt30rpx p20rpx p-x-30rpx box-border flex justify-between items-center input-shadow relative">
    <u-input
      v-model="searchValue"
      clearable
      :placeholder="placeholder"
      border="none"
      placeholder-style="color: #999999"
      @change="change"
      @confirm="confirm"></u-input>
    <u-icon name="search" size="30" @click="confirm"></u-icon>
  </view>
</template>

<script setup lang="ts" name="search-input">
const props = defineProps<{
  value?: string
  placeholder: string
  bg?: string
}>()

const emits = defineEmits(['onChange', 'onSearch'])

const searchValue = ref(props.value || '')

// 监听props.value的变化，同步到内部状态
watch(
  () => props.value,
  newValue => {
    searchValue.value = newValue || ''
  },
  { immediate: true }
)

const change = (e: string) => {
  searchValue.value = e
  emits('onChange', e)
}

const confirm = () => {
  emits('onSearch', searchValue.value)
}
</script>

<style lang="scss" scoped>
.input-shadow {
  box-shadow: 0rpx 10rpx 12rpx 1rpx rgba(201, 227, 248, 0.25);
}
</style>
