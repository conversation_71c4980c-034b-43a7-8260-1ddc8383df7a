<template>
  <view>
    <ExamList
      title="真题练习"
      :show-banner="false"
      :request-fn="getRealExamList"
      @go-test="gotoTest"></ExamList>
  </view>
</template>

<script setup lang="ts" name="past-paper-practice">
import ExamList from './components/ExamList.vue'
import { getRealExamList } from '@/api/project/exercises/index'
import type { PaperItem } from '@/api/project/exercises/type'
// 跳转
function gotoTest(exam: PaperItem) {
  uni.navigateTo({
    url: `/subPages/exercises/answer-sheet?type=test&examId=${exam.testPaperId}&title=${exam.name}&answerType=真题练习`,
  })
}
</script>

<style lang="scss" scoped></style>
