<template>
  <view
    class="w685rpx h214rpx bg-white ma mt20rpx px44rpx py35rpx box-border text-20rpx text-#333333"
    @click="onClick">
    <view class="flex items-center">
      <u-image
        :src="getSystemImg('/687758481807a96b974f9916/66ac86741f1cb273f7dbebae')"
        width="36rpx"
        height="45rpx"></u-image>
      <view class="ml15rpx">{{ name || '加载中' }} </view>
    </view>
    <view class="mt35rpx">
      以上数据来源:学校官网、网报平台公示，为人工整理，仅供参考，如有错误遗漏之处
      <navigator url="/" open-type="navigate" class="c-[#459AF7] inline"> 请反馈> </navigator>
      ，感谢您的支持理解!
    </view>
  </view>
</template>

<script setup lang="ts" name="file-item">
import { downloadFile, getSystemImg } from '@/utils'
const { url, name } = defineProps<{
  name: string
  url: string
}>()
function onClick() {
  downloadFile(url, name)
}
</script>

<style lang="scss" scoped></style>
