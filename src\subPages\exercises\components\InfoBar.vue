<template>
  <view class="answer-sheet-header" :style="safeHeader">
    <view class="answer-total">
      <text class="color-theme">{{ curIndex + 1 }}</text>
      <text style="margin: 0 0.1em">/</text>
      <text>{{ topicListLength }}</text>
    </view>
    <view v-if="testAndExam" class="answer-accuracy-rate color-theme">
      准确率 : {{ accuracyRate }}%
    </view>
    <view v-if="testType !== 'analysis'" class="answer-time">
      {{ isCountDown ? '剩余时间' : '答题用时' }} : {{ time }}
    </view>
  </view>
</template>

<script setup lang="ts" name="InfoBar">
import { getStatusBarHeight } from '@/utils/index'

const props = defineProps({
  testAndExam: {
    type: Boolean,
    default: false,
  },
  testType: {
    type: String,
    default: '',
  },
  curIndex: {
    type: Number,
    default: 0,
  },
  topicListLength: {
    type: Number,
    default: 0,
  },
  isCountDown: {
    type: Boolean,
    default: false,
  },
  accuracyRate: {
    type: Number,
    default: 0.0,
  },
  time: {
    type: String,
    default: '',
  },
})
const safeHeader = ref({
  top: `${Number(getStatusBarHeight()) + 43.75}px`,
})
</script>

<style lang="scss" scoped>
@import '@/common/css/index.scss';
@import '@/common/css/project.scss';
.answer-sheet {
  &-header {
    position: sticky;
    top: 0;
    display: flex;
    align-items: center;
    padding: 16rpx 30rpx;
    border-bottom: 1px solid #eee;
    background-color: #fff;
    z-index: 2023;
    > .answer-accuracy-rate {
      margin-left: 32rpx;
    }
    > .answer-time {
      margin-left: auto;
    }
  }
}
</style>
