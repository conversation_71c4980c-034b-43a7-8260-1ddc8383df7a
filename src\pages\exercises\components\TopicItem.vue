<template>
  <view class="w650rpx h180rpx ma flex justify-between pb10rpx b-b-1 b-b-#e5e5e5 b-b-solid">
    <image
      :src="assembleImgData(cover)"
      class="w220rpx h180rpx rounded-26rpx"
      mode="aspectFit"></image>
    <view class="ml20rpx flex-1 flex flex-col justify-around items-start">
      <view class="fw-bold"> {{ title }} </view>
      <view class="flex">
        <view
          v-for="(item, index) in tags"
          :key="index"
          class="mr10rpx p6rpx text-22rpx text-#537EEF bg-#e9f6fe"
          >{{ item }}</view
        >
      </view>
      <view class="text-28rpx fwbold text-#FF776A">￥{{ price?.toFixed(2) }}</view>
    </view>
  </view>
</template>

<script setup lang="ts" name="course-item">
import { assembleImgData, getSystemImg } from '@/utils'
defineProps<{
  title?: string
  tags?: string[]
  price?: number
  cover?: UploadImgData
}>()
</script>

<style lang="scss" scoped></style>
