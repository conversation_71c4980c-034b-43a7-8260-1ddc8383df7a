// src/store/examStore.ts
import { defineStore } from 'pinia'
import { ref } from 'vue'

export const useExamStore = defineStore(
  'exam',
  () => {
    const enCode = ref('') // 定义响应式变量来存储 enCode

    // 更新 enCode 的方法
    const updateEnCode = (newEnCode: string) => {
      enCode.value = newEnCode // 更新 enCode
    }

    return {
      enCode,
      updateEnCode,
    }
  },
  {
    persist: {
      // paths: [''], // 持久化存储的状态
      storage: {
        getItem: uni.getStorageSync,
        setItem: uni.setStorageSync,
      },
    },
  }
)
