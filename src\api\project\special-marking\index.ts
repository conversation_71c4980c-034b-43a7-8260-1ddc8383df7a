import type * as SpecialMarkingType from './type.d'
import {
  createModelData,
  dataInterface,
  getModelDataDetail,
  getModelList,
  updateSubTableData,
} from '@/api/visual'
import type { FilterType } from '@/es/request'
// 获取阅卷列表
export const getMarkingList = () => {
  return getModelList<SpecialMarkingType.MarkingItemData[]>({
    menuId: '675c246e805f686c4209daea',
    pageSize: -1,
    filter: [{ enCode: 'judges', method: 'eq', type: 'systemField', value: ['currentUser'] }],
  })
}

// 根据id查询阅卷
export const getMarkingDetail = (id: string) => {
  return dataInterface<SpecialMarkingType.MarkingDetailData[]>({
    id: '675cfd0f336ed1a5ac04b9a1',
    data: {
      '@id': id,
    },
  })
}

// 试卷切图接口
export const getPaperCut = (data: any) => {
  return uni.request({
    // url: 'http://192.168.1.135:8000/grading_papers',
    url: 'http://111.22.161.89:8000/api/api/grading_papers',
    method: 'POST',
    header: {
      'Content-Type': 'application/json',
    },
    data,
    timeout: 120000,
  })
}

// 提交阅卷
export const submitMarking = (
  id: string,
  data: SpecialMarkingType.MarkingSubmitData['tableField102']
) => {
  return updateSubTableData({
    modelId: '675bdc30805f686c4209dae4',
    id,
    data: {
      tableField102: data,
    },
  })
}

// 根据id查询阅卷题目明细
export const getMarkingTopicDetail = (id: string) => {
  return getModelDataDetail<SpecialMarkingType.TaskFacetItem>({
    menuId: '675bdc30805f686c4209dae4',
    _id: id,
  })
}

// 新增题目切割回写
export const addMarkingTopicCut = (data: SpecialMarkingType.MarkingSubmitData) => {
  return createModelData({
    menuId: '675bdc30805f686c4209dae4',
    data: JSON.stringify(data),
  })
}

// 获取当前用户专项打卡的答题记录
export const getSpecialMarkingList = (id: string) => {
  return getModelList<SpecialMarkingType.SpecialClockInHistoryItem>({
    menuId: '66ac4cefb98de275c77bedcc',
    filter: [
      { enCode: 'creatorUserId', method: 'eq', type: 'systemField', value: ['currentUser'] },
      { enCode: 'fid', method: 'eq', type: 'custom', value: [id] },
    ],
  })
}

// 创建专项打卡试卷记录
export const createSpecialClockInResultCard = (data: SpecialMarkingType.CreateResultCardType) => {
  return createModelData({
    menuId: '66ac4cefb98de275c77bedcc',
    data: JSON.stringify(data),
  })
}

// 获取刷题记录
export function getTestHistoryList({
  pageSize = 20,
  currentPage = 1,
  filter = [] as FilterType[],
}) {
  return getModelList<SpecialMarkingType.TestHistoryItem[]>({
    menuId: '66ac4cefb98de275c77bedcc',
    pageSize,
    currentPage,
    filter,
    connect: 'and',
  })
}
