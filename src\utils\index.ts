import { getFileStream } from '@/api/common'
import type { UploadFileResult } from '@/api/common/type'
import type { RequestResponse } from '@/es/request'
import useUserStore from '@/store/modules/user'

export function requestStream(
  url: string,
  method: UniNamespace.RequestOptions['method'],
  data: any
) {
  return new Promise((resolve, reject) => {
    const response = uni.request({
      url,
      method, // 你的请求方法
      data,
      header: {
        Accept: 'text/event-stream',
        'Content-Type': 'application/json;charset=UTF-8;',
        Connection: 'keep-alive',
      },
      responseType: 'text',
      enableChunked: true, // 开启流传输
      success: res => {
        resolve(res)
      },
      fail: err => {
        reject(err)
      },
    })

    // 返回请求的响应
    resolve(response)
  })
}
export function showFailToast(msg: string) {
  showToast(msg, 'error')
}

export function showSuccessToast(msg: string) {
  showToast(msg, 'success')
}

export function showToast(
  title: string,
  icon: UniNamespace.ShowToastOptions['icon'] = 'none',
  duration: UniNamespace.ShowToastOptions['duration'] = 2000
) {
  uni.showToast({
    title,
    icon,
    duration,
  })
}

export function showToastBack(
  title: string,
  icon: UniNamespace.ShowToastOptions['icon'] = 'none',
  duration: UniNamespace.ShowToastOptions['duration'] = 2000
) {
  uni.showToast({
    title,
    icon,
    duration,
  })
  setTimeout(() => {
    uni.navigateBack()
  }, duration)
}

export function showLoading(title: string) {
  uni.showLoading({
    title,
  })
}

export function getStatusBarHeight() {
  return uni.getSystemInfoSync().statusBarHeight
}

export function hideLoading() {
  uni.hideLoading()
}
// 获取图片地址
export function assembleImgData(imgData?: UploadImgData) {
  if (!imgData?.url) return ''
  return import.meta.env.VITE_SERVE + imgData.url
}

// 获取图片地址
export function getHeadIcon(url?: string) {
  if (!url) return ''
  return import.meta.env.VITE_SERVE + url
}
// 获取静态图片
export function getAssetsIcons(url: string) {
 // #ifdef H5
  return new URL(`../static/${url}`, import.meta.url).href
  // #endif
  return `/static/${url}`
}

// 文件下载
export async function downloadFile(url: string, fileName: string) {
  // #ifdef H5
  if (!url) {
    showFailToast('下载失败')
    return
  }
  const blob = await getFileStream(url)
  // 流下载
  const link = document.createElement('a')
  link.href = URL.createObjectURL(blob)
  link.download = fileName
  link.click()
  // #endif

  // #ifndef H5
  const res = await uni.downloadFile({
    url: import.meta.env.VITE_SERVE + url,
  })
  if (res.statusCode === 200) {
    const { savedFilePath } = await uni.saveFile({
      tempFilePath: res.tempFilePath,
    })
    uni.openDocument({
      filePath: savedFilePath,
      showMenu: true,
    })
  }
  // #endif
}

// 小程序文件上传 微信小程序只支持单文件上传
export async function miniProgramUploadFile(
  filePath: string
): Promise<RequestResponse<UploadFileResult>> {
  const userStore = useUserStore()
  return new Promise((resolve, reject) => {
    uni.uploadFile({
      url: `${import.meta.env.VITE_SERVE}/api/file/uploader`,
      filePath,
      name: 'file',
      header: {
        Authorization: userStore.token,
      },
      success: res => {
        const data = JSON.parse(res.data)

        if (data.code !== 200) {
          showFailToast('上传失败')
          reject(data)
          return
        }

        resolve(data)
      },
      fail: err => {
        showFailToast('上传失败')
        reject(err)
      },
    })
  })
}

// 解析导入excel文件
export async function previewExcelFile(
  filePath: string
): Promise<RequestResponse<UploadFileResult>> {
  const userStore = useUserStore()
  return new Promise((resolve, reject) => {
    uni.uploadFile({
      url: `${import.meta.env.VITE_SERVE}/api/visualdev/form/read`,
      filePath,
      name: 'file',
      header: {
        Authorization: userStore.token,
        'Accept-Charset': 'utf-8',
        'Content-Type': 'multipart/form-data; charset=utf-8',
      },
      success: res => {
        const data = JSON.parse(res.data)
        if (data.code !== 200) {
          showFailToast('上传失败')
          reject(data)
          return
        }
        resolve(data)
      },
      fail: err => {
        showFailToast('上传失败')
        reject(err)
      },
    })
  })
}

// 导入excel文件到表
export async function importExcelToTable(
  data: any,
  fileId: string,
  importField: string[],
  menuId: string
): Promise<RequestResponse<UploadFileResult>> {
  const userStore = useUserStore()
  return new Promise((resolve, reject) => {
    uni.uploadFile({
      url: `${import.meta.env.VITE_SERVE}/api/visualdev/form/import`,
      data,
      fileId,
      importField,
      menuId,
      header: {
        Authorization: userStore.token,
      },
      success: res => {
        console.log(res)
        const data = JSON.parse(res.data)
        // if (data.code !== 200) {
        //   showFailToast('上传失败')
        //   reject(data)
        //   return
        // }
        resolve(data)
      },
      fail: err => {
        showFailToast('上传失败')
        reject(err)
      },
    })
  })
}

// base64图片上传
export async function uploadBase64Img(base64Img: string) {
  const userStore = useUserStore()
  return new Promise((resolve, reject) => {
    uni.request({
      url: `${import.meta.env.VITE_SERVE}/api/file/uploaderBase64`,
      method: 'POST',
      header: {
        Authorization: userStore.token,
      },
      data: {
        base64: base64Img,
        name: `${uni.$u.guid(32)}.png`,
      },
      success: res => {
        resolve(res)
      },
      fail: err => {
        reject(err)
      },
    })
  })
}

// 把对象转换成 url参数
export function toURLSearchParams(params: Record<string, string | number | boolean>) {
  return Object.entries(params)
    .map(([key, value]) => `${encodeURIComponent(key)}=${encodeURIComponent(value)}`)
    .join('&')
}

// 系统图片
export function getSystemImg(url: string) {
  return `${import.meta.env.VITE_SERVE}/api/file/previewImage/${url}`
}

// url拼接图片
export function getImgByUrl(url: string) {
  return `${import.meta.env.VITE_SERVE_IMG}${url}`
}

// 系统图片
export function getSystemImgUrl(url: string) {
  return `${import.meta.env.VITE_SERVE_IMG}/api/file/previewImage/${url}`
}
// 跳转至微信客服
export const TargetWeChatService = () => {
  uni.openCustomerServiceChat({
    extInfo: {
      url: 'https://work.weixin.qq.com/kfid/kfc2826e01ee90b0f46',
    },
    corpId: 'wwa09912170486c7a5',
    fail: e => {
      console.log(e)
      showToast('跳转客服失败')
    },
  })
}

// 数字筛出
export const extractNumbers = (str: string) => {
  const matches = str.match(/\d+/g)
  if (matches) {
    return {
      strs: matches.join(''),
      list: matches,
    }
  } else {
    return {
      strs: '',
    }
  }
}
// 正则替换数学公式
export const replaceSpanWithLatex = (input: string) => {
  const regex = /<span class='math'>(.*?)<\/span>/g
  const replaced = input?.replace(regex, (match, content) => {
    return `$${content}$`
  })
  return replaced
}

// 计算指定月份始末时间
export const getStartEndTime = (month?: number, year?: number) => {
  const today = new Date()
  if (year) today.setFullYear(year)
  if (month) today.setMonth(month - 1)
  const startTime = new Date(today.getFullYear(), today.getMonth(), 1).getTime()
  const endTime = new Date(today.getFullYear(), today.getMonth() + 1, 0).setHours(23, 59, 59, 0)
  return {
    startTime,
    endTime,
  }
}

// 秒转HH:mm:ss
export const formatHours = (seconds: number) => {
  const hours = Math.floor(seconds / 3600)
  const minutes = Math.floor((seconds % 3600) / 60)
  const remainingSeconds = seconds % 60
  const formattedHours = hours.toString().padStart(2, '0')
  const formattedMinutes = minutes.toString().padStart(2, '0')
  const formattedSeconds = remainingSeconds.toString().padStart(2, '0')
  return `${formattedHours}:${formattedMinutes}:${formattedSeconds}`
}

// 通用字符串分割
export const splitArray = (str = '') => {
  const rule = /[/\s，、：:；\n↵]+/g
  return str.split(rule).filter(Boolean)
}

/**
 * 将时间戳数组转换为特定格式的日期范围字符串。
 *
 * @param startTime - 包含两个时间戳的数组，第一个表示开始时间，第二个表示结束时间。
 * @returns 返回格式为 "YYYY.MM.DD~YYYY.MM.DD" 的日期范围字符串。
 * @throws {Error} 如果 startTime 数组的长度不是 2，则抛出错误。
 *
 * @example
 * const startTime = [1721836800000, 1722009600000];
 * const formattedDateRange = formatTimestamps(startTime);
 * console.log(formattedDateRange); // 输出: 2023.11.10~2023.11.20
 */
export function formatTimestamps(Time: Array<number>): string {
  if (Time.length !== 2) {
    throw new Error('startTime 应该包含两个时间戳')
  }
  const formatDate = (timestamp: number): string => {
    const date = new Date(timestamp)
    const year = date.getFullYear()
    const month = String(date.getMonth() + 1).padStart(2, '0') // 月份从0开始，所以加1
    const day = String(date.getDate()).padStart(2, '0')
    return `${year}.${month}.${day}`
  }

  const startDate = formatDate(Time[0])
  const endDate = formatDate(Time[1])

  return `${startDate}~${endDate}`
}

export function formatSingleTimestamp(timestamp: number): string {
  // 检查传入的 timestamp 是否为有效数字
  if (isNaN(timestamp) || timestamp < 0) {
    throw new Error('timestamp 应该是有效的时间戳')
  }

  const date = new Date(timestamp)
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0') // 月份从0开始，所以加1
  const day = String(date.getDate()).padStart(2, '0')
  const hours = String(date.getHours()).padStart(2, '0') // 获取小时并补零
  const minutes = String(date.getMinutes()).padStart(2, '0') // 获取分钟并补零

  return `${year}.${month}.${day} ${hours}:${minutes}` // 返回格式：YYYY.MM.DD HH:mm
}

/**
 * 判断答题是否正确,比如[B,A,C,D]和["ABCD"]]返回的结果是true
 */
export function arraysEqual(questionType: string, arr1: any[], arr2: string[]): boolean {
  // console.log("判断题目信息：", questionType, arr1, arr2);

  // 判断两个数组是否undefined或者null
  if (arr1 === undefined || arr2 === undefined || arr1 === null || arr2 === null) {
    return false
  }

  // 如果长度不同，直接返回 false
  if (arr1.length !== arr2.length) {
    return false
  }

  // 只在指定题目类型下使用逻辑
  const acceptableQuestionTypes = ['单选题', '多选题', '判断题']

  if (acceptableQuestionTypes.includes(questionType)) {
    // 首先，将 arr1 中的字符串分割成数组
    const elementsArr1 = arr1[0].split(',') // 从 arr1 中获取并分割字符串
    const elementsArr2 = arr2[0].split('') // 将 arr2 的字符串分割为字符数组

    // 使用 Map 统计元素的出现次数
    const countMap = new Map()

    // 统计 elementsArr1 中每个元素的出现次数
    for (const item of elementsArr1) {
      countMap.set(item.trim(), (countMap.get(item.trim()) || 0) + 1)
    }

    // 检查 elementsArr2 中每个元素是否在 countMap 中并减少计数
    for (const item of elementsArr2) {
      const count = countMap.get(item.trim())
      if (!count) {
        return false // 如果 arr2 中的元素在 arr1 中不存在或者已用完，返回 false
      }
      // 减少该元素的计数
      countMap.set(item.trim(), count - 1)
    }

    // 如果所有元素计数归零，返回 true
    return Array.from(countMap.values()).every(count => count === 0)
  } else if (questionType === '填空题') {
    // 处理填空题的逻辑
    const answerKey = removeHtmlTags(arr1[0].trim()) // 获取并清理正确答案
    const studentAnswer = removeHtmlTags(arr2[0].trim()) // 获取并清理学生答案

    // 忽略大小写和空格进行比较
    return answerKey.toLowerCase() === studentAnswer.toLowerCase()
  }

  // 如果题目类型不接受，返回 false
  return false
}

// 用于移除字符串中的 HTML 标签
export function removeHtmlTags(str: string): string {
  // 使用正则表达式去除 HTML 标签
  return str.replace(/<\/?[^>]+(>|$)/g, '')
}
