<template>
  <view class="min-h-screen bg-gray-100 overflow-hidden">
    <template v-if="pointsList.length">
      <view
        v-for="item in pointsList"
        :key="item._id"
        class="mx-4 mt-3 p-4 bg-white rounded-lg shadow-sm">
        <!-- 邀请人信息 -->
        <view class="flex justify-between items-center mb-3">
          <view class="flex-1">
            <text class="text-15px font-medium text-gray-800">{{
              item.creatorUserId?.fullName || '-'
            }}</text>
            <text class="ml-2 text-13px text-gray-400">{{ item.creatorUserId?.phone || '-' }}</text>
          </view>
          <text class="text-12px text-gray-400">{{ formatDate(item.creatorTime) }}</text>
        </view>
        <!-- 被邀请人信息 -->
        <view class="flex items-center justify-between">
          <view class="flex items-center">
            <text class="text-16px font-bold text-#ff6b6b">+{{ item.points_num }}</text>
            <text class="ml-1 text-12px text-gray-500">积分</text>
          </view>
        </view>
      </view>
    </template>

    <u-empty v-else mode="data" text="暂无邀请记录" margin-top="120" />
  </view>
</template>

<script setup lang="ts" name="user-invite-records">
import { getPointsInfo } from '@/api/project/member'
import type { IPointsData } from '@/api/project/member/type'
import useDateFormatter from '@/hooks/useDateFormatter'

const pointsList = ref<IPointsData[]>([])

const { formatDate } = useDateFormatter()

// 获取邀请记录列表
const getInviteRecords = async () => {
  const {
    data: { list },
  } = await getPointsInfo('邀请用户')
  pointsList.value = list
}

onLoad(() => {
  getInviteRecords()
})
</script>

<style lang="scss" scoped></style>
