<template>
  <view>
    <u-tabbar v-if="testAndExam" :placeholder="false" inactive-color="#459AF7">
      <u-tabbar-item text="题卡" @tap="showPopup">
        <template #inactive-icon>
          <image
            class="tabbar-icon"
            :src="getSystemImg('6747e9ed0a34815816f11159/669bb8940bb07d7cd6ed3bae')">
          </image>
        </template>
      </u-tabbar-item>
      <u-tabbar-item text="上一题" icon="play-left" @tap="pickTest(0)"> </u-tabbar-item>
      <u-tabbar-item text="下一题" icon="play-right" @tap="pickTest(1)"> </u-tabbar-item>
      <u-tabbar-item text="交卷" @tap="handExam">
        <template #inactive-icon>
          <image
            class="tabbar-icon"
            :src="getSystemImg('6747e9ed0a34815816f11159/66bb257cb23ec40f60a6eb43')">
          </image>
        </template>
      </u-tabbar-item>
    </u-tabbar>
    <u-tabbar v-else :placeholder="false" inactive-color="#459AF7">
      <u-tabbar-item text="上一题" icon="play-left" @tap="pickTest(0)"> </u-tabbar-item>
      <u-tabbar-item
        v-if="testType === 'analysis' || testType === 'report'"
        text="解析"
        @tap="showAnalysis">
        <template #inactive-icon>
          <image
            class="tabbar-icon"
            :src="getSystemImg('6747e9ed0a34815816f11159/669bbde90bb07d7cd6ed3bb2')">
          </image>
        </template>
      </u-tabbar-item>
      <u-tabbar-item text="题卡" @tap="showPopup">
        <template #inactive-icon>
          <image
            class="tabbar-icon"
            :src="getSystemImg('6747e9ed0a34815816f11159/669bb8940bb07d7cd6ed3bae')">
          </image>
        </template>
      </u-tabbar-item>
      <u-tabbar-item
        v-if="curIndex === topicListLength - 1 && testType === 'random'"
        text="交卷"
        @tap="handExam">
        <template #inactive-icon>
          <image
            class="tabbar-icon"
            :src="getSystemImg('6747e9ed0a34815816f11159/66bb257cb23ec40f60a6eb43')">
          </image>
        </template>
      </u-tabbar-item>
      <u-tabbar-item v-else text="下一题" icon="play-right" @tap="pickTest(1)"> </u-tabbar-item>
    </u-tabbar>
  </view>
</template>

<script setup lang="ts" name="TabbarItem">
// utils
import { getSystemImg } from '@/utils/index'
const props = defineProps({
  testAndExam: {
    type: Boolean,
    default: false,
  },
  testType: {
    type: String,
    default: '',
  },
  curIndex: {
    type: Number,
    default: 0,
  },
  topicListLength: {
    type: Number,
    default: 0,
  },
})
const emit = defineEmits(['pickTest', 'handExam', 'showAnalysis', 'showPopup'])

function pickTest(e: number) {
  emit('pickTest', e)
}
function handExam(e: number) {
  emit('handExam')
}
function showAnalysis(e: number) {
  emit('showAnalysis')
}
function showPopup(e: number) {
  emit('showPopup')
}
</script>

<style lang="scss" scoped>
.tabbar-icon {
  width: 20px;
  height: 20px;
}
.u-tabbar__icon {
  width: 17px;
  height: 17px;
}
</style>
