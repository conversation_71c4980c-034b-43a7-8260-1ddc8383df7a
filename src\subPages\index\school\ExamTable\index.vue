<template>
  <!-- 考试科目表格 -->
  <view
    class="h70rpx head bg-#459AF7 flex text-white justify-around items-center text-18rpx row bor-b">
    <view class="text-center col h74rpx lh-74rpx flex items-center px-8rpx" style="flex: 1">
      <view class="w100%">{{ titles[0] }}</view>
    </view>
    <view class="text-center col h74rpx lh-74rpx" style="flex: 4">{{ titles[1] }}</view>
  </view>
  <view
    v-for="(ite, index) in list"
    :key="`${index}1`"
    class="w-full flex text-#333333 items-stretch text-18rpx row relative b-solid b-#D1D1D1 b-1 border-box">
    <view class="watermark-container">
      <view class="watermark">湖南人人学教育</view>
    </view>
    <view class="text-left flex flex-center col bor-r border-box px-8rpx" style="flex: 1">
      {{ ite.major }}
    </view>
    <view class="text-left col" style="flex: 4">
      <!-- 考试科目 -->
      <view class="w-full p10rpx pl20rpx box-border h50rpx flex fw-bold justify-start items-center">
        {{ ite.list[0] }}
      </view>
      <view class="p10rpx pl20rpx box-border">
        <!-- {{ ite.books }} -->
        <view v-for="i in ite.list.splice(1)" :key="i">{{ i }}</view>
      </view>
    </view>
  </view>
  <u-empty v-if="list.length === 0" text="暂无数据"></u-empty>
</template>

<script setup lang="ts" name="exam-table">
defineProps<{
  titles: string[]
  list: {
    major: string
    // subject: string
    // books: string
    list: string[]
  }[]
}>()
</script>

<style lang="scss" scoped>
.border-box {
  box-sizing: border-box;
}
.bor-bor {
  border: 1px solid #d1d1d1;
}
.bor-b {
  border-bottom: 1px solid #d1d1d1;
}
.bor-r {
  border-right: 1px solid #d1d1d1;
}
.bor-l {
  border-left: 1px solid #d1d1d1;
}
.row.head {
  overflow: hidden;
  .col {
    rich-text {
      width: 100%;
    }
    &::after {
      content: '';
      height: 51rpx;
      background-color: #edeeee;
      width: 1rpx;
      opacity: 0.42;
      display: block;
      position: relative;
      left: 8rpx;
    }
    &:last-child::after {
      content: none;
    }
  }
}

/* 水印容器样式 */
.watermark-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 999;
  pointer-events: none;
}

/* 水印文字样式 */
.watermark {
  font-size: 40rpx;
  color: rgba(0, 0, 0, 0.3);
  white-space: nowrap;
  text-align: center;
  user-select: none;
  letter-spacing: 30rpx;
  opacity: 0.1;
  transform: rotate(-5deg);
}
</style>
