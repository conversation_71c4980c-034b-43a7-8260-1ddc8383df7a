<template>
  <view class="w750rpx p20rpx box-border bg-#f0f2f5">
    <view class="text-34rpx fw-bold text-#333333"> 课程资料（{{ infoNum }}） </view>
    <view class="w690rpx ma m-y-20rpx">
      <up-search
        v-model="searchValue"
        placeholder="请输入资料名"
        bg-color="white"
        :clearabled="true"
        shape="square"
        :action-style="{
          color: '#459AF7',
        }"></up-search>
    </view>
    <InfoItem
      v-for="item in infoList.filter(ite => ite.data_name.includes(searchValue))"
      :key="item._id"
      :title="item.data_name"
      :content="item.data_content"
      :publish="item.creatorUserId.fullName"
      :data-time="item.creatorTime"
      :dataurl="item.data_url[0].url" />
    <up-empty v-if="infoList.length === 0" text="暂无资料" />
    <view class="text-34rpx fw-bold text-#333333"> 大家都在学习 </view>
    <view v-if="recommendList.length > 0" class="bg-white mt20rpx p10rpx box-border rounded-20rpx">
      <CourseItem
        v-for="(item, index) in recommendList"
        :key="index"
        :title="item.course_name"
        :tags="item.course_tag"
        :cover="item.course_cover[0]"
        :price="item.course_price"
        @tap="toDetail(item.recommend)" />
    </view>
    <up-empty v-else text="暂无推荐课程" />
  </view>
</template>

<script setup lang="ts" name="course-information">
import InfoItem from './InfoItem/index.vue'
import CourseItem from '@/pages/course/components/CourseItem.vue'
import { getCourseInfomationData, getCourseRecommendData } from '@/api/project/course'
import type { ICourseInfomationType, ICourseRecommendTable } from '@/api/project/course/type'

const props = defineProps<{
  courseId: string
}>()

// 输入框内容
const searchValue = ref('')

const infoNum = ref(0)

// 资料列表
const infoList = ref<ICourseInfomationType[]>([])

// 推荐课程列表
const recommendList = ref<ICourseRecommendTable[]>([])

const getRecommend = async () => {
  const {
    data: { list },
  } = await getCourseRecommendData(props.courseId)
  recommendList.value = list[0].tableField102
}

const getData = async () => {
  const {
    data: { list },
  } = await getCourseInfomationData(props.courseId)
  infoList.value = list
  infoNum.value = list.length
  getRecommend()
}
getData()

const toDetail = (id: string) => {
  uni.redirectTo({
    url: `/subPages/course/detail?id=${id}`,
  })
}
</script>

<style lang="scss" scoped></style>
