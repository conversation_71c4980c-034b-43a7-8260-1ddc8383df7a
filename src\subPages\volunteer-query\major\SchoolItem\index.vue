<template>
  <view
    class="w730rpx h180rpx rounded-10rpx bg-white flex justify-between items-center p30rpx box-border ma m-y-20rpx relative">
    <view class="flex absolute top-10rpx right-10rpx text-26rpx text-#969DAB items-center">
      <view @click="toDetail(itemData._id)"> 查看更多> </view>
    </view>
    <u-image
      :src="assembleImgData(itemData.school_badge?.[0])"
      :width="imgSize?.width || '120rpx'"
      :height="imgSize?.height || '180rpx'"
      mode="aspectFit"></u-image>
    <view class="flex-1 h-160rpx ml40rpx flex flex-col justify-center">
      <view class="text-30rpx text-#333333 fw-bold">{{ itemData.school_name || '湖南大学' }}</view>
      <view class="w-full h40rpx"></view>
      <view class="text-22rpx text-#969DAB">招生计划、录取分数、学费详情</view>
    </view>
  </view>
</template>

<script setup lang="ts" name="school-item">
import type { IBKSchoolData } from '@/api/project/index/type'
import { assembleImgData, getSystemImg } from '@/utils'

defineProps<{
  itemData: IBKSchoolData
  imgSize?: {
    width: string
    height: string
  }
}>()

const toDetail = (id: string) => {
  uni.navigateTo({
    url: `/subPages/index/school/detail?id=${id}`,
  })
}
</script>

<style lang="scss" scoped></style>
