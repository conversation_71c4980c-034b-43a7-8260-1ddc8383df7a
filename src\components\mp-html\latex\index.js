/**
 * @fileoverview latex 插件
 * katex.min.js来源 https://github.com/rojer95/katex-mini
 */
import parse from 'katex-uniapp'

function Latex() {}

Latex.prototype.onParse = function (node, vm) {
  // $...$ 或者 [...]包裹的内容为latex公式
  if (!vm.options.editable && node.type === 'text' && /\$(.+?)\$|\[(.+?)\]/.test(node.text)) {
    delete node.type
    node.name = 'span'
    node.attrs = {}   
    node.children = node.text
    .replaceAll('[', '$')
    .replaceAll(']', '$')
      .split('$')
      .map((str, index) => {
        // 偶数
        if ((index + 1) % 2 === 0) {
          return {
            name: 'span',
            attrs: {},
            f: 'display:inline-block',
            children: parse(str),
          }
        }
        return {
          type: 'text',
          text: str,
        }
      })
      .filter(node => node.name || node.text)
    delete node.text
  }
}

export default Latex
