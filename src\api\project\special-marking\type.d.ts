// 阅卷列表
export interface MarkingItemData {
  _id: string
  creatorUserId: UserIdData
  tableField113: {
    _id: string
    taskId: string
  }[]
  name: string
  index: string[]
  creatorTime: number
  judges: {
    id: string
    headIcon: string
    phone: string
    fullName: string
  }[]
  paperId: string
}

// 提交阅卷
export interface MarkingSubmitData {
  paperId: string
  tableField102: {
    index: number
    topic: string
    topicImg: UploadImgData[]
    _id: string
    judges: string[]
    ai_isCorrect?: string
    ai_score?: number
    score?: number
  }[]
  userId: string
}

export interface MarkingDetailData {
  paperFacet: PaperFacetItem[]
  taskFacet: TaskFacetItem[]
}
export interface PaperFacetItem {
  _id?: string
  chapter?: string[]
  creatorUserId?: string
  knowledgePoints?: string[]
  answer?: string[]
  subject?: string
  grade?: string
  tableField116: any[]
  creatorTime: number
  title: string
  type: string
  keyword: string
  option: any[]
  score: number
  analysis?: string
}

export interface TaskFacetItem {
  _id: string
  creatorUserId: string
  tableField102: {
    score?: number
    ai_score?: number
    index: number
    topic: string
    topic_id: string
    topicImg: UploadImgData[]
    userAnswer: string
    _id: string
    judges: any[]
    isCorrect: string
    ai_isCorrect?: string
    showAnswer?: boolean
  }[]
  creatorTime: number
  userId: string
  paperId: string
}

// 创建专项打卡试卷记录
export interface CreateResultCardType {
  data: TopicResultType
  fid: string
  answerTime: string
  type: string
  state: string
  totalScore: number
  accuracy: number
  userId: string
  tableField112: TopicResultType[]
}
// 子表
export interface TopicResultType {
  _id?: string
  topicId: string
  index?: number
  userAnswer: string
  score?: number
}

// 专项打卡答题记录
export interface SpecialClockInHistoryItem {
  _id: string
  fid: string
  creatorUserId: UserIdData
  batchNo: string
  answerTime: string
  tableField112: TopicResultType[]
  accuracy: number
  state: string
  creatorTime: number
  type: string
  totalScore: number
}

export interface TestHistoryItem {
  _id: string
  fid: string
  userId: string
  creatorUserId: UserIdData
  batchNo: string
  answerTime: string
  tableField112: TopicResultType[]
  accuracy: number
  state: string
  creatorTime: number
  type: string
  totalScore: number
}
