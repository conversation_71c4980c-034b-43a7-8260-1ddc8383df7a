// 首页展示
export interface IinfoDisplayData {
  _id: string
  policyd_id: string
  major_id: string
  major_code: string
  school_id: string
  creatorTime: number
  title: string
  jump_type: '学校' | '政策' | '专业'
  content: string
}

// 前台配置数据
export interface IExamTimeData {
  _id: string
  exam_time: number
  creatorTime: number
  video_review: number
}

export interface IZKMajorData {
  _id: string
  college_type_name: string
  college_name: string
  creatorTime: number
  college_no: string
  college_type_code: string
}

interface IZKCrossMajorData {
  _id: {
    Undergraduate_type_name: string
    Undergraduate_type_code: string
  }
}

// 院校库
interface IBKSchoolData {
  _id: string
  academy_type: string
  school_address: string
  school_type: string
  school_name: string
  school_intro: string
  creatorTime: number
  school_region: string
  school_badge: UploadImgData[]
  school_banner: UploadImgData[]
  rank_type: string
  school_ranking: string
  floor_space: string
  found_time: string
  special_count: number
  special_online_count: number
  general_count: number
  general_online_count: number
  phone: string
}

//院校地区
interface IBKSchoolAddressData {
  _id: string
  school_address: string
  creatorTime: number
}

// 院校类型
interface IBKSchoolTypeData {
  _id: string
  school_type: string
  creatorTime: number
}

// 本科院校专业
interface IBKSchoolMajorData {
  _id: string
  sum_year: string
  other_info: string
  school_name: string
  acceptance_rate: string
  tuition: string
  score: string
  admission_nums: string
  application_nums: string
  enrollment_plan: string
  creatorTime: number
  Undergraduate_name: string
  Undergraduate_code: string
  annex_files: UploadFzData[]
  lastModifyTime: number
}

// 定制化专业库
interface IBKInterMajorData {
  degree_type: string
  major_code: string
  major_name: string
  undergraduate_count: number
  _id: string
}

// 专业库
interface IBKMajorData {
  _id: string
  employment_direction: string
  graduate_exam_direction: string
  degree_type: string
  major_code: string
  study_term: string
  core_curriculum: string
  average_salary: string
  major_name: string
  creatorTime: number
  major_intro: string
  train_objective: string
}

// 升本政策
interface ISbPolicyData {
  _id: string
  release_user: string
  source: string
  creatorTime: number
  type: string
  title: string
  content: string
  release_time: number
}

// 升本日程
interface ISbScheduleData {
  _id: string
  tabs: string
  header: string
  index: number
  creatorTime: number
  content: string
}

// 考试时间
interface ISbExamTimeData {
  _id: string
  date: number
  subject: string
  time: string
  creatorTime: number
}
interface ISbYearData {
  title: string
  plan: string
  score: string
  count: string
}

// 首页轮播
export interface IndexSwiper {
  _id: string
  cover: UploadImgData[]
  state: number
  creatorTime: number
  title: string
  target_fun_params: string
  target_fun: string
}

// 用户收藏
export interface IUserCollectSchool {
  _id: string
  school: string
  creatorTime: number
}
