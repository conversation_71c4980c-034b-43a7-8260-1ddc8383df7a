<template>
  <view>
    <view class="introduction-title">数据下载</view>
    <view class="flex">
      <view class="text-#999999 text-30rpx mt26rpx ml20rpx">
        文件名 <span class="text-#459AF7 cursor"> 下载</span></view
      >
    </view>
    <view class="introduction-title mt30rpx">数据说明</view>
    <view>
      <view class="mt34rpx"> 9月用户数据(UserLost_data) </view>
      <view>
        <view class="mt26rpx">
          <view v-for="item in 20" :key="item" class="flex table-line">
            <view
              class="table-item w374rpx h78rpx border border-r-none border-1rpx border-#707070 border-solid flex items-center flex-center">
              USER_NO
            </view>
            <view
              class="table-item w376rpx h78rpx border border-1rpx border-#707070 border-solid flex items-center text-center flex-center">
              用户编号
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts" name="data-view">
const rules = ref(
  '1.任务完成基本流程（1）了解、分析问题；（2）获取训练集和测试集；（3）进行数据整理和清洗，注意9月的训练数据和10月数据都 有空数据；（4）探索数据，进行特征处理；（5）建模、预测和解决问题；（6）提交结果，包含代码文件result.py和预测结果result.json。提交文件名：result.json ，文件格式说明：[{target_id:predict},{target_id:predict}]   ,target_id为用户编号，predict 为该预测是'
)
</script>

<style lang="scss" scoped>
.introduction-title {
  height: 42rpx;
  font-family: PingFang SC, PingFang SC;
  font-weight: 400;
  font-size: 30rpx;
  color: #333333;
  line-height: 42rpx;
  text-align: left;
  font-style: normal;
  text-transform: none;
  padding-left: 20rpx;
  position: relative;
}

.introduction-title::before {
  content: '';
  display: inline-block;
  width: 8rpx;
  height: 20rpx;
  position: absolute;
  background: #459af7;
  border-radius: 10rpx;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
}
.table-line {
  .table-item {
    border-top: none;
  }
  &:first-child .table-item {
    border-top: 1rpx solid #707070;
  }
}
</style>
