<template>
  <TitleBar
    title="会员中心"
    text-color="white"
    show-back
    nav-color="#1d1e1d"
    :flag="false"
    fsize="36rpx" />
  <!-- 用户信息 -->
  <view
    class="w-full h260rpx relative bg-#1d1e1d bg-opacity-100 flex p30rpx box-border items-center">
    <u-avatar :src="getHeadIcon(userAvatar)" size="120rpx" />
    <view class="flex flex-col ml30rpx h100rpx justify-between">
      <view class="text-40rpx text-white font-bold">{{ userName }}</view>
      <view v-if="!isVip" class="text-28rpx text-#969DAB"> 未开通 会员 </view>
      <view
        v-else
        class="relative flex min-w-220rpx h44rpx p-15rpx box-border rounded-22rpx bg-#141615 items-center justify-center"
        @click="toPoints">
        <view
          class="absolute top-[0rpx] left-[-20rpx] w60rpx h60rpx rounded-30rpx bg-#141615 flex-center">
          <image
            :src="getSystemImg('/6747e9ed0a34815816f11159/67590525f8730075fead9d6f')"
            class="w-56rpx h50rpx"></image>
        </view>
        <view class="text-24rpx text-#FBE1A3 pl40rpx"
          ><text class="mr20rpx">VIP·壹</text> 积分：{{ pointsNum }}</view
        >
      </view>
    </view>
  </view>
  <view class="relative">
    <view
      class="w-750rpx h-800rpx absolute top-[-30rpx] left-0 rounded-30rpx bg-white p30rpx box-border">
      <!-- 会员套餐 -->
      <view class="flex flex-wrap justify-between">
        <view
          v-for="(item, index) in membershipPlans"
          :key="item._id"
          class="w216rpx h190rpx relative flex flex-col justify-end items-center bg-#f3f3f3 mt20rpx rounded-10rpx text-24rpx member-card"
          :class="{ 'member-card-active': currentMembershipPlan === index }"
          @click="memberShipChange(item, index)">
          <view class="fw-bold text-#202120">{{ item.title }}</view>
          <view class="text-#202120 m-y-16rpx"
            ><text>￥</text><text class="text-28rpx fw-bold">{{ item.price }}</text></view
          >
          <view class="points w176rpx h40rpx text-center mb10rpx lh-40rpx"
            >{{ item.integral }}积分</view
          >
          <view v-if="item.is_recommend === 1" class="tag">推荐</view>
        </view>
      </view>
      <!-- 会员权益 -->
      <view class="fw-bold text-32rpx text-#202120 m-y-30rpx">会员权益</view>
      <view class="flex justify-between">
        <view
          v-for="item in memberBenefits"
          :key="item.id"
          class="w216rpx h330rpx bg-#f7f7f7 rounded-10rpx flex flex-col justify-around items-center p20rpx box-border">
          <image class="w134rpx h134rpx" :src="getSystemImg(item.icon)"></image>
          <view class="text-32rpx text-#02172D">{{ item.title }}</view>
          <view class="text-26rpx text-#777C8B">{{ item.description }}</view>
        </view>
      </view>
    </view>
  </view>
  <!-- 底部按钮 -->
  <u-tabbar>
    <view class="w-full flex items-center p-x-20rpx justify-between">
      <view class="flex w-60%">
        <view class="text-28rpx text-#666666">合计：</view>
        <view class="text-#FF776A text-28rpx fw-bold ml10rpx">￥{{ nowPrice }}</view>
      </view>
      <u-button
        :custom-style="{
          letterSpacing: '10rpx',
          width: '225rpx',
        }"
        type="primary"
        shape="circle"
        text="立即支付"
        @click="toPlace"></u-button>
    </view>
  </u-tabbar>
</template>

<script setup lang="ts" name="user-member">
import TitleBar from '@/components/TitleBar.vue'
import { getHeadIcon, getSystemImg, showToast, showToastBack } from '@/utils'
import useUserStore from '@/store/modules/user'
import useMemberStore from '@/store/modules/member'
import { getMemberPriceList, getPayStatus, wxPay } from '@/api/project/member'
import type { IMemberPriceData, WxPayParams } from '@/api/project/member/type.d'

const userStore = useUserStore()
const memberStore = useMemberStore()
const userAvatar = computed(() => {
  return userStore.userInfo?.headIcon
})

const userInfo = computed(() => {
  return userStore.userInfo
})

const userName = computed(() => {
  return userInfo.value?.realName
})

const pointsNum = computed(() => {
  return memberStore.points_num
})

const toPoints = () => {
  uni.navigateTo({
    url: '/subPages/user/points',
  })
}

// 会员套餐数据
const currentMembershipPlan = ref<number>(0)
const nowPrice = ref<number>(0)
const membershipPlans = ref<IMemberPriceData[]>([])
// 会员套餐切换
const memberShipChange = (val: any, index: number) => {
  currentMembershipPlan.value = index
  nowPrice.value = val.price
}

// 会员权益数据
const memberBenefits = [
  {
    id: 1,
    title: '看视频',
    description: '100+学习视频',
    icon: '/6747e9ed0a34815816f11159/67ad6c3f40e03a0b4b627fac',
  },
  {
    id: 2,
    title: '刷题',
    description: '100+题库',
    icon: '/6747e9ed0a34815816f11159/67590764f8730075fead9d73',
  },
  {
    id: 3,
    title: 'AI识题',
    description: 'ai帮助识别解答',
    icon: '/6747e9ed0a34815816f11159/67590764f8730075fead9d74',
  },
]

const placeData = ref<WxPayParams>({
  description: '',
  appId: 'wxe04186c0f2ce20d3',
  code: '',
  out_order_no: new Date().getTime().toString(),
  goods_module_id: '675a92ad805f686c4209dade',
  goods_price_field: 'price',
  goods_id: '',
})

// 支付
const toPay = (val: any) => {
  uni.requestPayment({
    provider: 'wxpay',
    orderInfo: {} as any,
    ...val,
    success: (res: any) => {
      // showToast('支付成功')
      getPayStatus(placeData.value.out_order_no).then(res => {
        if (res.code === 200) {
          showToastBack('支付成功')
        }
      })
    },
    fail: (err: any) => {
      showToast('支付失败', err)
    },
  })
}

// 下单
const toPlace = () => {
  placeData.value.goods_id = membershipPlans.value[currentMembershipPlan.value]._id
  placeData.value.description = membershipPlans.value[currentMembershipPlan.value].title
  uni.login({
    success: res => {
      placeData.value.code = res.code
      wxPay(placeData.value).then(res => {
        toPay(res.data)
      })
    },
  })
}

const getData = async () => {
  const {
    data: { list },
  } = await getMemberPriceList()
  membershipPlans.value = list
  nowPrice.value = membershipPlans.value[currentMembershipPlan.value].price
}

const isVip = computed(() => {
  return memberStore.isVip
})

onLoad(() => {
  getData()
})
</script>

<style lang="scss" scoped>
.member-card {
  background-color: #f3f3f3;
  border: 1rpx solid #cfcfcf;
  .points {
    border-radius: 9rpx;
    background-color: #dbdcdf;
    color: #777c8b;
  }
  .tag {
    position: absolute;
    top: 0;
    left: 0;
    width: 72rpx;
    height: 35rpx;
    background: #459af7;
    border-radius: 9rpx 0rpx 9rpx 0rpx;
    font-size: 22rpx;
    color: #ffffff;
    text-align: center;
  }
}
.member-card-active {
  background-color: #ecf5ff;
  border: 1rpx solid #459af7;
  .points {
    border-radius: 9rpx;
    background-color: #cbe3fe;
    color: #459af7;
  }
}
</style>
