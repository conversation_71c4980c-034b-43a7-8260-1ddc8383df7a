<template>
  <view
    class="w675rpx h94rpx rounded-47rpx ma mt30rpx bg-white p20rpx p-x-30rpx box-border flex justify-between items-center box-shadow">
    <u-input
      :value="value"
      clearable
      :placeholder="placeholder"
      border="none"
      @change="change"
      @confirm="confirm"></u-input>
    <u-icon name="search" size="30" @click="confirm"></u-icon>
  </view>
</template>

<script setup lang="ts" name="search-input">
const props = defineProps<{
  value?: string
  placeholder: string
}>()

const emits = defineEmits(['onChange', 'onSearch'])

const searchValue = ref(props.value || '')

const change = (e: string) => {
  searchValue.value = e
  emits('onChange', e)
}

const confirm = () => {
  emits('onSearch', searchValue.value)
}
</script>

<style lang="scss" scoped></style>
