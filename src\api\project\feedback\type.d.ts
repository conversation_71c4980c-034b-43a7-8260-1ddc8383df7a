export interface FeedBackItemType {
  _id: string
  f_news: string
  creatorUserId: UserIdData
  f_title: string
  f_bpic: UploadImgData[]
  f_detaile: string
  f_stauts: '待回复' | '已回复'
  f_pic: UploadImgData[]
  creatorTime: number
  f_enable_name: number
  lastModifyUserId?: UserIdData
  lastModifyTime?: number
}
export interface FeedBackSubmitItemType {
  f_title: string
  f_detaile: string
  f_pic: {
    url: string
    name: string
  }[]
  f_enable_name: number
}
