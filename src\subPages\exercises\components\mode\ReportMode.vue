<template>
  <view>
    <!-- header -->
    <view class="relative">
      <InfoBar :cur-index="curIndex" :topic-list-length="topicList.length"></InfoBar>
      <view class="pb120rpx">
        <SheetItem
          :detail="topicList[curIndex]"
          :collect="false"
          optionReadOnly
          :index="curIndex + 1"></SheetItem>
        <!-- 答案 -->
        <view class="topic-result flex items-center flex-wrap mt20rpx">
          <mp-html
            class="correct h-fit margin-r-24"
            :preview-img="false"
            :content="`正确答案：${arrayToString(curTopic.answer)}`" />
          <view
            v-if="curTopic.user_answer"
            class="flex h-fit text-state"
            :class="{
              correct: correct(),
              wrong: !correct(),
            }">
            <mp-html
              class="margin-r-24"
              :content="`我的答案${arrayToString([curTopic.user_answer])}`" />
            <u-icon v-if="correct()" name="checkmark" color="#61c5a1" size="28rpx" />
            <u-icon v-else name="close" size="28rpx" color="#ff776a" />
          </view>
        </view>
        <!-- 解析 -->
        <view>
          <view class="analysis-wrap mt20rpx">
            <view class="analysis-head"> 解析 </view>
            <view class="analysis-main margin-t-20">
              <view class="analysis-cont margin-t-20">
                <view class="hide-warp after">
                  <mpHtml
                    class="context u-main-color"
                    :content="replaceSpanWithLatex(curTopic.analysis)"></mpHtml>
                </view>
              </view>
            </view>
          </view>
        </view>
        <!-- 考点 -->
        <view class="analysis-wrap mt20rpx">
          <view class="analysis-head"> 考点 </view>
          <view class="analysis-main margin-t-20">
            <mpHtml
              class="context u-main-color"
              :content="arrayToString(curTopic.knowledgePoints)"></mpHtml>
          </view>
        </view>
      </view>
    </view>
    <!-- tabbar -->
    <u-tabbar :placeholder="false" inactive-color="#459AF7">
      <u-tabbar-item text="上一题" @tap="pickTest(0)" icon="play-left"> </u-tabbar-item>
      <u-tabbar-item text="题卡" @tap="showPopup = true">
        <template #inactive-icon>
          <image
            class="tabbar-icon"
            :src="getSystemImg('6747e9ed0a34815816f11159/669bb8940bb07d7cd6ed3bae')">
          </image>
        </template>
      </u-tabbar-item>
      <u-tabbar-item text="下一题" @tap="pickTest(1)" icon="play-right"> </u-tabbar-item>
    </u-tabbar>
    <!-- 弹窗 -->
    <u-popup :show="showPopup" :round="10" mode="bottom" closeable @close="showPopup = false">
      <view class="answer-card">答题卡</view>
      <view class="half">
        <u-grid :border="false" col="5">
          <view
            v-for="(topic, i) in topicList"
            :key="i"
            class="topic-item"
            :class="topic?.state"
            @tap="pickerByPopup(i)">
            {{ i + 1 }}
          </view>
        </u-grid>
      </view>
    </u-popup>
  </view>
</template>
<script setup lang="ts" name="report-mode">
// 题目选项
import SheetItem from '../SheetItem.vue'
import InfoBar from '../InfoBar.vue'
// 数学公式mphtml 开启扩展latex
import mpHtml from '../mp-html/mp-html.vue'
import { getSystemImg } from '@/utils'
// 工具类
import { replaceSpanWithLatex } from '@/utils/index'
// 加载页面
const props = defineProps({
  topicList: {
    type: Array,
    default: [],
  } as any,
})
// 类型
import type { TopicItem } from '@/api/project/exercises/type'
// 当前题目
const curTopic = ref({} as TopicItem)

// 当前下标
const curIndex = ref(0)
// 工具类
import { showToast } from '@/utils'

// 答题卡
const showPopup = ref(false)
onMounted(() => {
  curTopic.value = props.topicList[curIndex.value]
})
// 答案解析
function arrayToString(arr: any[], separator = ', '): string {
  if (!Array.isArray(arr)) {
    return '暂无'
  }
  return arr.join(separator)
}
function correct() {
  return arraysEqual([curTopic.value.user_answer], curTopic.value.answer)
}
// 判断对错
function arraysEqual(arr1: any[], arr2: string[]): boolean {
  if (arr1?.length !== arr2?.length) {
    return false
  }
  const sortedArr1 = arr1.slice().sort()
  const sortedArr2 = arr2.slice().sort()
  return sortedArr1.every((value, index) => value === sortedArr2[index])
}
// 选题
function pickTest(e: number) {
  if (e === 1) {
    // 下一题
    if (curIndex.value >= props.topicList.length - 1) {
      showToast('已经是最后一题', 'error')
      return
    }
    curIndex.value++
  } else {
    // 上一题
    if (curIndex.value <= 0) {
      showToast('已经是第一题', 'error')
      return
    }
    curIndex.value--
  }
  curTopic.value = props.topicList[curIndex.value]
}
// 点击答题卡跳转到指定题目
function pickerByPopup(e: number) {
  // 其他可以点击答题卡跳转题目
  showPopup.value = false
  curIndex.value = e
  curTopic.value = props.topicList[curIndex.value]
}
</script>

<style lang="scss" scoped>
@import '@/common/css/index.scss';
@import '@/common/css/project.scss';
.tabbar-icon {
  width: 20px;
  height: 20px;
}
.u-tabbar__icon {
  width: 17px;
  height: 17px;
}
.answer-card {
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
  text-align: center;
  margin-top: 16rpx;
}
.half {
  max-height: 50vh;
  overflow: auto;
}
.topic-item {
  width: 70rpx;
  height: 70rpx;
  background-color: #f6f7f8;
  border-radius: 36rpx;
  opacity: 1;
  text-align: center;
  line-height: 70rpx;
  margin: 20rpx 40rpx;
  &.select {
    background: $u-theme-color;
    color: #fff;
  }
  &.wrong {
    background: #ff776a;
    color: #fff;
  }
}
$pd_l_r: 30rpx;
.analysis-wrap {
  padding: 24rpx $pd_l_r;
  background: #ffffff;
  .analysis-head {
    position: relative;
    line-height: 1.4;
    &::before {
      $w: 8rpx;
      content: '';
      position: absolute;
      width: $w;
      height: 100%;
      background-color: $u-theme-color;
      left: $w - $pd_l_r;
    }
    .analysis-correction {
      float: right;
      font-size: 30rpx;
    }
  }
  .analysis-main {
    padding: 0 0.5em;
    .analysis-answer {
      margin: 12rpx 0;
    }
    .hide-warp {
      .context {
        text-indent: 2em;
        min-height: 4em;
        ._root > view {
          font-size: 24rpx;
        }
      }
    }
  }
}
.topic-result {
  background: #fff;
  text-align: center;
  line-height: 70rpx;
  font-size: 28rpx;
  padding: 0 30rpx;
  .correct {
    color: #61c5a1;
  }
  .wrong {
    color: #ff776a;
  }
  text {
    margin-right: 1em;
    line-height: 2.5;
  }
  & > text {
    margin-right: 4em;
  }
}
</style>
