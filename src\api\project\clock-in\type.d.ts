// 创建打卡
export interface IAddClockReq {
  is_complete: number
  teacherId: any
  date: number
  type: string
  time: number[]
  department: string[]
  content: string
}

// 打卡完成情况
export interface IClockInConfirmData {
  _id: string
  task_name: string
  user_id: string
  user_name: string
  sum_date: string
  task_date: string
  task_id: string
  is_commit: string
  creatorTime: number
}

// 打卡记录
export interface IClockInRecords {
  date: number
  type: '拍照打卡' | '在线学习' | '在线刷题'
  time: number[]
  content: string
  is_complete: number
  _id: string
}

//在线学习或者在线刷题打卡记录
export interface IOnlineClockInRecords {
  complete_id?: string //报告id
  catalog_id?: string //章节id
  topic_id?: string //题目id
  content: string
  date: number
  is_complete: number
  section_id?: string //课程id
  time: number[]
  type: '拍照打卡' | '在线打卡' | '刷题打卡'
  _id: string
}

// 创建拍照打卡完成记录
export interface IClockInComplete {
  is_complete: number
  photo: UploadImgData[]
  content: string
}

// 创建派发打卡记录
export interface IDistributeTask {
  _id?: string
  sum_date: string //统计时间
  user_id: string
  user_name: string
  task_id: string
  task_name: string
  task_date: string //任务时间
  is_commit: string //是否完成
  report_id?: string //学习报告id
}

// 专项练习打卡
export interface ISpecialClockIn {
  _id?: string
  paperId?: string
  code: string
  special_type: '专项打卡' | '日积月累'
  images: UploadImgData[]
  grade: string
  subject: string
  creatorTime?: number
}

// 专项练习打卡成绩记录
interface ISpecialClockInScore {
  _id: string
  creatorUserId: string
  tableField102: ISpecialClockInTopic[]
  creatorTime: number
  userId: string
  paperId: string
}

// 专项练习打卡题目
interface ISpecialClockInTopic {
  userAnswer: string
  topicImg: UploadImgData[]
  ai_isCorrect: string
  ai_score: number
  index: number
  topic_id: string
  _id: string
  judges: any[]
  isCorrect: string
}
