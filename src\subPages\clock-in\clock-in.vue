<template>
  <view class="w690rpx h450rpx ma mt30rpx rounded-20rpx bg-white p30rpx box-border">
    <view class="text-#303133 text-28rpx">学习内容</view>
    <view class="bg-#f5f7fa rounded-10rpx p20rpx m-y-20rpx text-30rpx text-#606266">{{
      data.content
    }}</view>
    <view class="text-#303133 m-y-20rpx text-28rpx">拍照</view>
    <up-upload
      :file-list="fileList"
      name="file"
      :disabled="data.is_complete == 1"
      multiple
      :deletable="data.is_complete !== 1"
      :max-count="4"
      @after-read="afterRead"
      @delete="deletePic"></up-upload>
  </view>
  <view v-if="flag" class="absolute w-full bottom-30rpx p40rpx box-border">
    <u-button text="提交保存" type="primary" shape="circle" @click="submit"></u-button>
  </view>
</template>

<script setup lang="ts" name="clock-in-clock-in">
import { CompleteClockInReq, getClockInRecordsById } from '@/api/project/clock-in'
import type { IClockInComplete } from '@/api/project/clock-in/type'
import { miniProgramUploadFile, showToast, showToastBack } from '@/utils'

const data = ref<any>({
  is_complete: 0,
  photo: [] as UploadImgData[],
  content: '',
})

const flag = ref(true)

const baseUrl = import.meta.env.VITE_SERVE

// 图片上传
interface afterReadParams {
  file: {
    url: string
    status: string
    message: string
    name: string
  }[]
}
const fileList = ref<afterReadParams['file']>([])

// 删除图片
const deletePic = ({ index }: { index: number }) => {
  fileList.value.splice(index, 1)
}

// 新增图片
const afterRead = async ({ file }: afterReadParams) => {
  const initIndex = fileList.value.length
  // 添加初始状态
  for (const item of file) {
    fileList.value.push({
      ...item,
      status: 'uploading',
      message: '上传中',
    })
  }
  // 上传并修改数据
  for (let index = 0; index < file.length; index++) {
    const item = file[index]
    await miniProgramUploadFile(item.url)
      .then(({ data }) => {
        fileList.value.splice(
          initIndex + index,
          1,
          Object.assign(item, {
            status: 'success',
            message: '',
            name: data.name,
            url: data.url,
          })
        )
      })
      .catch(() => {
        // 删除失败文件
        fileList.value.splice(initIndex + index, 1)
      })
  }
}

const submit = async () => {
  data.value.photo = fileList.value.map(item => {
    return {
      name: item.name,
      url: item.url,
    }
  })

  data.value.is_complete = 1

  CompleteClockInReq(data.value, data.value._id).then(res => {
    showToastBack('打卡成功')
  })
}

onLoad(async (val: any) => {
  console.log(val)

  const res = await getClockInRecordsById(val.taskId)
  data.value = res.data.list[0]

  if (res.data.list[0].is_complete) {
    flag.value = false
    fileList.value = [
      ...data.value.photo.map((item: any) => {
        return {
          ...item,
          url: `${baseUrl}${item.url}`,
        }
      }),
    ]
  }
})
</script>

<style>
page {
  background-color: #f3f4f6;
}
</style>

<style lang="scss" scoped></style>
