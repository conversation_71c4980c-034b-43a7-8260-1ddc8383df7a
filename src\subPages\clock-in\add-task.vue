<template>
  <SelectDate :flag="true" @change="seleDate">
    <template #right>
      <u-tag text="日程同步" type="primary" @click="synshronous"></u-tag>
    </template>
  </SelectDate>
  <!-- 打卡内容 -->
  <view
    v-for="(item, index) in taskList"
    :key="item.id"
    class="w690rpx min-h-330rpx relative ma mt30rpx rounded-20rpx bg-white box-shadow">
    <view class="w-full h30rpx"></view>
    <view class="flex justify-between overflow-hidden">
      <view class="flex mt10rpx items-center relative right-130rpx lh-50rpx">
        <view class="w200rpx h50rpx bg-#4599f6 rounded-40rpx pr10rpx text-right text-white mr20rpx"
          >{{ index + 1 }}.
        </view>
        学习内容
      </view>
    </view>
    <view class="p20rpx box-border">
      <u-form ref="form1" :rules="rules" :model="item">
        <u-form-item prop="content">
          <up-textarea v-model="item.content" placeholder="请输入学习内容"></up-textarea>
        </u-form-item>
      </u-form>
    </view>
    <view v-if="index !== 0" class="absolute top-[-20rpx] right-[-20rpx]" @click="dele(index)">
      <u-icon name="close-circle-fill" size="28" color="#c8c8ca"></u-icon>
    </view>
  </view>
  <button class="m30rpx but-shadow" @click="addTask">十</button>
  <view class="fixed w-full bottom-30rpx p40rpx box-border">
    <u-button text="提交保存" type="primary" shape="circle" @click="submit"></u-button>
  </view>
  <view class="w-full h150rpx"></view>
</template>

<script setup lang="ts" name="clock-in-add-task">
import { batchAddClockData, getMyTeacher } from '@/api/project/clock-in'
import type { IAddClockReq } from '@/api/project/clock-in/type'
import SelectDate from '@/components/SelectDate.vue'
import useUserStore from '@/store/modules/user'
import { showToast, showToastBack } from '@/utils'
const userStore = useUserStore()

const rules = reactive({
  content: [
    {
      required: true,
      message: '学习内容不能为空',
      trigger: ['blur', 'change'],
    },
  ],
})

const selectWeekList = ref<number[]>([])
const seleDate = (list: number[]) => {
  selectWeekList.value = list
}

const taskList = ref([
  {
    id: uni.$u.guid(10),
    content: '',
    date: 0,
    type: '拍照打卡',
    time: [new Date().setHours(8, 0, 0, 0), new Date().setHours(9, 0, 0, 0)],
  },
])

const addTask = () => {
  taskList.value.push({
    id: uni.$u.guid(10),
    content: '',
    date: 0,
    type: '拍照打卡',
    time: [new Date().setHours(8, 0, 0, 0), new Date().setHours(9, 0, 0, 0)],
  })
}

const dele = (index: number) => {
  uni.showModal({
    title: '提示',
    content: '是否删除该任务',
    showCancel: true,
    success: success => {
      if (success.confirm) {
        taskList.value.splice(index, 1)
      }
    },
  })
}

const submit = async () => {
  if (selectWeekList.value.length === 0) {
    showToast('未选择日期')
    return 0
  }
  if (taskList.value.filter(item => item.content !== '').length === 0) {
    showToast('未输入内容')
    return 0
  }
  const teacherList = await getMyTeacher(userStore.userInfo!.phone)
  const teacherId = teacherList.data.list[0].teacherId
  const list: IAddClockReq[] = []
  taskList.value.forEach((item, index) => {
    delete item.id
    selectWeekList.value.forEach((ite, inde) => {
      list.push({
        ...item,
        is_complete: 0,
        teacherId: teacherId,
        department: userStore.userInfo!.departmentId || [],
        date: ite,
      })
    })
  })
  batchAddClockData(list).then(res => {
    showToastBack('操作成功')
  })
}

const synshronous = async () => {
  if (selectWeekList.value.length === 0) {
    showToast('请选择日期后再同步')
    return 0
  }
  if (taskList.value.filter(item => item.content !== '').length === 0) {
    showToast('请输入内容后再同步')
    return 0
  }
  const startTime = selectWeekList.value[selectWeekList.value.length - 1] / 1000
  let desc = []
  desc = taskList.value.map(item => item.content)
  await uni.addPhoneCalendar({
    title: '拍照打卡',
    allDay: true,
    startTime,
    endTime: String(startTime),
    description: desc.join('\n'),
  })
}
</script>

<style>
page {
  background-color: #f3f4f6;
}
</style>

<style lang="scss" scoped>
.but-shadow {
  box-shadow: 6rpx 6rpx 10rpx 3rpx #e5e5e5;
}

// 日历样式
::v-deep .u-calendar-header .u-calendar-header__subtitle {
  display: block;
  font-size: 36rpx;
}

::v-deep .u-calendar-header .u-calendar-header__title {
  display: none;
}

::v-deep .u-calendar-month-wrapper .u-calendar-month__title {
  display: block;
  font-size: 36rpx;
}
</style>
