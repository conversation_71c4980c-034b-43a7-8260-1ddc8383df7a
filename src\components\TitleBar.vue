<template>
  <up-navbar
    :title="title"
    placeholder
    :border="flag"
    left-icon=""
    :bg-color="navColor"
    :title-style="{ color: textColor, fontSize: fsize }">
    <template #left>
      <view v-if="showBack" class="u-nav-slot">
        <up-icon name="arrow-left" size="19" :color="textColor" @click="back"></up-icon>
      </view>
    </template>
  </up-navbar>
</template>

<script setup lang="ts">
interface Props {
  title?: string
  showBack?: boolean
  navColor?: string
  textColor?: string
  flag?: boolean
  fsize?: string
}
withDefaults(defineProps<Props>(), {
  showBack: false,
  navColor: '#ffffff',
  textColor: '#333333',
  flag: true,
})

function back() {
  uni.navigateBack().catch(() => {
    uni.switchTab({
      url: '/pages/index/index',
      fail: () => {
        uni.reLaunch({
          url: '/pages/index/index',
        })
      },
    })
  })
}
</script>

<style lang="scss"></style>
