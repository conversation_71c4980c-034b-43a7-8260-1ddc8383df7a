<template>
  <view v-if="Object.keys(detail).length !== 0" class="w-full">
    <u-image :src="assembleImgData(detail?.school_banner?.[0] || '')" width="100%" height="300rpx">
    </u-image>
  </view>
  <SchoolItem
    :item-data="detail"
    :img-size="{
      width: '180rpx',
      height: '180rpx',
    }"
    :flag="true"
    :collect="collect"
    @click-collect="collect = !collect" />
  <up-read-more v-if="detail?.school_intro" :toggle="true" show-height="150rpx" close-text="展开">
    <view class="p20rpx">
      <rich-text :nodes="detail.school_intro"></rich-text>
    </view>
  </up-read-more>
  <view class="flex justify-around m-y-20rpx">
    <view
      v-for="item in detailInfo"
      :key="item.key"
      class="w205rpx h134rpx rounded-10rpx bg-#F5F5F5 p20rpx box-border text-24rpx text-right">
      <view class="text-#969DAB">{{ item.title }}</view>
      <view class="text-#000000 mt10rpx">
        <text class="text-50rpx text-#333333 fw-bold">
          {{ item.value }}
        </text>
        {{ item.unit }}
      </view>
    </view>
  </view>
  <view class="w-full bg-#f3f4f6 pb20rpx">
    <up-sticky>
      <view class="bg-white mt20rpx sticky">
        <up-tabs
          :list="tabs"
          :item-style="{ width: '350rpx', height: '100rpx' }"
          :line-width="40"
          :line-height="6"
          :current="tabIndex"
          @change="tabChange"></up-tabs>
      </view>
    </up-sticky>
    <!-- 招生计划 -->
    <view>
      <Auchor id="plan" ref="auchor1" @scrol="e => toId(0, e)" />
      <view class="flex justify-between items-center">
        <Section title="招生计划" />
        <view
          class="w132rpx h42rpx flex flex-center rounded-21rpx bg-white text-22rpx text-#333333"
          @click="showYear = !showYear">
          <text>
            {{ currentYear }}
          </text>
          <view
            class="ml10rpx"
            style="transition: transform 0.5s"
            :style="`transform: rotate(${showYear ? 180 : 0}deg);`">
            <u-icon name="arrow-up-fill" size="15"></u-icon>
          </view>
        </view>
      </view>
      <view class="p10rpx">
        <MyTable :titles="studentTitle" :list="studentData" />
      </view>
      <view class="text-#FC3838 text-18rpx p20rpx">
        该统计是由湖南专升本网报平台的志愿填报前截止数据与学校官方公布的数据整合生成，仅供参考
      </view>
    </view>
    <!-- 考试科目 -->
    <view>
      <Auchor id="exam" ref="auchor2" @scrol="e => toId(1, e)" />
      <Section title="考试科目" />
      <view class="p10rpx">
        <ExamTable :titles="examTitle" :list="examData" />
      </view>
    </view>
    <!-- 简章考纲 -->
    <view>
      <Auchor id="general" ref="auchor3" @scrol="e => toId(2, e)" />
      <Section title="简章考纲" />
      <FileItem
        v-for="(item, index) in examSyllabus"
        :key="index"
        :name="item?.[0].name"
        :url="item?.[0].url" />
      <view v-if="examSyllabus.length === 0" class="mt40rpx">
        <up-empty text="暂无文件"></up-empty>
      </view>
    </view>
    <!-- <swiper :style="`height:${swiperHeight}px`" :current="tabIndex" @change="swiperChange">
      <swiper-item item-id="111"> </swiper-item>
      <swiper-item item-id="222"> </swiper-item>
      <swiper-item item-id="333"> </swiper-item>
    </swiper> -->
  </view>
  <up-action-sheet
    :actions="listYear"
    title="选择年份"
    :show="showYear"
    @close="showYear = false"
    @select="selectYear"></up-action-sheet>
  <FloatWeixin />
</template>

<script setup lang="ts" name="school-detail">
import MyTable from '../report-major/MajorTable/index.vue'
import SchoolItem from './SchoolItem/index.vue'
import FileItem from './FileItem/index.vue'
import ExamTable from './ExamTable/index.vue'
import Auchor from '@/components/Auchor.vue'
import FloatWeixin from '@/components/FloatWeixin.vue'
import Section from '@/components/Section.vue'
import { getBKSchoolData, getBKSchoolMajorDataList } from '@/api/project/index'
import type { IBKSchoolData, IBKSchoolMajorData } from '@/api/project/index/type'
import { assembleImgData, extractNumbers, getSystemImg } from '@/utils'
import useIndexStore from '@/store/modules/index'

const detail = ref<IBKSchoolData>({} as IBKSchoolData)

// 本科院校专业列表
const majorList = ref<IBKSchoolMajorData[]>([])
// 招生计划表格数据
const studentTitle = ['专业名称', '招生计划', '报考人数', '录取人数', '分数线', '录取率', '学费']
const studentData = ref<string[][]>([])
const listYear = ref<{ name: string }[]>([])
const showYear = ref(false)

const currentYear = ref()
const examTitle = ['专业名称', '考试科目+参考材料']
const examData = ref<
  {
    major: string
    // subject: string
    // books: string
    list: string[]
  }[]
>([])
const handleStudent = () => {
  studentData.value = majorList.value
    .filter(item => item.sum_year === currentYear.value)
    .map(item => {
      return [
        item.Undergraduate_name,
        item.enrollment_plan,
        item.application_nums,
        item.admission_nums,
        item.score,
        item.acceptance_rate,
        item.tuition,
      ]
    })
}
const handleExam = () => {
  examData.value = majorList.value
    .filter(item => item.sum_year === currentYear.value)
    .map(item => {
      return {
        major: item.Undergraduate_name,
        list: item.other_info.split('\n'),
      }
      // return {
      //   major: item.Undergraduate_name,
      //   subject: item.other_info.split('\n')[0],
      //   books: item.other_info.split('\n').splice(1).join(''),
      // }
    })
}
// 简章考纲
const examSyllabus = ref<UploadFzData[][]>([])
const handleSyllabus = () => {
  examSyllabus.value = majorList.value
    .filter(item => item.sum_year === currentYear.value)
    .map(item => {
      return item.annex_files
    })
    .filter(files => files.length > 0)
}
const selectYear = ({ name }: { name: string }) => {
  currentYear.value = name
  handleStudent()
  handleExam()
  handleSyllabus()
}

// 收藏状态
const collect = ref(false)

const detailInfo = ref([
  {
    key: 'found_time',
    title: '建校时间',
    value: '1900',
    unit: '年',
  },
  {
    key: 'floor_space',
    title: '占地面积',
    value: '1000',
    unit: '亩',
  },
  {
    key: 'school_ranking',
    title: '高校排行',
    value: '150',
    unit: '名',
  },
])

// tabs导航
const flag = ref(0)
const tabs = ref([
  {
    id: 1,
    name: '招生计划',
    top: 0,
    url: '#plan',
  },
  {
    id: 2,
    name: '考试科目',
    top: 0,
    url: '#exam',
  },
  {
    id: 3,
    name: '简章考纲',
    top: 0,
    url: '#general',
  },
])
// 当前tab
const tabIndex = ref(0)
const tabChange = (val: any) => {
  tabIndex.value = val.index

  uni.pageScrollTo({
    scrollTop: val.top - 144,
  })
}
const height = ref(0)
const auchor1 = ref<InstanceType<typeof Auchor>>()
const auchor2 = ref<InstanceType<typeof Auchor>>()
const auchor3 = ref<InstanceType<typeof Auchor>>()
const setTab = () => {
  auchor1.value?.refres()
  auchor2.value?.refres()
  auchor3.value?.refres()
  setTimeout(() => {
    if (auchor1.value!.scrollTop < height.value - 200) {
      tabIndex.value = 0
    }
    if (auchor2.value!.scrollTop < height.value - 200) {
      tabIndex.value = 1
    }
    if (auchor3.value!.scrollTop < height.value - 200) {
      tabIndex.value = 2
    }
  }, 50)
}
const toId = (id: number, val: any) => {
  if (flag.value++ < 3) {
    tabs.value[id].top = val
  }
}
onPageScroll((e: any) => {
  uni.$u.debounce(setTab, 10, false)
})

const swiperChange = (val: any) => {
  tabIndex.value = val.detail.current
}
const swiperHeight = ref(400)

onLoad(async (val: any) => {
  detail.value = (await getBKSchoolData(val.id)).data
  uni.setNavigationBarTitle({
    title: detail.value.school_name,
  })
  detailInfo.value[0].value = extractNumbers(detail.value.found_time).strs
  detailInfo.value[1].value = extractNumbers(detail.value.floor_space).strs
  detailInfo.value[2].value = extractNumbers(detail.value.school_ranking).strs
  majorList.value = (await getBKSchoolMajorDataList(detail.value.school_name)).data.list
  listYear.value = [...new Set(majorList.value.map(item => item.sum_year))].map(item => {
    return {
      name: item,
    }
  })
  currentYear.value = `${await useIndexStore().getCurrentYear()}`

  const data: any = await uni.getSystemInfoAsync()
  height.value = data.safeArea.height

  handleStudent()

  handleExam()
  handleSyllabus()
})

watchEffect(() => {
  if (tabIndex.value === 0) {
    swiperHeight.value =
      studentData.value.length === 0
        ? studentData.value.length * 60 + 150 + 100
        : studentData.value.length * 60 + 150
  }
  if (tabIndex.value === 1) {
    swiperHeight.value =
      examData.value.length === 0
        ? examData.value.length * 100 + 150 + 100
        : examData.value.length * 100 + 150
  }
  if (tabIndex.value === 2) {
    swiperHeight.value =
      examSyllabus.value.length === 0
        ? studentData.value.length * 110 + 150 + 100
        : studentData.value.length * 110 + 150
  }
})
</script>

<style lang="scss" scoped></style>
