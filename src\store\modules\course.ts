import { defineStore } from 'pinia'
import type { ICatalogTableType, ICourseDirecToryResuleType } from '@/api/project/course/type'

export default defineStore(
  'course',
  () => {
    const dirList = ref<ICatalogTableType[]>([])
    const currentIndex = ref<number>(0)
    const playStatus = ref<boolean>(false)

    const currentPlay = computed(() => {
      return dirList.value[currentIndex.value]
    })

    const setPlayStatus = (status: boolean) => {
      playStatus.value = status
    }

    const setCurrentIndex = (index: number) => {
      currentIndex.value = index
    }

    // 上一节
    const prevVideo = () => {
      if (currentIndex.value > 0) {
        currentIndex.value--
      }
    }
    // 下一节
    const nextVideo = () => {
      if (currentIndex.value < dirList.value.length - 1) {
        currentIndex.value++
      }
    }

    const setDirList = (data: ICourseDirecToryResuleType) => {
      dirList.value = data.tableField107
      currentIndex.value = 0
      playStatus.value = false
    }
    return {
      dirList,
      currentPlay,
      playStatus,
      currentIndex,
      prevVideo,
      nextVideo,
      setDirList,
      setPlayStatus,
      setCurrentIndex,
    }
  },
  {
    persist: {
      paths: [],
      storage: {
        getItem: uni.getStorageSync,
        setItem: uni.setStorageSync,
      },
    },
  }
)
