<template>
  <view class="pb-120rpx">
    <u-tabs
      :list="tabList"
      :current="curIndex"
      line-width="80rpx"
      line-height="8rpx"
      line-color="#459af7"
      :active-style="{
        color: '#459AF7',
        fontWeight: '600',
      }"
      :inactive-style="{
        color: '#333333',
        fontSize: '30rpx',
      }"
      :item-style="{ width: '50%', height: '100rpx' }"
      @change="onTabChange">
    </u-tabs>
    <LoadingPage
      class="h-full"
      empty-text="暂无课程"
      loading-text="课程加载中..."
      :loading="loading"
      :empty="empty">
      <template #default="{ show }">
        <!-- 课程 -->
        <view v-if="groupedData.length > 0" class="mb-100rpx">
          <view
            v-for="(item, index) in groupedData"
            :key="index"
            class="w690rpx bg-white rounded-10rpx mt20rpx mx-auto pt-22rpx pb-30rpx px-44rpx box-border">
            <view>
              <view class="text-36rpx color-#459AF7 font-600">{{ item.school }}</view>
              <view v-for="(item1, index1) in item.list" :key="item1._id" class="course-item">
                <view class="text-25rpx color-#333333 mt-10rpx relative left-[-10rpx]">
                  《{{ item1.course_name }}》<text class="ml-18rpx">{{ item1.chapter }}</text></view
                >
                <view class="text-25rpx color-#666666 mt-10rpx"
                  >{{ item1.startTime }}--{{ item1.endTime }}</view
                >

                <view class="booking-tag">
                  <up-tag
                    width="150rpx"
                    height="60rpx"
                    :plain="item1.status === 'active' ? true : false"
                    :text="
                      item1.status === 'completed'
                        ? '已完成'
                        : item1.status === 'expired'
                        ? '已结束'
                        : item1.status === 'active'
                        ? '已预约'
                        : '&nbsp;&nbsp;预约&nbsp;&nbsp;'
                    "
                    :type="
                      item1.status === 'completed'
                        ? 'success'
                        : item1.status === 'expired'
                        ? 'error'
                        : 'primary'
                    "
                    shape="circle"
                    @click="item1.status === 'inactive' ? booking(item1, index, index1) : null">
                  </up-tag>
                </view>
              </view>
            </view>
          </view>
        </view>
        <view v-else class="mt200rpx">
          <up-empty mode="data" text="当日无课程"> </up-empty>
        </view>
      </template>
    </LoadingPage>
  </view>
  <up-modal
    width="623rpx"
    height="463rpx"
    title=""
    :show="showConfirmModal"
    content="22"
    show-cancel-button
    @confirm="handleConfirm"
    @cancel="showConfirmModal = false">
    <view>
      <view class="text-36rpx text-center color-#459AF7">预约确认</view>
      <view class="w439rpx text-28rpx color-#878787 mt36rpx">{{ confirmModalContent }}</view>
    </view>
  </up-modal>
  <up-modal
    width="623rpx"
    height="463rpx"
    title=""
    :show="showSuccessModal"
    content="22"
    show-cancel-button>
    <view>
      <view class="flex justify-center">
        <image
          src="https://kindoucloud.com/api/file/previewImage/6613669f955ed0316d6434c9/66b9c9fdb23ec40f60a6eb04"
          class="w187rpx h148rpx"></image>
      </view>
      <view class="text-36rpx text-center color-#459AF7">预约成功</view>
      <view class="w439rpx text-28rpx color-#878787 mt36rpx">{{ successModalContent }}</view>
    </view>
    <template #confirmButton>
      <button
        class="w439rpx h74rpx rounded-10rpx bg-#459AF7 color-#FFFFFF text-28rpx lh-74rpx"
        @click="showSuccessModal = false">
        我知道了
      </button>
    </template>
  </up-modal>
  <view
    class="fixed bottom-0 left-0 w-full h-100rpx bg-white text-center lh-100rpx c-[#666666] text-28"
    @tap="toMyBooking">
    我的预约
  </view>
</template>

<script setup lang="ts" name="book-lesson">
import type { BookCourseListItem } from '@/api/project/booking/type'
import LoadingPage from '@/components/LoadingPage.vue'
import { createBooking, getBookCourseList, getMyActiveList } from '@/api/project/booking/index'
import { showToast } from '@/utils/index'
import useDateFormatter from '@/hooks/useDateFormatter'

interface DayItem {
  dt: number
  month: number
  year: number
  hasCourse: boolean
  dateText: string
}

interface CourseGroup {
  school: string
  list: BookCourseListItem[]
}
// 组件状态
const loading = ref(true)
const empty = ref(false)
const curIndex = ref(0)
const showConfirmModal = ref(false)
const showSuccessModal = ref(false)

// 数据相关
const list = ref<BookCourseListItem[]>([])
const confirmModalContent = ref('')
const successModalContent = ref('')
const tempBookingData = ref<{ item: BookCourseListItem; index: number; index1: number } | null>(
  null
)
const pickedDate = ref(new Date())
const date = ref(new Date())

// 日期格式化
const { formatDate } = useDateFormatter('MM-DD')

// 计算属性：日期列表（只显示今明两天）
const dayList = computed<DayItem[]>(() => {
  const now = date.value
  const nowDate = now.getDate()
  const dayItems: DayItem[] = []

  // 只循环两次，生成今天和明天
  for (let index = 0; index < 2; index++) {
    const currentDate = new Date(now)
    currentDate.setDate(nowDate + index)
    const timestamp = currentDate.getTime()

    const hasCourse = list.value.some(course =>
      compareDatesOnly(course.start_class_date, timestamp)
    )

    // 生成日期文本，格式为 "x月x日"
    const monthText = `${currentDate.getMonth() + 1}月`
    const dayText = `${currentDate.getDate()}日`
    const dateText = `${monthText}${dayText}`

    dayItems.push({
      dt: currentDate.getDate(),
      month: currentDate.getMonth(),
      year: currentDate.getFullYear(),
      hasCourse,
      dateText,
    })
  }

  return dayItems
})

// 计算属性：u-tabs 需要的列表格式
const tabList = computed(() => {
  return dayList.value.map(item => ({
    name: item.dateText,
  }))
})

// 计算属性：根据选中日期对课程进行分组
const groupedData = computed<CourseGroup[]>(() => {
  const result: Record<string, CourseGroup> = {}

  const pickedTimestamp = new Date(pickedDate.value).getTime()
  list.value.forEach(item => {
    if (compareDatesOnly(item.start_class_date, pickedTimestamp)) {
      const { school } = item

      if (!result[school]) {
        result[school] = {
          school,
          list: [],
        }
      }

      result[school].list.push(item)
    }
  })

  return Object.values(result)
})
// 课程预约处理
async function booking(item: BookCourseListItem, index: number, index1: number) {
  // 课程状态检查
  if (item.status === 'expired') {
    showToast('当前课程已超时')
    return
  }

  if (item.status === 'completed') {
    showToast('当前课程已完成')
    return
  }

  if (item.status === 'active') {
    showToast('当前课程您已经预约')
    return
  }

  // 课程时间检查
  const courseStartDate = new Date(item.startTime)
  if (courseStartDate < new Date()) {
    groupedData.value[index].list[index1].status = 'expired'
    showToast('当前课程已超时')
    return
  }

  // 显示确认弹窗
  confirmModalContent.value = `您确定要预约${formatDate(item.start_class_date)}日${
    item.startTime
  }-${item.endTime}${item.school}${item.course_name}课程吗？`
  tempBookingData.value = { item, index, index1 }
  showConfirmModal.value = true
}

async function handleConfirm() {
  if (!tempBookingData.value) return

  const { item, index, index1 } = tempBookingData.value
  showConfirmModal.value = false

  try {
    await createBooking(item)

    successModalContent.value = `您已经成功预约${formatDate(item.start_class_date)}日${
      item.startTime
    }-${item.endTime}${item.school}${item.course_name}课程。`
    showSuccessModal.value = true
    groupedData.value[index].list[index1].status = 'active'
  } catch (error) {
    showToast('预约失败，请稍后重试')
    console.error('Booking error:', error)
  } finally {
    tempBookingData.value = null
  }
}
// 函数：获取起始和结束日期
function getDateRange(baseDate: Date) {
  // 创建当前日期的副本，并设置为当天的 0 点
  const startDate = new Date(baseDate)
  startDate.setHours(0, 0, 0, 0) // 设置为当天的 0 点

  // 创建结束日期的副本，并设置为加1天后的 0 点
  const endDate = new Date(startDate)
  endDate.setDate(endDate.getDate() + 1) // 当前日期加1天
  endDate.setHours(0, 0, 0, 0) // 设置为加1天后的 0 点

  return { startDate, endDate }
}

onMounted(async () => {
  getCourseList()
  setLoadingState()
})
// 获取课程列表数据
async function getCourseList() {
  try {
    const { startDate, endDate } = getDateRange(pickedDate.value)

    // 并行请求课程列表和报名列表
    const [courseResponse, activeResponse] = await Promise.all([
      getBookCourseList(startDate.getTime(), endDate.getTime()),
      getMyActiveList(),
    ])

    const courses = courseResponse.data.list || []
    const myActiveList = activeResponse.data.list || []
    // 处理课程状态
    const processedCourses = courses.map(course => {
      const activeBooking = myActiveList.find(active => active.course_id === course._id)
      const isActive = !!activeBooking

      const courseEndDate = new Date(
        addTimeStringToTimestamp(course.endTime, course.start_class_date)
      )
      const courseStartDate = new Date(
        addTimeStringToTimestamp(course.startTime, course.start_class_date)
      )
      const now = new Date()

      // 判断课程状态
      let status = 'inactive' // 默认可预约

      const isClassStarted = courseStartDate < now // 课程已开始
      const isClassEnded = courseEndDate < now // 课程已结束
      if (isActive) {
        // 已预约的课程，需要进一步判断状态
        if (isClassEnded) {
          status = 'completed' // 已完成（课程结束时间已过）
        } else {
          status = 'active' // 已预约（课程未结束）
        }
      } else {
        // 未预约的课程
        if (isClassStarted) {
          status = 'expired' // 课程已开始，无法预约
        }
      }

      return {
        ...course,
        status,
      }
    })

    list.value = processedCourses
    empty.value = processedCourses.length === 0
  } catch (error) {
    console.error('Error fetching data:', error)
    showToast('获取课程列表失败')
    empty.value = true
  } finally {
    loading.value = false
  }
}

function compareDatesOnly(timestamp1: number, timestamp2: number): boolean {
  return new Date(timestamp1).setHours(0, 0, 0, 0) === new Date(timestamp2).setHours(0, 0, 0, 0)
}
// u-tabs 切换事件
function onTabChange(item: any) {
  const index = item.index
  const selectedItem = dayList.value[index]

  if (selectedItem) {
    pickDate(selectedItem, index)
    // 切换tab时重新获取课程数据
    getCourseList()
  }
}

function pickDate(date: any, index: number) {
  const newDate = new Date()
  newDate.setDate(date.dt)
  newDate.setMonth(date.month)
  newDate.setFullYear(date.year)

  pickedDate.value = newDate
  curIndex.value = index
}
function setLoadingState() {
  setTimeout(() => {
    loading.value = false
  }, 1000)
}
// time TO 时间戳
function addTimeStringToTimestamp(timeString: string, baseTimestamp: number): number {
  // 创建一个日期对象，使用已有的时间戳
  const baseDate = new Date(baseTimestamp)

  // 获取 baseDate 的年、月、日
  const year = baseDate.getFullYear()
  const month = baseDate.getMonth() // 注意：月份从 0 开始
  const day = baseDate.getDate()

  // 构造新的日期时间字符串，使用本地时间
  const dateTimeString = new Date(year, month, day, ...timeString.split(':').map(Number))

  // 获取时间戳（毫秒）
  return dateTimeString.getTime()
}
function toMyBooking() {
  uni.navigateTo({
    url: '/subPages/booking/my-bookings',
  })
}
</script>

<style>
page {
  background-color: #f4f5f7;
}
</style>

<style lang="scss" scoped>
.course-item {
  position: relative;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  margin: 10rpx 0;
  border-bottom: 1px solid #d0d0d0;
  padding-bottom: 24rpx;
  &:last-child {
    border-bottom: none;
    padding-bottom: 0;
    .booking-tag {
      bottom: 0;
    }
  }

  .booking-tag {
    position: absolute;
    right: 0;
    bottom: 24rpx;
  }
}

.title {
  color: $u-primary;
  text-align: center;
  padding: 20rpx 0 0 0;
}
::v-deep .u-tag__content {
  display: flex;
  align-items: center;
}
</style>
