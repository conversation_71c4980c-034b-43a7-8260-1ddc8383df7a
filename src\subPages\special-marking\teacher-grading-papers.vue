<template>
  <view class="w750rpx pb120rpx">
    <view v-for="(ite, inde) in taskFacet" :key="ite._id">
      <view
        v-for="(item, index) in ite.tableField102"
        :key="item._id"
        class="w710rpx min-h374rpx rounded-15rpx bg-white ma my20rpx p20rpx box-border relative">
        <view
          v-if="item.score && item.isCorrect && item.judges.length === 0"
          class="absolute left-[-10rpx] top-[-10rpx]">
          <view class="w60rpx h30rpx bg-#E6F3FF rounded-20rpx flex-center text-20rpx text-#3388FF">
            AI
          </view>
        </view>
        <view class="absolute right-[-10rpx] top-[-10rpx]">
          <view class="w60rpx h30rpx bg-#E6F3FF rounded-20rpx flex-center text-20rpx text-#3388FF">
            {{ item.judges.length > 0 ? '已阅' : '未阅' }}
          </view>
        </view>
        <view class="w-full min-h100rpx cursor-pointer" @tap.capture="markingChange(inde, index)">
          <mp-html v-if="!item?.topicImg" :content="item.topic"></mp-html>
          <view v-else>
            <image :src="getHeadIcon(item.topicImg[0].url)" width="750rpx" mode="widthFix"></image>
          </view>
        </view>
        <up-divider dashed></up-divider>
        <view class="toggle-group">
          <view
            class="toggle-btn1"
            :class="{ active: item.isCorrect === '对' }"
            @click="toggleResult(item, '对', index)"
            >对</view
          >
          <view
            class="toggle-btn2"
            :class="{ active: item.isCorrect === '错' }"
            @click="toggleResult(item, '错', index)"
            >错</view
          >
        </view>
        <view
          class="w670rpx h86rpx rounded-10rpx b-1rpx b-solid b-#D9D9D9 flex justify-between items-center p20rpx box-border">
          <view class="text-28rpx text-#333333">评分：</view>
          <view>
            <up-input
              v-model="item.score"
              type="number"
              placeholder="请输入评分"
              border="none"
              input-align="right"
              readonly
              @change="handleChange(ite, index, $event)"></up-input>
          </view>
        </view>
      </view>
    </view>
  </view>
  <view class="w-full fixed bottom-30rpx p30rpx box-border">
    <u-button text="批改提交" shape="circle" type="primary" @click="handleSubmit"></u-button>
  </view>
  <u-popup
    :overlay-opacity="1"
    :show="markingDialog"
    mode="center"
    :safe-area-inset-bottom="false"
    @close="markingDialog = false">
    <view class="w-750rpx min-h-100rpx">
      <mp-html
        v-if="!taskFacet[currentIndex].tableField102[currentTableIndex]?.topicImg"
        :content="taskFacet[currentIndex].tableField102[currentTableIndex].topic"
        preview-img="all"></mp-html>
      <view v-else>
        <image
          :src="
            getHeadIcon(taskFacet[currentIndex].tableField102[currentTableIndex].topicImg[0].url)
          "
          class="w-full h-auto min-h-113rpx"
          width="750rpx"
          mode="widthFix"
          @click="
            previewImg(
              getHeadIcon(taskFacet[currentIndex].tableField102[currentTableIndex].topicImg[0].url)
            )
          "></image>
      </view>
      <view class="w-full bg-white p30rpx box-border">
        <view class="text-28rpx flex fw-bold justify-between">
          <view class="flex">
            <view class="text-#333333">AI阅卷结果：</view>
            <view class="text-#FF0000 ml20rpx">{{
              taskFacet[currentIndex].tableField102[currentTableIndex].isCorrect === '对'
                ? '正确'
                : '错误'
            }}</view>
          </view>
          <view class="flex">
            <view class="text-#333333 mb20rpx">分数：</view>
            <view class="text-#FF0000">{{
              taskFacet[currentIndex].tableField102[currentTableIndex].score
            }}</view>
          </view>
        </view>
        <view class="m-y-20rpx flex-center">
          <view class="toggle-group1">
            <view
              class="toggle-group1-toggle-btn1"
              :class="{
                active: taskFacet[currentIndex].tableField102[currentTableIndex].isCorrect === '对',
              }"
              @click="
                toggleResult(
                  taskFacet[currentIndex].tableField102[currentTableIndex],
                  '对',
                  currentTableIndex
                )
              "
              >对</view
            >
            <view
              class="toggle-group1-toggle-btn2"
              :class="{
                active: taskFacet[currentIndex].tableField102[currentTableIndex].isCorrect === '错',
              }"
              @click="
                toggleResult(
                  taskFacet[currentIndex].tableField102[currentTableIndex],
                  '错',
                  currentTableIndex
                )
              "
              >错</view
            >
          </view>
        </view>
        <view
          class="w-full m-y-20rpx h86rpx rounded-10rpx b-1rpx b-solid b-#D9D9D9 flex justify-between items-center p20rpx box-border">
          <view class="text-28rpx text-#333333">评分：</view>
          <view>
            <up-input
              v-model="taskFacet[currentIndex].tableField102[currentTableIndex].score"
              type="number"
              placeholder="请输入评分"
              border="none"
              input-align="right"
              readonly
              @change="
                handleChange(
                  taskFacet[currentIndex].tableField102[currentTableIndex],
                  currentTableIndex,
                  $event
                )
              "></up-input>
          </view>
        </view>
        <view class="w-full flex">
          <u-button text="上一题" shape="circle" :custom-style="prevBtnStyle" @click="prevQuestion">
          </u-button>
          <u-button text="下一题" shape="circle" :custom-style="nextBtnStyle" @click="nextQuestion">
          </u-button>
        </view>
      </view>
    </view>
  </u-popup>
</template>

<script setup lang="ts" name="teacher-grading-papers">
import { getHeadIcon, showToast, showToastBack } from '@/utils'
import {
  createSpecialClockInResultCard,
  getMarkingDetail,
  submitMarking,
} from '@/api/project/special-marking'
import type {
  CreateResultCardType,
  PaperFacetItem,
  TaskFacetItem,
} from '@/api/project/special-marking/type'
import useUserStore from '@/store/modules/user'
import mpHtml from '@/components/mp-html/mp-html.vue'

const userStore = useUserStore()

// 阅卷题目明细
const taskFacet = ref<TaskFacetItem[]>([] as TaskFacetItem[])
// 试卷题目明细
const paperFacet = ref<PaperFacetItem[]>([] as PaperFacetItem[])

// 改卷弹窗
const markingDialog = ref<boolean>(false)
const currentIndex = ref<number>(0)
const currentTableIndex = ref<number>(0)
const markingChange = (index1: number, index2: number) => {
  // 主表
  currentIndex.value = index1
  // 子表
  currentTableIndex.value = index2
  markingDialog.value = !markingDialog.value
}

// 上一题
const prevQuestion = () => {
  currentTableIndex.value--
  if (currentTableIndex.value < 0) {
    currentTableIndex.value = taskFacet.value![currentIndex.value].tableField102.length - 1
  }
}

// 下一题
const nextQuestion = () => {
  currentTableIndex.value++
  if (currentTableIndex.value >= taskFacet.value![currentIndex.value].tableField102.length) {
    currentTableIndex.value = 0
  }
}

// 切换结果
const toggleResult = (item: any, value: string, index: number) => {
  item.isCorrect = value

  const score = paperFacet.value![index].score || 0
  if (value === '对') {
    item.score = score
  } else {
    item.score = 0
  }
  item.judges = new Array(1)
  item.judges[0] = userStore.userInfo?.id
}

// 评分
const handleChange = (ite: any, index: number, value: number) => {
  const score = Number(value)
  const maxScore = paperFacet.value![index].score
  setTimeout(() => {
    if (score > maxScore) {
      showToast(`评分不能大于${maxScore}分`)
      ite.tableField102[index].score = maxScore
    } else if (score < 0) {
      showToast('评分不能小于0分')
      ite.tableField102[index].score = 0
    }
  }, 500)
}

// 添加专项试卷答题记录
const addTopicResult = async (val: TaskFacetItem['tableField102'], paperId: string) => {
  const data = {} as CreateResultCardType
  data.state = '提交'
  data.answerTime = ''
  data.type = '专项打卡'
  data.tableField112 = val.map(ite => {
    return {
      topicId: ite.topic_id,
      userAnswer: ite.userAnswer,
      index: ite.index,
      score: ite.score,
    }
  })
  data.totalScore = val.reduce((pre, cur) => pre + (cur.score || 0), 0)
  data.accuracy = Number((val.filter(ite => ite.isCorrect === '对').length / val.length).toFixed(2))
  data.fid = paperId
  await createSpecialClockInResultCard(data)
}

// 批改提交
const handleSubmit = async () => {
  for (const item of taskFacet.value!) {
    addTopicResult(item.tableField102, item.paperId)
    await submitMarking(item._id, item.tableField102)
  }
  showToastBack('批改成功')
}

onLoad(async (val: any) => {
  const { data } = await getMarkingDetail(val.id)
  taskFacet.value = data[0].taskFacet
  taskFacet.value.forEach(item => {
    item.tableField102.forEach(ite => {
      if (!ite?.score) {
        // 设置默认分数
        ite.score = 0
      }
    })
  })
  paperFacet.value = data[0].paperFacet
})

// 按钮样式
const prevBtnStyle = computed(() => ({
  flex: '1',
  margin: '20rpx',
  color: '#3388FF',
  backgroundColor: '#ffffff',
  border: '1px solid #3388FF',
}))

const nextBtnStyle = computed(() => ({
  flex: '1',
  margin: '20rpx',
  color: '#ffffff',
  backgroundColor: '#3388FF',
  border: '1px solid #3388FF',
}))

// 图片预览
const previewImg = (url: string) => {
  uni.previewImage({
    urls: [url],
  })
}
</script>

<style>
page {
  background-color: #f4f5f7;
}
</style>

<style lang="scss" scoped>
.toggle-group {
  width: 202rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
  font-size: 28rpx;
  text-align: center;
  line-height: 54rpx;
  .toggle-btn1 {
    width: 101rpx;
    height: 54rpx;
    border-radius: 27rpx 0rpx 0rpx 27rpx;
    border: 1rpx solid #3a97f3;
    border-right: none;
    color: #459af7;
    &.active {
      background-color: #459af7;
      color: #fff;
    }
  }

  .toggle-btn2 {
    width: 101rpx;
    height: 54rpx;
    background: #ffffff;
    border-radius: 0rpx 27rpx 27rpx 0rpx;
    border: 1rpx solid #f33a3a;
    color: #f33a3a;
    &.active {
      background-color: #f33a3a;
      color: #fff;
    }
  }
}
.toggle-group1 {
  display: flex;
  width: 260rpx;
  justify-content: space-between;
  text-align: center;
  line-height: 100rpx;
  &-toggle-btn1 {
    width: 100rpx;
    height: 100rpx;
    border-radius: 50%;
    border: 1px solid #459af7;
    color: #459af7;
    &.active {
      background-color: #459af7;
      color: #fff;
    }
  }
  &-toggle-btn2 {
    width: 100rpx;
    height: 100rpx;
    border-radius: 50%;
    border: 1px solid #f33a3a;
    color: #f33a3a;
    &.active {
      background-color: #f33a3a;
      color: #fff;
    }
  }
}
</style>
