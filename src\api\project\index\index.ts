import type * as IndexType from './type.d'
import type { FilterType } from '@/es/request'
import {
  createModelData,
  dataInterface,
  dataPageInterface,
  deleteModelData,
  getModelDataDetail,
  getModelList,
} from '@/api/visual'

// 获取首页信息展示栏目
export const getInfoDisplayDataList = () => {
  return getModelList<IndexType.IinfoDisplayData>(
    {
      menuId: '669a0a2ac523aa5a70e0a37e',
    },
    { unAuth: true }
  )
}

// 获取首页信息展示栏目详情
export const getInfoDisplayData = (id: string) => {
  return getModelDataDetail<IndexType.IinfoDisplayData>(
    {
      menuId: '669a0a2ac523aa5a70e0a37e',
      _id: id,
    },
    { unAuth: true }
  )
}

// 获取前台配置数据
export const getZsbExamTime = () => {
  return getModelList<IndexType.IExamTimeData>(
    {
      menuId: '669a09abc523aa5a70e0a37b',
    },
    { unAuth: true }
  )
}

// 获取首页公告列表
export const getNoticeDataList = () => {
  return getModelList<{
    content: string
  }>(
    {
      menuId: '669a094bc523aa5a70e0a37a',
    },
    { unAuth: true }
  )
}

// 获取专科专业列表
export const getZKmajorDataList = ({ pageSize = 10, currentPage = 1, filter = [] }) => {
  return getModelList<IndexType.IZKMajorData>(
    {
      menuId: '669a0ef7c523aa5a70e0a38b',
      pageSize,
      currentPage,
      filter,
    },
    { unAuth: true }
  )
}

// 查询可跨考专业
export const getCrossMajorDataList = (majorName: string) => {
  return dataInterface<IndexType.IZKCrossMajorData[]>(
    {
      id: '64c2168aae740000b0005ee6',
      data: {
        '@college_name': majorName,
      },
    },
    { unAuth: true }
  )
}

// 查询可以报名的专业及院校
export const getReportMajorDataList = (code: string) => {
  return dataInterface(
    {
      id: '64c21796ae740000b0005ee7',
      data: {
        '@type_code': code,
      },
    },
    { unAuth: true }
  )
}

// 查询本科院校库
export const getBKSchoolDataList = ({
  pageSize = 10,
  currentPage = 1,
  filter = [] as FilterType[],
}) => {
  filter = filter.filter(item => item.value[0] !== '')
  return getModelList<IndexType.IBKSchoolData>(
    {
      menuId: '669a0c7dc523aa5a70e0a384',
      pageSize,
      currentPage,
      filter,
      connect: 'and',
      sort: { sort: 'asc' },
    },
    { unAuth: true }
  )
}

// 查询本科院校地区
export const getBKSchoolAddressDataList = ({ pageSize = 10, currentPage = 1, filter = [] }) => {
  return getModelList<IndexType.IBKSchoolAddressData>(
    {
      menuId: '669a0e01c523aa5a70e0a386',
      pageSize,
      currentPage,
      filter,
    },
    { unAuth: true }
  )
}

// 查询本科院校类型
export const getBKSchoolTypeDataList = ({ pageSize = 10, currentPage = 1, filter = [] }) => {
  return getModelList<IndexType.IBKSchoolTypeData>(
    {
      menuId: '669a0de4c523aa5a70e0a385',
      pageSize,
      currentPage,
      filter,
    },
    { unAuth: true }
  )
}

// 查询本科院校库详情
export const getBKSchoolData = (id: string) => {
  return getModelDataDetail<IndexType.IBKSchoolData>(
    {
      menuId: '669a0c7dc523aa5a70e0a384',
      _id: id,
    },
    { unAuth: true }
  )
}

// 查询本科院校专业信息
export const getBKSchoolMajorDataList = (schoolName?: string) => {
  let filter: any = []
  if (schoolName) {
    filter = [
      {
        enCode: 'school_name',
        method: 'eq',
        type: 'custom',
        value: [schoolName],
      },
    ]
  }
  return getModelList<IndexType.IBKSchoolMajorData>(
    {
      menuId: '669a0fb2c523aa5a70e0a38f',
      pageSize: -1,
      currentPage: 1,
      filter,
      sort: { school_name: 'desc' },
    },
    { unAuth: true }
  )
}

// 查询定制化专业库信息列表
export const getBKMajorInterDataList = ({
  pageSize = 10,
  currentPage = 1,
  filters = {} as { major_name: string },
}) => {
  return dataPageInterface<IndexType.IBKInterMajorData>(
    {
      id: '649820e25714000056001702',
      data: {
        '@pageSize': pageSize,
        '@currentPage': currentPage,
        '@keyword': filters?.major_name || 'All',
        '@sort': 'undergraduate_count',
      },
    },
    { unAuth: true }
  )
}

// 查询专业库详情
export const getBkMajorData = (name: string) => {
  return getModelList<IndexType.IBKMajorData>(
    {
      menuId: '669a1104c523aa5a70e0a391',
      filter: [
        {
          enCode: 'major_name',
          method: 'eq',
          type: 'custom',
          value: [name],
        },
      ],
    },
    { unAuth: true }
  )
}

// 专业库专业详情查询学校
export const getBKMajorQuerySchool = (code: string) => {
  return dataInterface(
    {
      id: '649ef4a98b5c00006d005ca2',
      data: {
        '@Undergraduate_code': code,
      },
    },
    { unAuth: true }
  )
}

// 获取升本政策
export const getSbPolicyData = (id: string) => {
  return getModelDataDetail<IndexType.ISbPolicyData>(
    {
      menuId: '669a0730c523aa5a70e0a373',
      _id: id,
    },
    { unAuth: true }
  )
}

// 获取升本日程
export const getSbScheduleData = () => {
  return getModelList<IndexType.ISbScheduleData>(
    {
      menuId: '669a0889c523aa5a70e0a376',
      currentPage: 1,
      pageSize: -1,
    },
    { unAuth: true }
  )
}

// 获取专升本考试时间
export const getSbExamTime = () => {
  return getModelList<IndexType.ISbExamTimeData>(
    {
      menuId: '669a08dcc523aa5a70e0a378',
    },
    { unAuth: true }
  )
}

// 获取相关数据的年份
export const getSbYearData = () => {
  return getModelList<IndexType.ISbYearData>(
    {
      menuId: '67e209f6f0417818d93c7fc6',
    },
    { unAuth: true }
  )
}

// 获取首页轮播图
export const getIndexSwiperDataList = () => {
  return getModelList<IndexType.IndexSwiper>({
    association: false,
    authGroupId: '680dfa0a6353160fb2777b8b',
    connect: 'and',
    currentPage: 1,
    menuId: '67abfe6c66dcb6553bdcdb87',
    pageSize: 20,
    filter: undefined,
    filterList: [],
    userInfoConvert: true,
  })
}

// 获取用户收藏院校
export const getUserCollectSchool = (schoolId?: string) => {
  const filter: FilterType[] = [
    {
      enCode: 'creatorUserId',
      method: 'eq',
      type: 'systemField',
      value: ['currentUser'],
    },
  ]

  if (schoolId) {
    filter.push({
      enCode: 'school',
      method: 'eq',
      type: 'custom',
      value: [schoolId],
    })
  }

  return getModelList<IndexType.IUserCollectSchool>({
    connect: 'and',
    currentPage: 1,
    menuId: '68872e041807a96b974fa35f',
    pageSize: -1,
    filter,
  })
}

// 添加用户收藏院校
export const addUserCollectSchool = (schoolId: string) => {
  return createModelData({
    menuId: '68872e041807a96b974fa35f',
    data: JSON.stringify({
      school: schoolId,
    }),
  })
}

// 删除用户收藏院校
export const delUserCollectSchool = (id: string) => {
  return deleteModelData({
    menuId: '68872e041807a96b974fa35f',
    _id: id,
  })
}
