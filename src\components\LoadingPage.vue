<template>
  <view class="loading-page" :class="[{ 'overflow-auto': empty, show }]">
    <u-loading-page v-if="loading" loading :loading-text="loadingText" />
    <view v-else-if="empty" class="h100vh flex flex-col justify-center items-center">
      <u-empty :text="emptyText" :mode="emptyModel" :icon="emptyIcon" />
    </view>
    <slot v-else :show="show" />
  </view>
</template>

<script setup lang="ts">
interface Props {
  loading?: boolean
  empty?: boolean
  emptyText?: string
  loadingText?: string
  emptyModel?: string
  emptyIcon?: string
}
const props = withDefaults(defineProps<Props>(), {
  emptyText: '暂无数据',
  loadingText: '数据加载中...',
  emptyModel: 'list',
  emptyIcon: undefined,
})
const show = computed(() => !props.loading && !props.empty)
</script>

<style lang="scss" scoped></style>
