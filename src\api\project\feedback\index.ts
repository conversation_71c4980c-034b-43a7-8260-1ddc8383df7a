import type * as commonType from './type'
import { createModelData, getModelDataDetail, getModelList } from '@/api/visual'

// 获取反馈列表
export const getFeedbackList = ({ pageSize = 20, currentPage = 1, filter = [] }) => {
  return getModelList<commonType.FeedBackItemType>({
    menuId: '66438455a82cc103bfc53e95',
    filter,
    pageSize,
    currentPage,
    userInfoConvert: true,
  })
}

// 获取反馈详情
export const getFeedbackDetail = (id: string) => {
  return getModelDataDetail<commonType.FeedBackItemType>({
    menuId: '66438455a82cc103bfc53e95',
    _id: id,
    userInfoConvert: true,
  })
}

// 提交反馈
export const submitFeedback = (data: commonType.FeedBackSubmitItemType) => {
  return createModelData({
    menuId: '66438455a82cc103bfc53e95',
    data: JSON.stringify(data),
  })
}
