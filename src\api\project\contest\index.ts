import type * as VisualType from './type'
import { createModelData, dataInterface, getModelList } from '@/api/visual'

// 获取竞赛列表,getModelList是一个封装好的请求函数,可以直接使用
export function getContestList() {
  return getModelList<VisualType.ContestListType>(
    {
      currentPage: 1,
      filterList: [],
      menuId: '664c0ac1a82cc103bfc53f1a',
      pageSize: -1, // 获取全部
    },
    {
      unAuth: true,
    }
  )
}

// 根据关键词搜索后的竞赛列表
export function getContestListByKeyword(keyword: string) {
  return getModelList<VisualType.ContestListType>(
    {
      currentPage: 1,
      filter: [
        // {
        //     enCode: 'tags',
        //     method: 'like',
        //     type: 'custom',
        //     value: [keyword] // 使用传入的关键词
        // },
        {
          enCode: 'title',
          type: 'custom',
          method: 'like',
          value: [keyword], // 使用传入的关键词
        },
        {
          enCode: 'tableField118.title',
          type: 'custom',
          method: 'like',
          value: [keyword], // 使用传入的关键词
        },
      ],
      menuId: '664c0ac1a82cc103bfc53f1a',
      pageSize: -1, // 获取全部
    },
    {
      unAuth: true,
    }
  )
}

//  获取当前enCode下参赛团队列表
export function getParticipatingTeamsList(enCode: any) {
  return getModelList<VisualType.List>({
    currentPage: 1,
    filter: [
      {
        enCode: 'enCode',
        type: 'custom',
        method: 'eq',
        value: [enCode], // 使用传入的关键词
      },
    ],
    menuId: '664edfe47c867c6a7d4987ab',
    pageSize: 20, // 获取20条数据
  })
}

// 根据团队名称获取参赛团队列表

export function getParticipatingTeamsListByKeyword(keyword: string, enCode: any) {
  return getModelList<VisualType.List>({
    currentPage: 1,
    filter: [
      {
        enCode: 'name',
        type: 'custom',
        method: 'like',
        value: [keyword], // 使用传入的关键词
      },
      {
        enCode: 'enCode',
        type: 'custom',
        method: 'eq',
        value: [enCode], // 使用传入的关键词
      },
    ],
    menuId: '664edfe47c867c6a7d4987ab',
    connect: 'and',
    pageSize: -1, // 获取20条数据
  })
}

// 根据赛题编码查询竞赛的情况
export function getContestInfoByenCode(enCode: string) {
  return getModelList<VisualType.ContestListType>({
    currentPage: 1,
    filter: [
      {
        enCode: 'tableField118.enCode',
        method: 'like',
        type: 'custom',
        value: [enCode], // 使用传入的关键词
      },
    ],
    menuId: '664c0ac1a82cc103bfc53f1a',
    pageSize: -1, // 获取全部
  })
}

// 查询团队列表

// 查询当前用户是否在团队中
export const getTeamDataList = (enCode: string) => {
  return dataInterface<any[]>({
    id: '643655daab7f0000ae006383',
    data: {
      '@enCode': enCode,
    },
  })
}

// 将用户添加到团队中
export const addTeamData = (data: any) => {
  return createModelData({
    menuId: '664edfe47c867c6a7d4987ab',
    data: JSON.stringify(data),
  })
}

// 获取详细资讯的数据
export const getCompetitionNews = () => {
  return getModelList<VisualType.CompetitionNews>(
    {
      currentPage: 1,
      filterList: [],
      menuId: '675905e20a34815816f111c5',
      pageSize: -1, // 获取全部
    },
    {
      unAuth: true,
    }
  )
}

// 获取课题报告的数据
export const getTopicInformation = () => {
  return getModelList<VisualType.CompetitionNews>(
    {
      currentPage: 1,
      filterList: [],
      menuId: '67593d770a34815816f111ca',
      pageSize: -1, // 获取全部
    },
    {
      unAuth: true,
    }
  )
}

// 获取硕博招考的列表信息
export const getRecruitmentExam = () => {
  return getModelList<VisualType.CompetitionNews>(
    {
      currentPage: 1,
      filterList: [],
      menuId: '675954840a34815816f111cd',
      pageSize: -1, // 获取全部
    },
    {
      unAuth: true,
    }
  )
}
