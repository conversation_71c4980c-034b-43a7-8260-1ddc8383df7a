<template>
  <view class="mainbox">
    <view class="box">
      <u-cell-group>
        <u-cell
          :icon="getSystemImg('6747e9ed0a34815816f11159/66988df60bb07d7cd6ed3884')"
          title="本书已学"
          is-link
          :value="learnedCount"
          @click="jumpReciteWordPage({ title: '本书已学', bookId })"></u-cell>
        <u-cell
          :icon="getSystemImg('6747e9ed0a34815816f11159/66988df60bb07d7cd6ed3883')"
          title="本书待学"
          is-link
          :value="tolearnedCount"
          @click="jumpReciteWordPage({ title: '本书待学', bookId })"></u-cell>
        <u-cell
          :icon="getSystemImg('6747e9ed0a34815816f11159/66988df60bb07d7cd6ed3884')"
          title="学习记录"
          is-link
          @click="goRecord"></u-cell>
      </u-cell-group>
    </view>
    <view class="box">
      <u-cell-group>
        <u-cell
          :icon="getSystemImg('6747e9ed0a34815816f11159/66988df60bb07d7cd6ed3885')"
          title="生词"
          is-link
          :value="newWordCount"
          @click="jumpReciteWordPage({ title: '生词', bookId })"></u-cell>
        <u-cell
          :icon="getSystemImg('6747e9ed0a34815816f11159/66988df60bb07d7cd6ed3886')"
          title="熟词"
          is-link
          :value="familiarWordCount"
          @click="jumpReciteWordPage({ title: '熟词', bookId })"></u-cell
      ></u-cell-group>
    </view>
  </view>
</template>

<script setup lang="ts">
import {
  getLearnedReciteWordCount,
  getNewWordCountAndFamiliarWordCount,
  getReciteWordCount,
} from '@/api/project/recite-words'
import { getSystemImg } from '@/utils'
const bookId = ref('')
const totalCount = ref('')
const learnedCount = ref('')
const tolearnedCount = ref()
const newWordCount = ref('')
const familiarWordCount = ref('')

const jumpReciteWordPage = ({ title, bookId }: { title: string; bookId: string }) => {
  uni.navigateTo({
    url: `/subPages/recite-words/word-list?title=${title}&bookId=${bookId}`,
  })
}
onLoad(async (e: any) => {
  bookId.value = e.id
  const result = await getReciteWordCount(bookId.value)
  totalCount.value = String(result.data?.[0]?.totalCount || 0)

  const learnedRes = await getLearnedReciteWordCount(bookId.value)
  learnedCount.value = String(learnedRes.data?.[0]?.totalCount || 0)
  tolearnedCount.value = Number(totalCount.value) - Number(learnedCount.value)

  const WordCountRes = await getNewWordCountAndFamiliarWordCount(bookId.value)

  if (WordCountRes.data.length > 0) {
    WordCountRes.data.forEach(e => {
      if (e._id === '生词') {
        newWordCount.value = String(e.totalCount)
      } else {
        familiarWordCount.value = String(e.totalCount)
      }
    })
  }
})
function goRecord() {
  uni.navigateTo({
    url: `/subPages/recite-words/record`,
  })
}
</script>

<style lang="scss" scoped>
.mainbox {
  height: 100vh;
  background: #f4f5f7;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.box {
  width: 710rpx;
  background: #ffffff;
  box-shadow: 0rpx 4rpx 16rpx 1rpx rgba(143, 143, 143, 0.13);
  border-radius: 10rpx 10rpx 10rpx 10rpx;
  margin: 20rpx 0rpx;
}
</style>
