import { defineStore } from 'pinia'
import {
  addMemberRight,
  addOrSubtractPoints,
  getMemberList,
  getMemberRight,
  getPointsAction,
  getPointsFilterInfo,
} from '@/api/project/member'
import useUserStore from '@/store/modules/user'
import type { IPointsRecordData } from '@/api/project/member/type'

export default defineStore(
  'member',
  () => {
    const isVip = ref<boolean>(false)
    const points_num = ref<number>(0)

    const getMemberInfo = async () => {
      if (!useUserStore().token) return {}

      // 是否vip
      const memberData = await getMemberList()
      isVip.value = memberData.data.list.length > 0

      // 查询积分数
      const pointsData = await getPointsFilterInfo()
      points_num.value = pointsData.data.list.reduce((sum, item) => sum + (item.points_num || 0), 0)
    }
    // 积分操作
    const pointsAction = async (
      action: string,
      num?: number,
      equity_id?: string,
      user_id?: string
    ) => {
      // 查询积分操作配置
      const {
        data: { list },
      } = await getPointsAction(action)
      const actionData = list[0]

      const actionName = num ? `${actionData.action_name}x${num}` : actionData.action_name
      const pointsNum = num ? actionData.points_num * num : actionData.points_num

      if (equity_id) {
        // 查询权益
        const equityData = await getMemberRight(equity_id)
        if (equityData.data.list.length > 0) {
          return {
            is: true,
          }
        }
        // 新增权益
        if (points_num.value > pointsNum) {
          // 积分足够的情况才添加权益
          await addMemberRight({
            equity_id,
            equity_type: actionData.action_type as '刷题' | '单词' | '课程',
            user: [user_id || useUserStore().userInfo!.id],
          })
        }
      }

      // 检查积分是否不足
      if (points_num.value < pointsNum) {
        return {
          flag: true,
        }
      }
      const data: IPointsRecordData = {
        action_id: actionData._id,
        action_name: actionName,
        points_num: pointsNum,
        points_day: actionData.points_day,
        points_type: actionData.points_type,
        user: [user_id || useUserStore().userInfo!.id],
      }
      await addOrSubtractPoints(data)
      await getMemberInfo()
      return {
        flag: false,
        points: pointsNum,
        content: actionName,
      }
    }
    const memberLogout = () => {
      isVip.value = false
      points_num.value = 0
      uni.removeStorageSync('member')
    }
    return {
      isVip,
      getMemberInfo,
      points_num,
      pointsAction,
      memberLogout,
    }
  },
  {
    persist: {
      storage: {
        getItem: uni.getStorageSync,
        setItem: uni.setStorageSync,
      },
    },
  }
)
