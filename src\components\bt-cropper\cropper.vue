<template>
  <view class="w100vw h90vh m-0 p-0">
    <bt-cropper :imageSrc="src" ref="cropper"></bt-cropper>
    <view class="fixed-bottom safe-area-inset-bottom" :style="{ zIndex: 99 }">
      <block>
        <view class="rechoose" @click="chooseImage">重选</view>
        <button class="button" size="mini" @click="crop">确定</button>
      </block>
    </view>
  </view>
</template>

<script>
import btCropper from './bt-cropper.vue'
export default {
  name: 'cropper',
  props: {
    src: {
      type: String,
      default: '',
    },
  },
  components: {
    btCropper,
  },
  data() {
    return {
      dist: '',
    }
  },
  methods: {
    chooseImage() {
      this.$emit('chooseImage')
    },
    crop() {
      this.$refs.cropper.crop().then(res => {
        this.$emit('crop', res)
      })
    },
  },
}
</script>

<style lang="scss" scoped>
.fixed-bottom {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 99;
  display: flex;
  flex-direction: row;
  background-color: $uni-bg-color-grey;

  .action-bar {
    position: absolute;
    top: -90rpx;
    left: 10rpx;
    display: flex;
    .rotate-icon {
      background-image: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADAAAAAwCAYAAABXAvmHAAAAAXNSR0IArs4c6QAABCFJREFUaEPtml3IpVMUx3//ko/ChTIyiGFSMyhllI8bc4F85yuNC2FCqLmQC1+FZORiEkUMNW7UjKjJULgxV+NzSkxDhEkZgwsyigv119J63p7zvOc8z37OmXdOb51dz82711r7/99r7bXXXucVi3xokeNnRqCvB20fDmwAlgK/5bcD+FTSr33tHXQP2H4MeHQE0A+B5yRtLiUyDQJrgVc6AAaBpyV93kXkoBMIQLbfBS5NcK8BRwDXNcD+AdwnaVMbiWkRCPBBohpxHuK7M7865sclRdgNHVMhkF6IMIpwirFEUhzo8M7lwIvASTXEqyVtH8ZgagQSbOzsDknv18HZXpHn5IL8+94IOUm7miSmSqAttjPdbgGuTrnNktYsGgLpoYuAD2qg1zRTbG8P2D4SOC6/Q7vSHPALsE/S7wWy80RsPw/ckxMfSTq/LtRJwPbxwF3ASiCUTxwHCPAnEBfVF8AWSTtL7Ng+LfWOTfmlkn6udFsJ5K15R6a4kvX6yGyUFBvTOWzHXXFzCt4g6c1OArYj9iIGh43YgR+BvztXh1PSa4cMkd0jaVmXDduPAE+k3HpJD7cSGFKvfAc8FQUX8IOk/V2L1udtB/hTgdOBW4Aba/M7Ja1qs2f7euCNlHlZUlx4/495IWQ7Jl+qGbxX0gt9AHfJ2o6zFBVoNVrDKe+F3Sm8VdK1bQQ+A85JgXckXdkFaJx527cC9TpnVdvBtl3h2iapuhsGPdBw1b9xnUvaNw7AEh3bnwDnpuwGSfeP0rN9NvAMELXRXFkxEEK2nwQeSiOtRVQJwC4Z29cAW1Nuu6TVXTrN+SaBt4ErUug2Sa/2NdhH3vZy4NvU2S/p6D768w5xI3WOrAD7LtISFpGdIhVXKfaYvjd20wP13L9M0p4DBbaFRKToSLExVkr6qs+aIwlI6iwz+izUQqC+ab29PiMwqRcmPXczD8w8MFj1zg7xXEqbpdHCw7FgWSjafZL+KcQxtpjteCeflwYulFR/J3TabSslVkj6utPChAK2f6q9uZdLitKieLQRuExSvX9ZbLRUMFs09efpUZL+KtUfVo1GW/umNHC3pOhRLtiwfSbwZS6wV9IJfRdreuBBYH0a2STp9r4G+8jbXgc8mzoDT8VSO00ClwDv1ZR7XyylC4ec7ejaLUmdsV6Aw7oSbwFXpdFdks7qA6pU1na0aR6owgeIR/1cx63UzjAC0YXYVjMQHlkn6ZtSo21ytuPZGKFagQ/xsXZ/3iGuFrYdjafXG0DiQMeBi47c9/GV3BO247UV38n5o0UAP6xmu7jFOGxjRr66On5NPBDOCBsDTapxjHY1dyOcolNXnYlx1himE53p2PmNkxosevfavhg4Izt2k7TXPwZ2S6p6QZPin/2rwcQ7OKmBohCadJGF1P8PG6aaQBKVX/8AAAAASUVORK5CYII=');
      background-size: 60% 60%;
      background-repeat: no-repeat;
      background-position: center;
      width: 80rpx;
      height: 80rpx;
      &.is-reverse {
        transform: rotateY(180deg);
      }
    }
  }

  .rechoose {
    color: $uni-color-primary;
    padding: 0 $uni-spacing-row-lg;
    line-height: 100rpx;
  }

  .choose-btn {
    color: $uni-color-primary;
    text-align: center;
    line-height: 100rpx;
    flex: 1;
  }

  .button {
    margin: auto $uni-spacing-row-lg auto auto;
    background-color: $uni-color-primary;
    color: #fff;
  }
}

.safe-area-inset-bottom {
  padding-bottom: 0;
  padding-bottom: constant(safe-area-inset-bottom); // 兼容 IOS<11.2
  padding-bottom: env(safe-area-inset-bottom); // 兼容 IOS>=11.2
}
</style>
