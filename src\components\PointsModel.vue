<template>
  <view>
    <!-- 通用积分变动弹窗 -->
    <up-modal :show="show" @close="closeModal">
      <view class="slot-content flex flex-col items-center">
        <image :src="getSystemImg(getCover)" class="w180rpx h210rpx"></image>
        <view v-if="!flag" class="text-center">
          <view class="text-36rpx fw-bold text-#333333">{{ content || '积分操作' }}</view>
          <view class="text-40rpx fw-bold m-y-20rpx" :class="type"
            >{{ type === 'add' ? '+' : '-' }}{{ points }}</view
          >
          <view class="text-#777C8B text-26rpx">剩余：{{ remainder }}</view>
        </view>
        <view v-else class="text-center">
          <view class="text-36rpx fw-bold text-#333333">您当前积分不足</view>
        </view>
      </view>
      <template #confirmButton>
        <view class="flex justify-between gap-30rpx">
          <u-button type="primary" plain text="取消" @click="closeModal" />
          <u-button type="primary" :text="flag ? '去购买' : '确定'" @click="toConfirm" />
        </view>
      </template>
    </up-modal>
  </view>
</template>

<script setup lang="ts" name="PointsModel">
import { getSystemImg, showToastBack } from '@/utils'
import useMemberStore from '@/store/modules/member'
// flag 积分是否不足
const props = defineProps<{
  show: boolean
  type?: 'add' | 'reduce'
  content?: string
  flag: boolean
  points?: number
}>()

const emits = defineEmits(['close'])

const memberStore = useMemberStore()

const getCover = computed(() => {
  return props.flag
    ? '/6747e9ed0a34815816f11159/67590511f8730075fead9d6b'
    : '/6747e9ed0a34815816f11159/6759050bf8730075fead9d69'
})

// 关闭弹窗
const closeModal = () => {
  emits('close')
  if (props.flag) {
    uni.navigateBack()
  }
}

// 确认按钮
const toConfirm = () => {
  if (props.flag) {
    uni.navigateTo({
      url: '/subPages/user/member',
    })
    return
  }
  closeModal()
}

// 剩余积分
const remainder = computed(() => {
  return memberStore.points_num
})
</script>

<style lang="scss" scoped>
.add {
  color: #17e232;
}
.reduce {
  color: #ff0c0c;
}
</style>
