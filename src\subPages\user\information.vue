<template>
  <view class="w690rpx rounded-10rpx bg-white ma mt20rpx p-x-40rpx p-y-10rpx box-border">
    <view class="w-full flex justify-between items-center p-y-20rpx bor-b" @click="upLoad">
      <u-avatar size="50" :src="getHeadIcon(info.userInfo.headIcon)"></u-avatar>
      <view class="text-#000000 fw-bold text-24rpx">点击上传头像></view>
    </view>
    <u-form ref="form1" :model="info" label-width="80" :rules="rules">
      <u-form-item label="姓名" :border-bottom="true" required prop="userInfo.realName">
        <u-input
          v-model="info.userInfo.realName"
          placeholder="请输入你的姓名"
          border="none"
          input-align="right"></u-input>
      </u-form-item>
      <u-form-item label="手机号" :border-bottom="true" required prop="userInfo.phone">
        <u-input
          v-model="info.userInfo.phone"
          placeholder=""
          disabled
          border="none"
          input-align="right"></u-input>
      </u-form-item>
    </u-form>
  </view>
  <view
    class="w690rpx rounded-10rpx bg-white ma mt20rpx p-x-40rpx p-y-20rpx box-border flex justify-between items-center">
    <view class="text-28rpx text-#333333">当前企业：</view>
    <view class="text-28rpx text-#333333" @click="showPopup = true">{{ nowCorp.corpName }}></view>
  </view>
  <u-popup :show="showPopup" mode="center" bg-color="transparent" @close="showPopup = false">
    <view
      v-for="item in corpList"
      :key="item.corpId"
      class="w690rpx mb20rpx h160rpx rounded-20rpx bg-white flex justify-start items-center"
      @click="onChangeCorp(item)">
      <image
        :src="getSystemImg('/6747e9ed0a34815816f11159/67d24a561d253f01dc2b89eb')"
        class="w-100rpx h-100rpx ml40rpx"></image>
      <view class="text-36rpx text-#333333 fw-bold ml40rpx">{{ item.corpName }}</view>
    </view>
  </u-popup>
  <view class="absolute w-full bottom-30rpx p40rpx box-border">
    <u-button text="提交" type="primary" shape="circle" @click="submit"></u-button>
  </view>
</template>

<script setup lang="ts" name="user-information">
import type { CorpType, UserInfo } from '@/api/user/type'
import { getHeadIcon, getSystemImg, miniProgramUploadFile, showToastBack } from '@/utils'
import { getCorpList, getCurrentUserCorpList, updateUser } from '@/api/user'
import useUserStore from '@/store/modules/user'
import { switchCorp } from '@/api/permission'

// 用户信息
const userStore = useUserStore()
const info = ref({
  userInfo: {} as UserInfo,
})

// 用户可切换的租户
const corpList = ref<CorpType[]>([])
const nowCorp = ref<CorpType>({} as CorpType)
const showPopup = ref(false)

// 切换租户
const onChangeCorp = async (corp: CorpType) => {
  await switchCorp(corp.corpId)
  nowCorp.value = corp
  userStore.refreshUserInfo()
  userStore.setRequestOpenToken()
  showPopup.value = false
  uni.reLaunch({
    url: '/pages/index/index',
  })
}

// 表单
const form1 = ref()
const rules = reactive({
  'userInfo.realName': [
    {
      required: true,
      message: '姓名不能为空',
      trigger: ['change', 'blur'],
    },
  ],
})

// 头像上传
const upLoad = () => {
  uni.chooseImage({
    count: 1,
    success: success => {
      miniProgramUploadFile(success.tempFilePaths[0]).then(res => {
        info.value.userInfo.headIcon = res.data.url
      })
    },
  })
}

// 提交or修改
const submit = () => {
  form1.value.validate().then((valid: any) => {
    if (valid) {
      updateUser(info.value.userInfo)
      showToastBack('修改成功')
    }
  })
}
onLoad(async () => {
  const { VITE_TENANT_ID, VITE_APP_ICON, VITE_APP_NAME } = import.meta.env
  info.value.userInfo = userStore.userInfo!
  const institutionCorpRes = await getCorpList()
  const getCorpListRes = await getCurrentUserCorpList()

  const { corpList: apiCorpList, joinCorpList } = getCorpListRes.data
  // 用户加入的全部租户
  const userAllCorp = [...apiCorpList, ...joinCorpList]

  corpList.value = institutionCorpRes.data.filter(item =>
    userAllCorp.map(o => o.corpId).includes(item.corpId)
  )

  // 如果包含主体租户 添加主体租户
  if (userAllCorp.some(item => item.corpId === VITE_TENANT_ID)) {
    corpList.value.unshift({
      corpId: VITE_TENANT_ID,
      corpIcon: VITE_APP_ICON,
      corpName: VITE_APP_NAME,
    })
  }
  nowCorp.value = corpList.value.find(item => item.corpId === userStore.userInfo?.corpId)!
})
</script>

<style lang="scss">
page {
  background-color: #f4f5f7;
}
</style>

<style lang="scss" scoped>
.bor-b {
  border-bottom: 2rpx solid #e2e2e2;
}
</style>
