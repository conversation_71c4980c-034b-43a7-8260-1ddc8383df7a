<template>
  <view class="record container padding-30 u-main-color">
    <view class="clock-in-line record-info">
      <view class="flex record-title justify-between items-center">
        <view class="font-bold">{{ month }}月汇总</view>
      </view>
      <view class="flex info-panel">
        <view class="flex-1 flex-center flex-col">
          <text class="font-bold">{{ totalDays }}</text>
          <view>总打卡</view>
        </view>
        <view class="flex-1 flex-center flex-col">
          <text class="font-bold">{{ continuousDays || 0 }}</text>
          <view>连续打卡</view>
        </view>
        <view class="flex-1 flex-center flex-col">
          <text class="font-bold text-wrong">{{ notDays }}</text>
          <view>未打卡</view>
        </view>
      </view>
    </view>
    <view v-if="extraData.length !== 0" class="clock-in-line">
      <calendar
        ref="calendars"
        read-mode
        :extra-data="extraData"
        @active="updateCurrentTimestamp"
        @change="computedTimeRangeTask">
        <template #foot>
          <view class="state-line flex items-center">
            <view class="state-dot state-correct"></view>
            <text class="text-note margin-r-24">已完成</text>
            <view class="state-dot state-warn"></view>
            <text class="text-note margin-r-24">待完成</text>
            <view class="state-dot state-wrong"></view>
            <text class="text-note margin-r-24">未完成</text>
          </view>
        </template>
      </calendar>
    </view>
    <view class="mt10rpx">
      <view class="fw-bold">打卡完成情况</view>
      <up-swipe-action>
        <view v-for="item in (list as any )" :key="item._id" class="m-y-20rpx">
          <up-swipe-action-item
            :disabled="
              !(
                (item.date > new Date().getTime() ||
                  new Date(item.date).getDate() === new Date().getDate()) &&
                item.is_complete === 0
              )
            "
            :options="[
              {
                text: '删除',
                style: {
                  backgroundColor: '#ff3131',
                },
              },
            ]"
            @click="dele(item._id)">
            <ComPleteItem
              :title="item.content"
              :date="item.date"
              :status="item.is_complete"
              :time="item.time"
              :task-id="item._id"
              :itype="item.type"
              :section-id="item.section_id"
              :complete-id="item.complete_id"
              :topic-id="item.topic_id"
              :catalog-id="item.catalog_id"
              :word-id="item.word_id"
              :report-id="item.report_id" />
          </up-swipe-action-item>
        </view>
      </up-swipe-action>

      <u-empty v-if="clockRecords.length === 0" text="暂无打卡"></u-empty>
    </view>
  </view>
</template>

<script setup lang="ts" name="clock-in-record">
import ComPleteItem from './CompleteItem/index.vue'
import {
  deleteClockData,
  getCameraClockInRecordsDataList,
  getOnlineRecordsDataList,
} from '@/api/project/clock-in'
import type { IClockInRecords } from '@/api/project/clock-in/type'
import calendar from '@/components/calendar/calendar.vue'
import { getStartEndTime, showToast } from '@/utils'

const extraData = ref<
  {
    timestamp: number
    status: string
  }[]
>([])

const month = ref(new Date().getMonth() + 1)

const today = ref()

const calendars = ref()

const timestamp = ref(new Date().setHours(0, 0, 0, 0))

const updateCurrentTimestamp = (val: any) => {
  timestamp.value = val.timestamp
}

// 打卡记录列表
interface IDistributeBase extends IClockInRecords {
  sectionId: string
  topicId: string
  catalogId: string
}
const clockRecords = ref<IDistributeBase[]>([])

const list = computed(() => {
  if (timestamp.value) {
    return clockRecords.value.filter(item => item.date === timestamp.value)
  } else {
    return clockRecords.value
  }
})

const totalDays = ref()

const continuousDays = ref()

const notDays = ref()

const handleContinuousDay = () => {
  const list = clockRecords.value.filter(
    item => new Date(item.date).getDate() < new Date().getDate()
  )
  list.sort((x, y) => x.date - y.date)
  // 连续打卡天数
  let day = 0
  list.forEach((item, index) => {
    if (index !== list.length - 1 && item.date !== list[index + 1].date) day++
    if (index !== 0 && new Date(item.date).getDate() - new Date(list[index - 1].date).getDate() > 1)
      day = 0
    if (index === list.length - 1) day++
    if (item.is_complete === 0) day = 0
  })
  continuousDays.value = day
}

const getData = async () => {
  clockRecords.value = (
    await getCameraClockInRecordsDataList(today.value.startTime, today.value.endTime)
  ).data.list
  // 获取在线刷题和在线学习记录
  const list = (await getOnlineRecordsDataList(today.value.startTime, today.value.endTime)).data
  clockRecords.value.push(...(list as []))
  totalDays.value = clockRecords.value.length
  notDays.value = clockRecords.value.filter(item => item.is_complete === 0).length
  clockRecords.value.forEach(item => {
    extraData.value.push({
      timestamp: item.date,
      status:
        item.is_complete === 1
          ? 'correct'
          : item.date > new Date().getTime() ||
            new Date(item.date).getDate() === new Date().getDate()
          ? 'warn'
          : 'wrong',
    })
  })
  const wrongList = extraData.value.filter(item => item.status === 'wrong')
  extraData.value.forEach(item => {
    const arr = wrongList.filter(ite => ite.timestamp === item.timestamp)
    if (arr.length > 0) {
      item.status = 'wrong'
    }
  })
  setTimeout(() => {
    calendars.value.updateCurrentActiveDayStatus()
    handleContinuousDay()
  }, 500)
}

const dele = (id: string) => {
  deleteClockData(id).then(res => {
    showToast('删除成功')
    getData()
  })
}
const computedTimeRangeTask = (date: any) => {
  today.value = getStartEndTime(date.month, date.year)
  getData()
}

onLoad((val: any) => {
  if (val.times) {
    timestamp.value = val.times
  }
  today.value = getStartEndTime(month.value)
})
onShow(() => {
  getData()
})
</script>

<style>
page {
  background-color: #f4f5f7;
}
</style>

<style lang="scss" scoped>
@import '../../common/css/project.scss';
@import '../../common/css/index.scss';
.record {
  .record-title {
    padding: 12rpx 16rpx 24rpx;
  }
  .text-wrong {
    color: #ff2525;
  }
  ::v-deep .state-correct {
    --status-color: #0ccc8c;
  }
  ::v-deep .state-wrong {
    --status-color: #ff3131;
  }
  ::v-deep .state-warn {
    --status-color: #ffa600;
  }

  ::v-deep .calendar {
    .state-correct + .state-correct:not(.week-sun) {
      position: relative;
      transform-style: preserve-3d;
      &::before {
        position: absolute;
        content: '';
        left: -100%;
        top: 0;
        width: 150%;
        height: 100%;
        background-color: #0ccc8c1a;
        transform: translateZ(-1px);
      }
    }
  }
  .info-panel > .flex-1 {
    > text {
      font-size: 46rpx;
    }
    > view {
      padding: 8rpx;
    }
  }
  .state-line {
    padding: 30rpx 24rpx 10rpx;
    border-top: 1rpx solid #f3f4f6;
    > .text-note {
      color: $u-main-color;
    }
  }
}
</style>
