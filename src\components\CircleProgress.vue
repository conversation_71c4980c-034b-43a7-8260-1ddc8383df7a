<template>
  <view
    class="circle-progress"
    :style="{
      width: `${widthPx}px`,
      height: `${widthPx}px`,
      backgroundColor: bgColor,
    }">
    <!-- 有的不支持canvas-id属性，必须用id属性 -->
    <canvas
      v-if="canvasId"
      :id="canvasId"
      type="2d"
      class="canvas-bg"
      :style="{
        width: `${widthPx}px`,
        height: `${widthPx}px`,
      }"></canvas>
    <canvas
      v-if="elId"
      :id="elId"
      type="2d"
      class="canvas"
      :style="{
        width: `${widthPx}px`,
        height: `${widthPx}px`,
      }"></canvas>
    <slot></slot>
  </view>
</template>

<script setup lang="ts">
import type { ComponentInternalInstance } from 'vue'
import { getCurrentInstance, onMounted, ref, toRefs, watch } from 'vue'

// Props
const props = defineProps({
  percent: {
    type: Number,
    default: 0,
    validator: (val: number) => val >= 0 && val <= 100,
  },
  inactiveColor: {
    type: String,
    default: '#ececec',
  },
  activeColor: {
    type: String,
    default: '#009dff',
  },
  borderWidth: {
    type: [Number, String],
    default: 14,
  },
  width: {
    type: [Number, String],
    default: 200,
  },
  duration: {
    type: [Number, String],
    default: 1500,
  },
  bgColor: {
    type: String,
    default: '#ffffff',
  },
})

const { percent, inactiveColor, activeColor, borderWidth, width, duration, bgColor } = toRefs(props)

// Methods
const randomId = (): string => `progressId${Math.floor(Math.random() * 1000000)}`

// Data properties
const canvasId = ref(randomId())
const elId = ref(randomId())
const widthPx = ref(uni.upx2px(Number(width.value)))
const borderWidthPx = ref(uni.upx2px(Number(borderWidth.value)))
const startAngle = ref(-Math.PI / 2)
const progressContext = ref<CanvasRenderingContext2D | null>(null)
const newPercent = ref(percent.value)
const oldPercent = ref(0)
const instance = ref() as any

const getCanvas = async (canvasId: string): Promise<CanvasRenderingContext2D> => {
  // if (!instance.value) {
  //   throw new Error('组件实例不可用')
  // }
  const query = uni.createSelectorQuery().in(instance.value)
  return new Promise((resolve, reject) => {
    query
      .select(`#${canvasId}`)
      .fields({ node: true, size: true }, {} as any) // 进行类型断言
      .exec(res => {
        if (!res[0]) {
          console.error(`Canvas with id ${canvasId} not found`)
          return reject(new Error(`Canvas with id ${canvasId} not found`))
        }
        const canvas = res[0].node
        const ctx = canvas.getContext('2d')
        const dpr = uni.getSystemInfoSync().pixelRatio
        canvas.width = res[0].width * dpr
        canvas.height = res[0].height * dpr
        ctx.scale(dpr, dpr)
        resolve(ctx)
      })
  })
}

const drawProgressBg = async () => {
  try {
    const ctx = await getCanvas(canvasId.value)
    ctx.translate(0.5, 0.5)
    ctx.lineWidth = borderWidthPx.value
    ctx.strokeStyle = inactiveColor.value
    ctx.beginPath()
    const radius = widthPx.value / 2
    ctx.arc(radius, radius, radius - borderWidthPx.value, 0, 2 * Math.PI, false)
    ctx.stroke()
  } catch (error) {
    console.error(error)
  }
}

const drawCircleByProgress = async (progress: number) => {
  if (oldPercent.value === 0 && newPercent.value === 0) {
    return
  }
  try {
    let ctx = progressContext.value
    if (!ctx) {
      ctx = await getCanvas(elId.value)
      progressContext.value = ctx
    }
    ctx.lineCap = 'round'
    ctx.lineWidth = borderWidthPx.value
    ctx.strokeStyle = activeColor.value
    const time = Math.floor(Number(duration.value) / 200)
    const endAngle = ((2 * Math.PI) / 100) * progress + startAngle.value
    ctx.beginPath()
    const radius = widthPx.value / 2
    ctx.arc(radius, radius, radius - borderWidthPx.value, startAngle.value, endAngle, false)
    ctx.stroke()
    if (newPercent.value > oldPercent.value) {
      progress++
      if (progress > newPercent.value) return
    } else {
      progress--
      if (progress < newPercent.value) return
    }
    setTimeout(() => {
      drawCircleByProgress(progress)
    }, time)
  } catch (error) {
    console.error(error)
  }
}

// Watchers
watch(
  () => percent.value,
  (newVal, oldVal) => {
    if (newVal > 100) newVal = 100
    if (newVal < 0) oldVal = 0
    newPercent.value = newVal
    oldPercent.value = oldVal
    setTimeout(() => {
      drawCircleByProgress(oldVal)
    }, 50)
  }
)

// Lifecycle hooks
onMounted(() => {
  instance.value = getCurrentInstance()
  drawProgressBg()
  drawCircleByProgress(oldPercent.value)
})
</script>

<style scoped>
.circle-progress {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}
.canvas-bg {
  position: absolute;
}
.canvas {
  position: absolute;
}
</style>
