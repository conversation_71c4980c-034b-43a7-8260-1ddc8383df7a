<template>
  <view v-if="!showCropper">
    <up-dropdown inactive-color="#333333">
      <up-dropdown-item
        v-model="nowGrade"
        :title="nowGrade || '年级'"
        :options="gradeList"
        :disabled="mode === 'look'"></up-dropdown-item>
      <up-dropdown-item
        v-model="nowSubject"
        :title="nowSubject || '科目'"
        :options="subjectList"
        :disabled="mode === 'look'"></up-dropdown-item>
    </up-dropdown>
    <view class="w750rpx p30rpx box-border">
      <!-- <view class="flex justify-between items-center text-36rpx">
      <view class="flex items-center">
        <view class="w8rpx h39rpx bg-#459AF7"></view>
        <view class="ml34rpx text-#333333">{{
          mode === 'clock' ? '专项练习上传' : '拍照上传试卷'
        }}</view>
      </view>
      <view v-if="mode === 'clock'" class="text-#459AF7">更多></view>
    </view> -->
      <view class="flex flex-wrap mt20rpx gap-30rpx justify-between">
        <view
          v-for="(item, index) in imgList"
          :key="index"
          class="w330rpx h400rpx rounded-20rpx b-8rpx b-#c7e0fd b-solid box-border flex-center">
          <!-- look模式下不允许点击上传 -->
          <view
            v-if="!item.url"
            class="flex-center flex-col"
            @click="mode === 'clock' ? handleClick(index) : null">
            <image
              :src="getSystemImg('/6747e9ed0a34815816f11159/670c8c434dfd0e1c49e0a94d')"
              class="w102rpx h82rpx"></image>
            <view class="text-26rpx text-#333333 mt20rpx">{{ item.name }}</view>
          </view>
          <image
            v-else
            class="w-full h-full"
            :src="getHeadIcon(item.url)"
            @click="mode === 'clock' ? handleClick(index) : null"></image>
        </view>
      </view>
      <view
        class="mt-40rpx flex w-690rpx h-95rpx justify-between items-center p-25rpx pl-39rpx box-border bg-white shadow-[0rpx_6rpx_18rpx_1rpx_rgba(209,209,209,0.4)] rounded-15rpx"
        @click="toHistory">
        <view class="text-32rpx text-#333333">历史记录</view>
        <u-icon name="arrow-right" size="24rpx" color="#999999"></u-icon>
      </view>
      <!-- <view v-if="mode === 'clock'" class="text-center text-30rpx text-#999999 mt20rpx">
      确保试卷清晰完整
    </view> -->
    </view>
    <view class="w-full fixed bottom-30rpx p30rpx box-border">
      <u-button
        :text="mode === 'clock' ? '完成上传' : '查看阅卷'"
        shape="circle"
        type="primary"
        :loading="buttonLoading"
        :disabled="buttonLoading"
        @tap="handleButton"></u-button>
    </view>
  </view>

  <view v-if="showCropper && tempImagePath" class="container">
    <cropper
      :src="tempImagePath"
      class="cropper-overlay"
      @crop="onCropComplete"
      @cancel="showCropper = false"
      @choose-image="handleClick(currentImageIndex)" />
  </view>
  <CommonModal
    :show="showModal"
    title="拍照注意事项"
    :show-close-button="true"
    @close="closeModal"
    :title-style="{
      fontSize: '36rpx',
    }">
    <view class="slot-content flex flex-col items-center mb75rpx">
      <image
        class="w503 h487"
        :src="getSystemImg('/6747e9ed0a34815816f11159/6757e70bf8730075fead9d54')"
        mode="aspectFit"></image>
    </view>
  </CommonModal>
</template>

<script setup lang="ts" name="clock-in-special">
import {
  getHeadIcon,
  getSystemImg,
  hideLoading,
  miniProgramUploadFile,
  showLoading,
  showToast,
  showToastBack,
} from '@/utils'
import cropper from '@/components/bt-cropper/cropper.vue'
import CommonModal from '@/components/CommonModal.vue'
import type { Grades } from '@/api/project/exercises/type.d'
import { getGradeAndSubject } from '@/api/project/exercises'
import type { ISpecialClockIn } from '@/api/project/clock-in/type.d'
import { getSpecialClockInDetail, specialClockIn } from '@/api/project/clock-in'
// 专项练习打卡的数据
const specialClockInData = ref<ISpecialClockIn>({
  code: '',
  images: [],
  special_type: '日积月累',
  grade: '',
  subject: '',
})

// 年级 科目联动数据
const gradeSubjectData = ref<Grades[]>([])
const nowGrade = ref('')
const nowSubject = ref('')
const getData = async () => {
  const {
    data: { list },
  } = await getGradeAndSubject()
  gradeSubjectData.value = list
}
// 年级下拉
const gradeList = computed(() => {
  return gradeSubjectData.value.map(item => {
    return {
      label: item.grade,
      value: item.grade,
    }
  })
})
// 科目下拉
const subjectList = computed(() => {
  const list = gradeSubjectData.value.find(item => item.grade === nowGrade.value)?.tableField107
  return list?.map(item => {
    return {
      label: item.subject,
      value: item.subject,
    }
  })
})

// 弹窗
const showModal = ref(false)
const closeModal = () => {
  showModal.value = false
  uni.setStorageSync('isLook', true)
}

// 跳转到历史记录页面
const toHistory = () => {
  uni.navigateTo({
    url: '/subPages/special-marking/special-history',
  })
}
const buttonDisabled = ref(true)

// 模式 专项练习打卡 专项练习查看
const mode = ref('clock')
const currentImageIndex = ref(0)
const imgList = ref<UploadImgData[]>([
  {
    name: '上传第1页',
    url: '',
  },
  {
    name: '上传第2页',
    url: '',
  },
  {
    name: '上传第3页',
    url: '',
  },
  {
    name: '上传第4页',
    url: '',
  },
])

// 裁剪
const showCropper = ref(false)
const tempImagePath = ref('')

const handleClick = (index: number) => {
  // look模式下不允许上传图片
  if (mode.value === 'look') return

  if (!nowGrade.value || !nowSubject.value) return showToast('请选择年级和科目')
  currentImageIndex.value = index
  uni.chooseImage({
    count: 1,
    sizeType: ['compressed'],
    sourceType: ['album', 'camera'],
    success: res => {
      tempImagePath.value = res.tempFilePaths[0]
      showCropper.value = true
    },
  })
}

const onCropComplete = (filePath: string) => {
  showCropper.value = false
  miniProgramUploadFile(filePath).then(res => {
    imgList.value[currentImageIndex.value].url = res.data.url
  })
}

// 检查图片列表
const checkImgList = (list: UploadImgData[]) => {
  let count = 4
  list.forEach(item => {
    if (!item.url) {
      showToast('请上传所有图片')
      count--
    }
  })
  return count === 4
}
const gradingPaperId = ref('')
const buttonLoading = ref(false)
const handleButton = () => {
  if (mode.value === 'clock') {
    if (!nowGrade.value || !nowSubject.value) return showToast('请选择年级和科目')
    if (!checkImgList(imgList.value)) return

    // 设置按钮loading状态
    buttonLoading.value = true

    specialClockInData.value.grade = nowGrade.value
    specialClockInData.value.subject = nowSubject.value
    specialClockInData.value.images = imgList.value
    specialClockIn(specialClockInData.value)
      .then(res => {
        console.log(res.data)
        gradingPaperId.value = res.data
        uni.$emit('special')

        showToast('上传成功')

        // 上传成功后跳转到look模式
        setTimeout(() => {
          uni
            .redirectTo({
              url: `/subPages/special-marking/special?openType=look&id=${res.data}`,
            })
            .then(() => {
              buttonLoading.value = false
            })
        }, 1500)
      })
      .catch(error => {
        console.error('上传失败:', error)
        showToast('上传失败，请重试')
        buttonLoading.value = false
      })
  } else {
    uni.navigateTo({
      url: `/subPages/special-marking/student-grading-papers?id=${gradingPaperId.value}`,
    })
  }
}

// 加载look模式的数据
const loadLookModeData = async (id: string) => {
  try {
    const { data } = await getSpecialClockInDetail(id)
    // 回显年级和科目
    nowGrade.value = data.grade
    nowSubject.value = data.subject
    // 回显图片
    imgList.value = data.images as unknown as UploadImgData[]
    gradingPaperId.value = data.paperId || id
  } catch (error) {
    console.error('加载数据失败:', error)
    showToast('加载数据失败')
  }
}

onLoad((e: any) => {
  // 判断是专项练习打卡还是专项练习查看
  // e.openType = 'look'
  if (e.grade && e.subject) {
    nowGrade.value = e.grade
    nowSubject.value = e.subject
  }

  if (e.openType === 'clock') {
    mode.value = 'clock'
    // 判断是否已查看弹窗
    const isLook = uni.getStorageSync('isLook')
    if (isLook) {
      showModal.value = false
    } else {
      showModal.value = true
      setInterval(() => {
        buttonDisabled.value = false
      }, 3000)
    }
  } else if (e.openType === 'look') {
    mode.value = 'look'
    // look模式下加载数据
    if (e.id) {
      loadLookModeData(e.id)
    }
  } else {
    mode.value = e.openType || 'clock'
  }
  getData()
})
</script>

<style scoped>
.container {
  position: relative;
  height: 100%;
  width: 100%;
}
.cropper-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 999; /* 确保裁剪组件在最上面 */
}

/* #ifdef H5 */
/* 移除了与up-modal相关的样式，因为现在使用CommonModal */
/* #endif */
</style>

<style>
/* #ifndef H5 */
/* 移除了与up-modal相关的样式，因为现在使用CommonModal */
/* #endif */
</style>
