<template>
  <view class="h-screen w-[750rpx] flex flex-col bg-[#f5f5f5] relative">
    <!-- 聊天消息区域 -->
    <scroll-view
      class="absolute top-0 left-0 right-0 p20rpx box-border"
      :style="{
        bottom: `${inputAreaTotalHeight}px`,
      }"
      scroll-y
      :scroll-with-animation="true"
      :scroll-into-view="lastMessageId"
      :scroll-top="scrollTop">
      <view class="message-list flex flex-col gap-[20rpx] text-28rpx pb-[20rpx]">
        <template v-for="(message, index) in chatMessages" :key="index">
          <view :id="`msg-${index}`">
            <!-- 用户消息 -->
            <view
              v-if="message.type === 'user'"
              class="flex gap-[20rpx] mb-[20rpx] flex-row-reverse">
              <view
                class="flex-shrink-0 w-[80rpx] h-[80rpx] rounded-full overflow-hidden flex items-center justify-center ml-[20rpx]">
                <u-avatar :src="getHeadIcon(userStore.userInfo?.headIcon)" size="80rpx" />
              </view>
              <view class="flex flex-col gap-[8rpx] items-end max-w-[70%]">
                <view class="text-[28rpx] text-[#666] mb8rpx">{{ message.sender }}</view>
                <view
                  class="p-[20rpx] rounded-[20rpx] text-[28rpx] bg-[#007aff] text-white"
                  style="white-space: pre-wrap; word-break: break-all">
                  {{ message.content }}
                </view>
              </view>
            </view>

            <!-- AI 消息 -->
            <view v-else class="flex gap-[20rpx] mb-[20rpx]">
              <view
                class="flex-shrink-0 mr-[20rpx] w-[80rpx] h-[80rpx] rounded-full overflow-hidden flex items-center justify-center">
                <image :src="avatarUrl" class="w-full h-full object-cover" mode="aspectFill" />
              </view>
              <view class="flex flex-col gap-[8rpx] max-w-[70%]">
                <view class="text-[28rpx] text-[#666] mb8rpx">{{ message.sender }}</view>
                <view class="p-x-[20rpx] rounded-[20rpx] text-[28rpx] bg-white text-[#333]">
                  <mp-html :content="message.content" :markdown="true" />
                  <text v-if="isLoading && index === chatMessages.length - 1" class="cursor-blink"
                    >|</text
                  >
                </view>
              </view>
            </view>
          </view>
        </template>
      </view>
    </scroll-view>
    <!-- 输入区域 -->
    <view
      class="absolute bottom-0 left-0 right-0 p-[20rpx] bg-white border-t-[2rpx] border-[#eee]"
      :style="{ paddingBottom: `${bottomHeight}px` }">
      <view class="flex items-center gap-[20rpx]">
        <button
          class="w-[80rpx] h-[80rpx] mr10rpx flex items-center justify-center bg-transparent border-none p-0 after:border-none">
          <image
            :src="getSystemImg('6747e9ed0a34815816f11159/67726510f8730075feada197')"
            mode="scaleToFill"
            class="w-[62.88rpx] h-[47.16rpx]" />
        </button>
        <input
          v-model="inputValue"
          class="flex-1 h-[80rpx] px-[20rpx] bg-[#f5f5f5] rounded-[40rpx]"
          type="text"
          placeholder="描述您的问题"
          @confirm="sendMessage" />
        <button
          class="w-[80rpx] h-[80rpx] ml10rpx flex items-center justify-center bg-transparent border-none p-0 after:border-none"
          @click="sendMessage">
          <image
            :src="getSystemImg('6747e9ed0a34815816f11159/6772633ef8730075feada195')"
            mode="scaleToFill"
            class="w-[55.88rpx] h-[55.16rpx]" />
        </button>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts" name="ai-chat">
import { getHeadIcon, getSystemImg, getSystemImgUrl, showToast } from '@/utils'
import useUserStore from '@/store/modules/user'
import { getMyLogWordList, getWrongWordList } from '@/api/project/recite-words'

// ai头像
const avatarUrl = getSystemImg('6747e9ed0a34815816f11159/67726514f8730075feada199')

// fastgpt应用ID
const appId = ref(import.meta.env.VITE_FASTGPT_APP_ID)

// 用户信息
const userStore = useUserStore()

// 聊天消息数据类型
type ChatMessage = {
  type: 'user' | 'ai'
  sender: string
  content: string
}
// 聊天消息
const chatMessages = ref<ChatMessage[]>([])

// 添加消息列表高度的响应式引用
const messageListHeight = ref(0)

// 添加scrollTop响应式变量
const scrollTop = ref(0)

// 获取系统安全区域的高度
const safeAreaBottom = computed(() => {
  const systemInfo = uni.getSystemInfoSync()
  return systemInfo.safeAreaInsets?.bottom || 0
})

// 输入框
const inputValue = ref('')

// 最后消息ID
const lastMessageId = ref('')

// 计算底部安全区域和键盘高度
const bottomHeight = computed(() => {
  const systemInfo = uni.getSystemInfoSync()
  const fallbackPx = (20 * systemInfo.windowWidth) / 750
  return safeAreaBottom.value > 0 ? safeAreaBottom.value : fallbackPx
})

// 修改 INPUT_AREA_BASE_HEIGHT 的计算方式
const INPUT_AREA_BASE_HEIGHT = computed(() => {
  const systemInfo = uni.getSystemInfoSync()
  return (120 * systemInfo.windowWidth) / 750
})

// 修改 inputAreaTotalHeight 的计算
const inputAreaTotalHeight = computed(() => {
  return INPUT_AREA_BASE_HEIGHT.value
})

// 671080b8e91a9a61d4182b89  jQRJPZetdDjo
const BaseUrl = import.meta.env.VITE_AI_BASE_URL
const token = ref(`Bearer ${import.meta.env.VITE_FASTGPT_TOKEN}`)
const chatId = ref(userStore.userInfo?.id)
const isLogin = computed(() => {
  return userStore.userInfo?.id
})
// 添加loading状态
const isLoading = ref(false)

// 发送消息的方法
const sendMessage = async () => {
  if (!inputValue.value.trim() || isLoading.value) return
  if (!isLogin.value) {
    uni.navigateTo({
      url: '/subPages/user/login',
    })
    return
  }

  // 添加用户消息
  chatMessages.value.push({
    type: 'user',
    sender: 'You',
    content: inputValue.value.trim(),
  })

  // 添加一个空的AI消息用于流式显示
  const aiMessageIndex = chatMessages.value.length
  chatMessages.value.push({
    type: 'ai',
    sender: 'AI',
    content: '',
  })

  // 准备请求数据
  const postData = {
    messages: [
      {
        role: 'user',
        content: inputValue.value.trim(),
      },
    ],
    appId: appId.value,
    chatId: chatId.value,
    detail: true,
    stream: true,
  }

  isLoading.value = true

  // 发送请求
  const requestTask = uni.request({
    url: `${BaseUrl}/api/v1/chat/completions`,
    timeout: 60000,
    responseType: 'text',
    method: 'POST',
    enableChunked: true,
    data: postData,
    header: {
      Accept: 'text/event-stream',
      Authorization: token.value,
    },
    success: response => {},
  }) as any

  // 处理流式响应
  requestTask.onChunkReceived((response: any) => {
    const uint8Array = new Uint8Array(response.data)
    let text = String.fromCharCode.apply(null, uint8Array as any)
    text = decodeURIComponent(escape(text))

    const lines = text.split('\n')
    for (const line of lines) {
      if (line.startsWith('data: ')) {
        const jsonStr = line.substring(6).trim()
        if (jsonStr === '[DONE]') {
          isLoading.value = false
          return
        }

        try {
          const jsonData = JSON.parse(jsonStr)
          const content = jsonData?.choices?.[0]?.delta?.content || ''
          if (content) {
            chatMessages.value[aiMessageIndex].content += content
          }
        } catch (error) {
          console.error('解析JSON失败:', error)
        }
      }
    }
  })
  // 清空输入框
  inputValue.value = ''
}

// scrollToBottom方法
const scrollToBottom = () => {
  nextTick(() => {
    const query = uni.createSelectorQuery()
    query
      .select('.message-list')
      .boundingClientRect((data: any) => {
        if (data) {
          messageListHeight.value = data.height
          // 强制触发滚动
          scrollTop.value = data.height * 2

          // 设置最后消息的ID
          if (chatMessages.value.length > 0) {
            lastMessageId.value = `msg-${chatMessages.value.length - 1}`
          }
        }
      })
      .exec()
  })
}

async function getHistory() {
  const data = await uni.request({
    url: `${BaseUrl}/api/core/chat/getPaginationRecords`,
    method: 'POST',
    header: {
      Authorization: token.value,
      'Content-Type': 'application/json',
    },
    data: {
      appId: appId.value,
      chatId: chatId.value,
      offset: 0,
      pageSize: 20,
      loadCustomFeedbacks: true,
    },
  })
  const res = data.data as any
  const list = res.data.list
  if (res.code === 200 && list.length > 0) {
    chatMessages.value = []
    const history = list.map((item: any) => ({
      type: item.obj === 'AI' ? 'ai' : 'user',
      sender: item.obj === 'AI' ? 'AI' : 'You',
      content: item.value[0].text.content,
    }))

    chatMessages.value = history
  }
  nextTick(() => {
    scrollToBottom()
  })
}

// 修改watch，添加立即执行选项
watch(
  chatMessages,
  () => {
    scrollToBottom()
  },
  { deep: true, immediate: true }
)
// 初始化错误单词列表
async function addWrongWord() {
  const logWordList = await getMyLogWordList()
  if (logWordList.data.length === 0) {
    showToast('学习单词后，对话效果更佳')
    return
  }
  const wordList = logWordList.data
    .map((item: any) => {
      return item.accuracy < 100
        ? `${item.word} - ${(((100 - item.accuracy) / 100) * item.totalAttempts).toFixed(0)}`
        : ''
    })
    .filter(item => item !== '') // 过滤掉空字符串
  inputValue.value = wordList.join('\n')
  sendMessage()
}

// 在组件挂载时也获取一次高度
onMounted(() => {
  if (userStore.userInfo?.id) {
    getHistory()
    addWrongWord()
  } else {
    showToast('请先登录')
    setTimeout(() => {
      uni.navigateTo({
        url: '/subPages/user/login',
      })
    }, 2000)
  }
})
</script>

<style scoped>
.cursor-blink {
  animation: blink 1s step-end infinite;
}

@keyframes blink {
  from,
  to {
    opacity: 1;
  }
  50% {
    opacity: 0;
  }
}
</style>
