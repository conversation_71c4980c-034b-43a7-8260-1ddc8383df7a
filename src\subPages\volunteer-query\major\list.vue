<template>
  <view class="w-full bg-white relative z-9999 pb10rpx overflow-hidden">
    <SearchInput placeholder="请输入招生专业" @on-change="onChange" />
  </view>
  <LoadMoreList
    ref="loadMoreList"
    :request-fn="getBKMajorInterDataList"
    :request-params="{ filters: filter }">
    <template #default="{ list }">
      <view
        v-for="(item,inde) in (list as  IBKInterMajorData[]).sort((x,y)=>y.undergraduate_count-x.undergraduate_count)"
        :key="item._id"
        class="w710rpx h153rpx ma mt20rpx bg-white rounded-10rpx p30rpx box-border flex flex-col justify-between"
        @click="toDetail(item.major_name, item.major_code)">
        <view class="flex justify-between">
          <view class="text-30rpx text-#000000 fw-500" :class="{ hot: inde < 3 }">{{
            item.major_name
          }}</view>
          <view class="text-22rpx text-#969DAB"
            >*招生院校<text class="text-#FF1717">{{ item.undergraduate_count }}</text
            >所</view
          >
        </view>
        <view class="flex">
          <view
            v-for="(ite, index) in splitArray(item.degree_type)"
            :key="index"
            class="w122rpx h40rpx text-center text-22rpx text-#FCB138 lh-40rpx rounded-4rpx mr20rpx bg-#fff7ea">
            {{ ite }}
          </view>
        </view>
      </view>
    </template>
  </LoadMoreList>
</template>

<script setup lang="ts" name="major-list">
import SearchInput from '../components/SearchInput/index.vue'
import { getBKMajorInterDataList } from '@/api/project/index'
import LoadMoreList from '@/components/LoadMoreList.vue'
import type { IBKInterMajorData } from '@/api/project/index/type'
import { splitArray } from '@/utils'

const loadMoreList = ref<InstanceType<typeof LoadMoreList>>()

onReachBottom(() => {
  loadMoreList.value?.onReachBottom()
})

const filter = ref({
  major_name: '',
})

const onChange = (val: string) => {
  filter.value.major_name = val
  loadMoreList.value?.refresh()
}

const toDetail = (name: string, code: string) => {
  uni.navigateTo({
    url: `/subPages/volunteer-query/major/detail?name=${name}&code=${code}`,
  })
}

onLoad(async () => {})
</script>

<style>
page {
  background-color: #f4f5f7;
}
</style>

<style lang="scss" scoped>
.hot::after {
  content: '🔥';
}
</style>
