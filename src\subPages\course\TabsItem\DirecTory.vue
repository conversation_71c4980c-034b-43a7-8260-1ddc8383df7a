<template>
  <view class="w750rpx p20rpx box-border">
    <view class="text-34rpx fw-bold text-#333333"> 课程目录 </view>
    <LoadMoreList
      ref="loadmoreList"
      :request-fn="getDirecToryList"
      :request-params="{ _id: courseId }">
      <template #default="{ totalData }">
        <view
          class="mt10rpx text-26rpx fw-bold text-#333333 b-b-2 b-b-solid pb20rpx b-b-gray"
          :abc="totalData">
          已更新{{ `${dirList.length}/${dirnum}节` }}
        </view>
        <view class="flex flex-col w690rpx ma">
          <view
            v-for="(item, index) in dirList"
            :key="item.catalog_code"
            class="flex justify-between h120rpx w-full items-center">
            <view class="text-30rpx w10% text-#333333"> {{ index + 1 }}</view>
            <view class="flex justify-between items-center w90% h-full b-b-2 b-b-solid b-b-gray">
              <view>
                <view class="text-32rpx text-#333333">
                  {{ item.catalog_name }}
                </view>
                <view class="w-full h10rpx"></view>
                <view class="flex items-center">
                  <u-icon
                    :name="getSystemImg('6747e9ed0a34815816f11159/669a25120bb07d7cd6ed3ac5')"
                    size="14"
                    :label="item.catalog_type"
                    label-size="26rpx"
                    label-color="#969DAB"></u-icon>
                  <view class="ml20rpx">
                    <u-icon
                      name="clock"
                      size="16"
                      :label="item.catalog_duration"
                      label-size="26rpx"
                      label-color="#969DAB"></u-icon>
                  </view>
                  <view
                    v-if="item.catalog_istry === '是'"
                    class="ml30rpx bg-#61c5a1 p6rpx text-white text-24rpx">
                    可试看
                  </view>
                </view>
              </view>
              <u-icon
                :name="
                  currentIndex === index && playStatus ? 'pause-circle-fill' : 'play-circle-fill'
                "
                size="40"
                color="#d4dffb"
                @click="playVideo(index)"></u-icon>
            </view>
          </view>
        </view>
      </template>
    </LoadMoreList>
  </view>
</template>

<script setup lang="ts" name="course-directory">
import { getDirecToryList } from '@/api/project/course/index'
import type { ICatalogTableType, ICourseDirecToryResuleType } from '@/api/project/course/type'
import LoadMoreList from '@/components/LoadMoreList.vue'
import { getSystemImg } from '@/utils'
import useCourseStore from '@/store/modules/course'
const props = defineProps<{
  courseId: string
  dirnum: string
}>()

const emits = defineEmits(['setUrl', 'setVideoStatus'])
const loadmoreList = ref<InstanceType<typeof LoadMoreList>>()
const courseStore = useCourseStore()
// 当前播放的章节索引
const currentIndex = computed(() => {
  return courseStore.currentIndex
})
// 章节列表
const dirList = computed<ICatalogTableType[]>(() => {
  return courseStore.dirList
})
const playStatus = computed(() => {
  return courseStore.playStatus
})

// 播放视频
const playVideo = (index: number) => {
  if (index === currentIndex.value) {
    courseStore.setPlayStatus(!playStatus.value)
  } else {
    courseStore.setCurrentIndex(index)
    courseStore.setPlayStatus(true)
  }
}

watch(
  () => loadmoreList.value?.list,
  newVal => {
    if (newVal?.length) {
      courseStore.setDirList(loadmoreList.value?.list[0] as ICourseDirecToryResuleType)
    }
  }
)

onReachBottom(() => {
  loadmoreList.value!.onReachBottom()
})
</script>

<style lang="scss" scoped></style>
