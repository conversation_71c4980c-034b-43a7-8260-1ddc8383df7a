<template>
  <view class="w750rpx p20rpx box-border">
    <view class="text-34rpx fw-bold text-#333333"> 课程介绍 </view>
    <rich-text
      :nodes="getValue(content || '')"
      class="rich mt20rpx text-28rpx text-#333333 lh-50rpx"></rich-text>
  </view>
</template>

<script setup lang="ts" name="course-introduce">
defineProps<{
  content?: string
}>()

const getValue = (str: string) => {
  return str.replace('img', 'img class="imggg"')
}
</script>

<style lang="scss" scoped>
::v-deep .imggg {
  width: 100%;
  height: 100%;
}
</style>
