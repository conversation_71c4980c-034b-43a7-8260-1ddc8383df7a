<template>
  <view class="common-item flex items-center" @click="$emit('click')">
    <image :src="assembleImgData(imageUrl)" class="w-125rpx h-108.5rpx" radius="4"></image>
    <view class="flex flex-col justify-between h-[108.5rpx] ml-2">
      <text class="font-bold text-sm title" :data-top="top"> {{ title }}</text>
      <view class="text-[#999999] text-xs">
        <span class="iconfont icon-time" style="font-size: 14px"> </span>
        {{ formatDate(date) }}
      </view>
    </view>
  </view>
</template>

<script setup lang="ts" name="CommonItem">
import useDateFormatter from '@/hooks/useDateFormatter'
import { assembleImgData } from '@/utils'
defineProps<{
  imageUrl: UploadImgData
  title: string
  date: string | number
  top: 0 | 1 | undefined
}>()
defineEmits(['click'])

const { formatDate } = useDateFormatter('YYYY-MM-DD')
</script>

<style lang="scss" scoped>
.common-item {
  box-shadow: 0px 5px 8px 0px rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  padding: 20rpx;
  margin-bottom: 10rpx;
  .title {
    &[data-top='1']::before {
      content: '置顶';
      display: inline-block;
      background-color: #fff0f0;
      font-size: 24rpx;
      border-radius: 5rpx;
      padding: 0 10rpx;
      margin-right: 10rpx;
      color: #ff0000;
    }
  }
}
</style>
