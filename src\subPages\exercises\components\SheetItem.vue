<template>
  <view class="topic">
    <view class="topic-wrap">
      <view class="topic-head flex items-center">
        <view class="flex items-center">
          <text class="topic-num">第{{ index }}题</text>
          <view class="topic-tag color-theme">{{ detail?.type }}</view>
          <view v-if="detail?.rate" class="ml-2rpx">校准确率：{{ detail?.rate }}</view>
        </view>
        <view v-if="collect" class="collect-btn flex items-center" @tap="bookMark">
          <u-icon
            :name="detail?.collect ? 'star-fill' : 'star'"
            :color="detail?.collect ? '#ffcf52' : undefined"
            size="20" />
          <text>收藏</text>
        </view>
      </view>
      <mp-html class="topic-title" :content="replaceSpanWithLatex(detail?.title)"></mp-html>
      <!-- 单选多选题 -->
      <view v-if="detail?.type === '单选题' || detail?.type === '多选题'">
        <view class="w-full margin-t-24">
          <view class="topic-context report">
            <view
              v-for="(item, index2) in detail?.option"
              :key="index2 + item"
              class="topic-option flex items-center sizing-border"
              :class="styleCheck('single', 0, index2).value"
              @tap="pickOptions(index2, 0)">
              <text>{{ constants[index2] }}. </text>
              <mp-html :content="replaceSpanWithLatex(item)"></mp-html>
            </view>
          </view>
        </view>
      </view>
      <view v-else-if="detail?.type === '阅读理解' || detail?.type === '完形填空'">
        <view v-for="(item, index2) in detail?.tableField116" :key="item._id">
          <mp-html
            class="topic-title"
            :content="`${replaceSpanWithLatex(item.title)}(${item.type})`"></mp-html>
          <view class="w-full margin-t-24">
            <view class="topic-context report">
              <view
                v-for="(item1, index1) in item.option"
                :key="index1 + item1"
                class="topic-option flex items-center sizing-border"
                :class="styleCheck('read', index2, index1).value"
                @tap="pickOptions(index2, index1)">
                <text>{{ constants[index1] }}. </text>
                <mp-html :content="replaceSpanWithLatex(item1)"></mp-html>
              </view>
            </view>
          </view>
        </view>
      </view>
      <view v-else-if="detail?.type === '判断题'">
        <view class="w-full margin-t-24">
          <view class="topic-context report">
            <view
              v-for="(item, index2) in judge"
              :key="index2 + item"
              class="topic-option flex items-center sizing-border"
              :class="styleCheck('judge', 0, index2).value"
              @tap="pickOptions(index2, 0)">
              <text>{{ item }}</text>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts" name="sheet-item">
// 数学公式mphtml 开启扩展latex
import mpHtml from './mp-html/mp-html.vue'
import { replaceSpanWithLatex } from '@/utils/index'

const props = defineProps({
  detail: {
    type: Object,
    default: () => ({}),
  },
  index: {
    type: Number,
  },
  // 是否展示收藏按钮
  collect: {
    type: Boolean,
    default: true,
  },
  // 选项的模式 i
  optionReadOnly: {
    type: Boolean,
    default: false,
  },
})
const emit = defineEmits(['checkOption', 'bookMark'])
const constants = ref(['A', 'B', 'C', 'D', 'E', 'F', 'G'])
const judge = ref(['对', '错'])

function checkAnswer(correctAnswer: string, userAnswer: string, referenceAnswer: string[]) {
  if (userAnswer === undefined || userAnswer === null) {
    // 这里进入了错题模式,用户答案为空,参考答案不为空,直接返回参考答案选中的样式
    return referenceAnswer && referenceAnswer[0].includes(correctAnswer) ? 'active' : ''
  }
  if (referenceAnswer === undefined || referenceAnswer.length === 0) {
    console.log('参考答案为空')
    return ''
  }
  const userAnswersSet = new Set(userAnswer.split(','))
  const referenceAnswersSet = new Set(referenceAnswer[0].split(''))

  const isInUserAnswer = userAnswersSet.has(correctAnswer)
  const isInReferenceAnswer = referenceAnswersSet.has(correctAnswer)
  // 判断两个答案集合,用户答案集合和参考答案集合是否相等
  function areSetsEqual(set1: Set<string>, set2: Set<string>) {
    if (set1.size !== set2.size) return false
    return [...set1].every(item => set2.has(item))
  }
  if (!isInUserAnswer && !isInReferenceAnswer) {
    return '' // 该选项既不在用户选项中,也不在正确答案中
  } else if (isInUserAnswer && !isInReferenceAnswer) {
    return 'wrong' // 该选项在用户选项中,但是不在正确答案中
  } else if (!isInUserAnswer && isInReferenceAnswer) {
    if (userAnswersSet.size <= referenceAnswersSet.size) {
      return ' correct unfinished' // 该选项在正确答案中,但是不在用户选项中
    } else {
      return 'wrong unfinished' // 该选项在正确答案中,但是不在用户选项中
    }
  } else if (isInUserAnswer && isInReferenceAnswer) {
    if (areSetsEqual(userAnswersSet, referenceAnswersSet)) {
      return 'correct' // 该选项在用户选项中,也在正确答案中,整体的答案正确
    } else {
      return 'wrong unfinished' // 该选项在用户选项中,也在正确答案中,但是整体的答案错误
    }
  }
}

// 选项选中样式
const styleCheck = (type: string, index: number, answer: number) => {
  return computed(() => {
    const correctAnswer = constants.value[answer] // 单选和多选的当前选项
    const correctJudge = judge.value[answer] // 判断题的当前选项
    const referenceAnswer = props.detail.answer // 参考答案
    const getUserAnswer = () => {
      if (props.detail.testType === 'analysis' || props.detail.testType === 'report') {
        switch (type) {
          case 'single':
          case 'judge':
            return props.detail.user_answer
          case 'read':
            return props.detail.tableField116[index]?.user_answer
          default:
        }
      } else {
        switch (type) {
          case 'single':
          case 'judge':
            return props.detail.user_answer
          case 'read':
            return props.detail.tableField116[index]?.user_answer
          default:
        }
      }
    } // 用户答案
    const userAnswer = getUserAnswer()
    // console.log("用户答案", userAnswer,"参考答案", referenceAnswer);
    // console.log(props.detail.testType,props.detail.testType);
    if (props.detail.testType === 'analysis' || props.detail.testType === 'report') {
      if (type === 'judge') {
        return checkAnswer(correctJudge, userAnswer, referenceAnswer)
      }
      // 这里当我面在分析模式中,和学习记录报告中,判断样式
      return checkAnswer(correctAnswer, userAnswer, referenceAnswer)
    }
    // 下面这个代码表示在答题模式下,选中的样式
    if (type === 'judge') {
      // 判断题选中样式
      return userAnswer && userAnswer.includes(correctJudge) ? 'active' : ''
    }
    return userAnswer && userAnswer.includes(correctAnswer) ? 'active' : ''
  })
}
function bookMark() {
  emit('bookMark')
}
function pickOptions(index: number, index1: number) {
  if (props.detail.type === '单选题' || props.detail.type === '多选题') {
    emit('checkOption', 0, constants.value[index])
  } else if (props.detail.type === '判断题') {
    console.log('判断题', judge.value[index])
    emit('checkOption', 0, judge.value[index])
  } else {
    emit('checkOption', index, constants.value[index1])
  }
}
</script>

<style lang="scss" scoped>
@import '@/common/css/index.scss';
.topic {
  // 内边距左右30rpx
  $pd_l_r: 30rpx;
  // padding-bottom: 50px; //tabbar高度
  & > view {
    margin-bottom: 12rpx;
    background-color: #fff;
    padding: 0 $pd_l_r;
  }
  .topic-wrap {
    padding-bottom: 24rpx;
  }
  .topic-head {
    justify-content: space-between;
    padding-top: 18rpx;
    .topic-num {
      font-size: 30rpx;
      font-weight: bold;
      line-height: 58rpx;
    }
    .topic-tag {
      margin-left: 30rpx;
      background: #ecf1ff;
      border-radius: 2px;
      padding: 4rpx 10rpx;
      font-size: 26rpx;
      line-height: 1.4;
    }
    .collect-btn {
      view > text {
        font-size: 40rpx !important;
      }
      & > text {
        margin-left: 12rpx;
      }
    }
  }
  .topic-title {
    margin-top: 24rpx;
    font-size: 28rpx;
    color: #333;
    line-height: 29px;
  }
  .topic-result {
    font-size: 28rpx;
    .correct {
      color: #61c5a1;
    }
    .wrong {
      color: #ff776a;
    }
    text {
      margin-right: 1em;
      line-height: 2.5;
    }
    & > text {
      margin-right: 4em;
    }
  }
  .preview-image {
    position: fixed;
    padding: 0 12rpx;
    width: 100%;
    height: 100%;
    left: 0;
    top: 0;
    background-color: #000;
    z-index: 2023;
  }
}
// 选项样式
.topic-context {
  .topic-option {
    margin: 20rpx 0;
    padding: 20rpx 1em;
    background: #f6f7f8;
    border-radius: 10rpx;
    color: $u-content-color;
    &.filter:not(.none) > rich-text {
      filter: invert(100%);
      color: #000;
    }
    &.active {
      background: $u-theme-color;
      color: #fff;
    }
    &.unfinished {
      opacity: 0.65;
    }
    &.correct {
      background-color: #61c5a1;
      color: #fff;
    }
    &.wrong {
      background-color: #ff776a;
      color: #fff;
    }
    & > text {
      display: block;
      height: fit-content;
      margin-right: 0.5em;
    }
  }
}
</style>
