<template>
  <view
    class="head sticky top-0 w-full z-9 min-h-86rpx row bg-#459AF7 flex text-white justify-around items-center text-20rpx bor-b">
    <view
      v-for="(i, index) in titles"
      :key="index"
      class="flex-1 h-full text-center col flex flex-center"
      :style="(index === 1 || index === 0) && type === 'major' ? 'flex: 2' : ''">
      <rich-text :nodes="i"> </rich-text>
    </view>
  </view>
  <view class="b-solid b-1 b-#D1D1D1 b-t-none" v-show="list?.length !== 0">
    <view
      v-for="(ite, index) in list"
      :key="`${index}1`"
      class="min-h-80rpx w-full row flex text-#333333 bg-white justify-around items-center text-18rpx relative bor-b">
      <!-- 添加水印，只在截屏时显示 -->
      <view :style="{ opacity: isScreenshot ? 1 : 0 }" class="watermark-container">
        <view class="watermark">湖南人人学教育</view>
      </view>
      <!-- <view
      v-for="(it, inde) in ite"
      :key="`${inde}11`"
      :style="(inde === 1 || inde === 0) && type === 'major' ? 'flex: 2' : ''"
      :class="inde === 0 ? 'color-b' : ''"
      class="col flex-1 text-center bor-r flex flex-center"
      @click="toDetail(it, inde)">
      <view class="m6rpx">
        {{ it || '—' }}
      </view>
    </view> -->
      <view
        v-for="(it, inde) in ite"
        :key="`${inde}11`"
        :style="(inde === 1 || inde === 0) && type === 'major' ? 'flex: 2' : ''"
        class="col flex-1 text-center flex flex-center bor-r min-h-80rpx"
        @click="toDetail(it, inde)">
        <view class="m6rpx">
          {{ it || '—' }}
        </view>
      </view>
    </view>
  </view>
  <u-empty text="暂无数据" :show="list?.length === 0"></u-empty>
</template>

<script setup lang="ts" name="report-major-major-table">
const props = defineProps<{
  titles: string[]
  list: string[][]
  type?: string
}>()

const emits = defineEmits(['toJump'])

const toDetail = (val: string, index: number) => {
  if (index === 0) {
    if (props.titles[0] === '专业名称') {
      emits('toJump', val)
    } else if (props.titles[0] === '学校名称') {
      emits('toJump', val)
    }
  }
}

const isScreenshot = ref(true)

const handleScreenshot = async () => {
  isScreenshot.value = true
  await nextTick()
  setTimeout(() => {
    isScreenshot.value = false
  }, 5000)
}

// 组件挂载时添加事件监听
onMounted(() => {
  uni.onUserCaptureScreen(handleScreenshot)
})

// 组件卸载时移除事件监听
onUnmounted(() => {
  uni.offUserCaptureScreen(handleScreenshot)
})
</script>

<style lang="scss" scoped>
.color-b {
  color: #459af7;
}

.bor-bor {
  border: 1px solid #edeeee;
}
.bor-b {
  border-bottom: 1px solid #edeeee;
}
.bor-r {
  border-right: 1px solid #edeeee;
  height: 100%;
  box-sizing: border-box;
}
.bor-r:last-child {
  border-right: none;
}
.row.head {
  overflow: hidden;
  .col {
    rich-text {
      width: 100%;
    }
    &::after {
      content: '';
      height: 51rpx;
      background-color: #edeeee;
      width: 1rpx;
      opacity: 0.42;
    }
    &:last-child::after {
      content: none;
    }
  }
}

/* 数据行样式 */
.row:not(.head) {
  display: flex;
  align-items: stretch; /* 确保所有列等高 */
}

/* 水印容器样式 */
.watermark-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 999;
  pointer-events: none;
}

/* 水印文字样式 */
.watermark {
  font-size: 40rpx;
  color: rgba(0, 0, 0, 0.3);
  white-space: nowrap;
  text-align: center;
  user-select: none;
  letter-spacing: 30rpx;
  opacity: 0.1;
  transform: rotate(-5deg);
}
</style>
