<template>
  <view>
    <LoadingPage
      empty-text="暂无题目"
      loading-text="错题加载中..."
      :loading="loading"
      :empty="empty">
      <template #default="{ show }">
        <view v-if="show" class="p30rpx">
          <view v-for="(item, index) in listData" :key="index">
            <view class="test-item" @tap="gotoTest(item)">
              <view class="text-30rpx">{{ item?._id }}</view>
              <view class="flex text-26rpx items-center color-#969DAB">
                <view class="mr18rpx">
                  {{ `${item.total}道` }}
                </view>
                <image
                  class="w13.5rpx h26rpx"
                  :src="getSystemImg('6747e9ed0a34815816f11159/669f29a00bb07d7cd6ed41a6')"></image>
              </view>
            </view>
          </view>
        </view>
      </template>
    </LoadingPage>
  </view>
</template>

<script setup lang="ts" name="my-collect">
import LoadingPage from '@/components/LoadingPage.vue'
import { getMyWrongList, getTopicListByIds } from '@/api/project/exercises/index'
import { getSystemImg } from '@/utils/index'
import useExercisesStore from '@/store/modules/exercises'
const loading = ref(true)
const empty = ref(false)
const listData = ref([] as any)
const useExercises = useExercisesStore()
// 跳转
async function gotoTest(item: any) {
  const result = await getTopicListByIds(item.ids)
  useExercises.setListData(result.data.list)
  uni.navigateTo({
    url: `/subPages/exercises/topic-transform?mode=report&title=我的错题`,
  })
}
onMounted(() => {
  getMyCollectInfo()
})
function setLoadingState(list: any) {
  loading.value = false
  if (list.length === 0) empty.value = true
}
function getMyCollectInfo() {
  getMyWrongList().then(({ data }) => {
    listData.value = data
    setLoadingState(data)
  })
}
</script>

<style>
page {
  background: #f3f5f7;
}
</style>

<style lang="scss" scoped>
.test-item {
  display: flex;
  justify-content: space-between;
  height: 102rpx;
  width: 690rpx;
  box-sizing: border-box;
  padding: 30rpx;
  background: #ffffff;
  border-radius: 20rpx;
  margin-bottom: 20rpx;
}
</style>
