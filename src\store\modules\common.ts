import { defineStore } from 'pinia'

export default defineStore(
  'common',
  () => {
    // 音频播放器
    const audio = uni.createInnerAudioContext()

    // 播放单词
    function playWord(word: string) {
      audio.stop()
      audio.src = `https://dict.youdao.com/dictvoice?audio=${word}&type=2`
      audio.play()
    }

    return {
      audio,
      playWord,
    }
  },
  {
    persist: {
      paths: [],
      storage: {
        getItem: uni.getStorageSync,
        setItem: uni.setStorageSync,
      },
    },
  }
)
