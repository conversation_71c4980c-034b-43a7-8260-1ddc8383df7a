<template>
  <view
    class="w730rpx h180rpx rounded-10rpx bg-white flex justify-between items-stretch p30rpx box-border ma m-y-20rpx relative">
    <view class="flex absolute top-30rpx right-30rpx text-22rpx text-#666666 items-center">
      <view @click="toDetail(itemData._id)"> 查看更多> </view>
    </view>
    <u-image
      :src="assembleImgData(itemData.school_badge?.[0])"
      :width="imgSize?.width || '130rpx'"
      :height="imgSize?.height || '130rpx'"
      mode="aspectFit"></u-image>
    <view class="flex-1 ml40rpx flex flex-col justify-between">
      <view class="text-30rpx text-#333333 fw-bold">{{ itemData.school_name || '湖南大学' }}</view>
      <view class="text-[#969DAB] text-22rpx">
        <view>
          普招计划：{{ itemData.general_count || '暂无数据' }} （网报人数：{{
            itemData.general_online_count || '暂无数据'
          }}）
        </view>
        <view>
          专项计划：{{ itemData.special_count || '暂无数据' }}（网报人数：{{
            itemData.special_online_count || '暂无数据'
          }}）
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts" name="school-item">
import type { IBKSchoolData } from '@/api/project/index/type'
import { assembleImgData, getSystemImg } from '@/utils'

const p = defineProps<{
  itemData: IBKSchoolData
  imgSize?: {
    width: string
    height: string
  }
}>()

const toDetail = (id: string) => {
  uni.navigateTo({
    url: `/subPages/index/school/detail?id=${id}`,
  })
}
</script>

<style lang="scss" scoped></style>
