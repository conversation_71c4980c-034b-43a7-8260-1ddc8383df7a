<template>
  <view class="text-30rpx text-#333333 flex items-center p25rpx" :style="`font-size: ${fontSize};`">
    <view
      class="w8rpx h26rpx bg-#459AF7 rounded-10rpx"
      :style="`background-color: ${bgColor}; height:${hei}rpx`"></view>
    <text class="ml20rpx">{{ title }}</text>
  </view>
</template>

<script setup lang="ts" name="section">
defineProps<{
  title: string
  fontSize?: number
  bgColor?: string
  hei?: string
}>()
</script>

<style lang="scss" scoped></style>
