import { defineStore } from 'pinia'
import { getZsbExamTime } from '@/api/project/index'

export default defineStore(
  'index',
  () => {
    const getCurrentYear = async () => {
      const {
        data: { list },
      } = await getZsbExamTime()
      if (list.length === 0) return new Date().getFullYear()
      const year = new Date(list[0].exam_time).getFullYear()

      if (list[0].exam_time >= new Date().getTime()) return year
      return year + 1
    }

    return {
      getCurrentYear,
    }
  },
  {
    persist: {
      paths: [],
      storage: {
        getItem: uni.getStorageSync,
        setItem: uni.setStorageSync,
      },
    },
  }
)
