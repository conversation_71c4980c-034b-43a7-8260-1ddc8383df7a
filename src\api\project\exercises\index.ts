import type * as ReciteType from './type.d'
import {
  createModelData,
  dataInterface,
  deleteModelData,
  getModelDataDetail,
  getModelList,
} from '@/api/visual'
import { post } from '@/config/request'
import type { FilterType } from '@/es/request'
import useDateFormatter from '@/hooks/useDateFormatter'
const { formatDate } = useDateFormatter('YYYY-MM-DD')

export function getGradeAndSubject() {
  return getModelList<ReciteType.Grades>(
    {
      menuId: '66616c4fc68a8307d74047c1',
      pageSize: -1,
    },
    {
      unAuth: true,
    }
  )
}
// 新建收藏
export function createCollect(data: any) {
  return createModelData({
    menuId: '66a73b4e31bb187c942c7b6e',
    data: JSON.stringify(data),
  })
}
// 取消收藏
export const deleteCollect = (_id: string) => {
  return deleteModelData({
    menuId: '66a73b4e31bb187c942c7b6e',
    _id,
  })
}
// 模拟考试
export function getMockExamList(type: string) {
  return getModelList<ReciteType.TestingItemData>({
    menuId: '66a74e0431bb187c942c7b77',
    pageSize: -1,
    filter: [
      {
        enCode: 'subject',
        method: 'eq',
        type: 'custom',
        value: [type],
      },
    ],
    connect: 'and',
  })
}
// 章节练习
export function getChapterList(type: string) {
  return getModelList<ReciteType.TestingItemData>({
    menuId: '66a74ae531bb187c942c7b71',
    pageSize: -1,
    filter: [
      {
        enCode: 'subject',
        method: 'eq',
        type: 'custom',
        value: [type],
      },
    ],
    connect: 'and',
  })
}
// 真题练习
export function getRealExamList(type: string) {
  return getModelList<ReciteType.TestingItemData>({
    menuId: '66a74b7431bb187c942c7b74',
    pageSize: -1,
    filter: [
      {
        enCode: 'subject',
        method: 'eq',
        type: 'custom',
        value: [type],
      },
    ],
    connect: 'and',
  })
}
// 根据组卷id获取list
export function getRealExamDetail(id: string) {
  return dataInterface<ReciteType.TopicItem[]>({
    id: '6698c2c2c366000049002682',
    data: {
      '@id': id,
    },
  })
}
// 随机刷题list
export function getRandomList(grade: string, subject: string, num: string) {
  return dataInterface<ReciteType.TopicItem[]>({
    id: '64f05c2d6c3a0000be0079b1',
    data: {
      '@grade': grade,
      '@subject': subject,
      '@num': num,
    },
  })
}
// 首页坚持天数和刷题正确率
export function getDaysAndAccuracy() {
  return dataInterface<ReciteType.DaysAndAccuracy[]>({
    id: '64e86c70b512000041007e34',
    data: {},
  })
}
// 击败占比
export function getDefeatRate() {
  return dataInterface<ReciteType.DefeatRate[]>({
    id: '64f0597c6c3a0000be0079b0',
    data: {},
  })
}
// 今日答题
export function getCurrentDaysInfo() {
  return dataInterface<ReciteType.ToDaysInfo[]>({
    id: '64f0364d6c3a0000be0079ae',
    data: {
      '@date': formatDate(new Date()),
    },
  })
}
// 根据组卷id获取list
export function calculationExercise(topicId: string, data: any) {
  return post(`/api/third/exercise/${topicId}`, data)
}

// 新建答题卡
export function createResultCard(data: ReciteType.CreateResultCardType) {
  return createModelData({
    menuId: '66ac4cefb98de275c77bedcc',
    data: JSON.stringify(data),
  })
}

// 提交答题卡,这里是实际的请求
export function submitResultCard(data: ReciteType.CardSubmitData) {
  return createModelData({
    menuId: '6699d432c523aa5a70e0a347',
    data: JSON.stringify(data),
  })
}
// 查询我的收藏
export function getMyCollectList() {
  return dataInterface<ReciteType.CollectListItem[]>({
    id: '64d6074dc80f0000ca003be2',
    data: {},
  })
}
// 查询我的错题
export function getMyWrongList() {
  return dataInterface<ReciteType.CollectListItem[]>({
    id: '64ec0797b512000041007e35',
    data: {},
  })
}
// 查询报告
export function getReportList(id: string) {
  return dataInterface<any[]>({
    id: '6523df6b90780000e5004c72',
    data: {
      '@id': id,
    },
  })
}
// 根据我的收藏ids查询题目
export function getTopicListByIds(ids: any) {
  return getModelList<ReciteType.TopicItem>({
    menuId: '665d7241be6674669fa85159',
    pageSize: -1,
    filter: [
      {
        enCode: '_id',
        method: 'in',
        type: 'custom',
        value: ids,
      },
    ],
    connect: 'and',
  })
}
// 根据我的收藏ids查询题目
export function getReportListByUser(id: string) {
  return getModelList<any>({
    menuId: '66f7c847c434b40a13bf60de',
    pageSize: -1,
    filter: [
      {
        enCode: 'user_id',
        method: 'eq',
        type: 'custom',
        value: id,
      },
    ],
    connect: 'and',
  })
}
// 获取刷题记录
export function getTestHistoryList({
  pageSize = 20,
  currentPage = 1,
  filter = [] as FilterType[],
}) {
  return getModelList<ReciteType.TestHistoryItem[]>({
    menuId: '66ac4cefb98de275c77bedcc',
    pageSize,
    currentPage,
    filter: [
      {
        enCode: 'creatorUserId',
        method: 'eq',
        type: 'systemField',
        value: ['currentUser'],
      },
      ...filter,
    ],
    connect: 'and',
  })
}
// 根据id查询具体刷题记录
export function getTestHistoryDetail(id: string) {
  return getModelList<ReciteType.TestHistoryItem[]>({
    menuId: '66ac4cefb98de275c77bedcc',
    pageSize: -1,
    filter: [
      {
        enCode: 'creatorUserId',
        method: 'eq',
        type: 'systemField',
        value: ['currentUser'],
      },
      {
        enCode: '_id',
        method: 'eq',
        type: 'custom',
        value: [id],
      },
    ],
  })
}
// 根据id查询答题卡
export function getCardItemById(id: string) {
  return getModelList<ReciteType.TopicItem>({
    menuId: '66ac4cefb98de275c77bedcc',
    pageSize: -1,
    filter: [
      {
        enCode: '_id',
        method: 'eq',
        type: 'custom',
        value: [id],
      },
    ],
    connect: 'and',
  })
}

// 获取题目明细
export function getTopicDetail(id: string) {
  return getModelDataDetail<ReciteType.TopicDetail>({
    menuId: '66989272c523aa5a70e0a33b',
    _id: id,
  })
}

// 获取单词学习报告
export function getSignInfo(id: string) {
  return getModelList<ReciteType.SignMissionItem>({
    menuId: '669a1d0ac523aa5a70e0a39d',
    pageSize: -1,
    connect: 'and',
    filter: [
      {
        enCode: 'task_id',
        method: 'eq',
        type: 'custom',
        value: [id],
      },
      {
        enCode: 'creatorUserId',
        method: 'eq',
        type: 'systemField',
        value: ['currentUser'],
      },
    ],
  })
}

// 获取考试时间
export function getExamTime() {
  return getModelList<any>(
    {
      menuId: '669a09abc523aa5a70e0a37b',
      pageSize: -1,
    },
    {
      unAuth: true,
    }
  )
}

// 查询专升本科目
export function getSubject() {
  return getModelList<any>(
    {
      menuId: '66616c4fc68a8307d74047c1',
      pageSize: -1,
      filter: [
        {
          enCode: 'grade',
          method: 'eq',
          type: 'custom',
          value: ['专升本'],
        },
      ],
    },
    {
      unAuth: true,
    }
  )
}
export function getSubjectModule(type: string, subject: string) {
  return getModelList<any>(
    {
      menuId: '67b44f8e66dcb6553bdcddd4',
      pageSize: -1,
      connect: 'and',
      sort: { sort: 'asc' },
      filter: [
        {
          enCode: 'type',
          method: 'in',
          type: 'custom',
          value: [type],
        },
        {
          enCode: 'subject',
          method: 'in',
          type: 'custom',
          value: [subject],
        },
      ],
    },
    {
      unAuth: true,
    }
  )
}

// 查询精选刷题
export function getSelectedExercise({
  currentPage = 1,
  subject,
  module,
}: {
  pageSize?: number
  currentPage?: number
  subject: string
  module: string
}) {
  return getModelList<any>(
    {
      menuId: '67b53e5666dcb6553bdcdf92',
      currentPage,
      pageSize: 10,
      connect: 'and',
      sort: { sort: 'asc' },
      filter: [
        {
          enCode: 'grade',
          method: 'eq',
          type: 'custom',
          value: '专升本',
        },
        {
          enCode: 'subject',
          method: 'eq',
          type: 'custom',
          value: subject,
        },
        {
          enCode: 'module',
          method: 'eq',
          type: 'custom',
          value: module,
        },
      ],
    },
    {
      unAuth: true,
    }
  )
}

// 获取所有科目的错题数
export function getWrongCountBySubject() {
  return dataInterface<ReciteType.WrongCountBySubject[]>({
    id: '68818c7d1246bd901007c441',
    data: {},
  })
}
// 获取用户错题
// @param subject 科目名称
// @param selectTime 时间戳字符串
export function getWrongExercises(subject?: string, selectTime?: string) {
  return dataInterface<ReciteType.WrongExercises>(
    {
      id: '688ac658a2b65d153f08efa1',
      data: {
        '@subject': subject || '',
        '@selectTime': selectTime || '',
      },
    },
    {
      unMessage: true, // 禁用自动错误提示，避免500错误时弹窗
    }
  )
}
