<template>
  <view class="w-750rpx" :style="`height: ${TitleBarHeight}px`"></view>
  <view class="title ml-49rpx">庖丁搜题</view>
  <view class="w750rpx p20rpx box-border">
    <up-search
      v-model="searchValue"
      height="80rpx"
      bg-color="#FFFFFF"
      :show-action="false"
      placeholder="请输入查询内容"
      @search="handleSearch" />
  </view>
  <up-grid :col="5" :border="false">
    <up-grid-item v-for="item in navList" :key="item.text" @click="toServe(item)">
      <view class="flex-center flex-col">
        <image :src="getSystemImg(item.icon)" class="w80rpx h70rpx"></image>
        <view class="mt18rpx text-24rpx text-#111111">{{ item.text }}</view>
      </view>
    </up-grid-item>
  </up-grid>
  <swiper class="swiper" autoplay interval="3000" circular>
    <swiper-item
      v-for="item in swiperList"
      :key="item._id"
      @click="
        toServe({
          path: item.target_fun_params,
        })
      ">
      <image
        class="w710rpx h200rpx"
        mode="scaleToFill"
        :src="assembleImgData(item.cover[0])"></image>
    </swiper-item>
  </swiper>
  <view class="online-course">
    <view class="w-full flex justify-between items-center p30rpx p-y-20rpx box-border">
      <view class="text-36rpx text-#0F2237 font-bold">在线课程</view>
      <view class="text-28rpx text-#30507C" @click="toServe({ path: '/pages/course/course' })"
        >更多></view
      >
    </view>
    <view class="list">
      <up-tabs v-model:current="tabIndex" :list="tabList" :active-style="{ color: '#3c9cff' }" />
      <view class="overflow-auto h-460rpx">
        <LoadMoreList
          ref="loadMoreListRef"
          :request-fn="getPageCourseList"
          :request-params="{ filter }">
          <template #default="{ list }">
            <CourseItem
              v-for="item in (list as ICourseItemResultType[])"
              :key="item._id"
              :title="item.course_name"
              :tags="item.course_tag"
              :cover="item.course_cover?.[0]"
              :price="item.course_price"
              @tap="toDetail(item._id)" />
          </template>
        </LoadMoreList>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts" name="index">
import LoadMoreList from '@/components/LoadMoreList.vue'
import CourseItem from '@/pages/course/components/CourseItem.vue'
import { getPageCourseList } from '@/api/project/course/index'
import type { ICourseItemResultType } from '@/api/project/course/type'
import { assembleImgData, getSystemImg, showToast } from '@/utils'
import { getIndexSwiperDataList } from '@/api/project/index'
import type { IndexSwiper } from '@/api/project/index/type'
import useUserStore from '@/store/modules/user'

const useStore = useUserStore()

// 标题栏高度
const TitleBarHeight = computed(() => {
  const { statusBarHeight } = uni.getSystemInfoSync()
  return statusBarHeight ? statusBarHeight + 44 : 44
})

// 搜索框
const searchValue = ref('')
const handleSearch = () => {
  console.log(searchValue.value)
}

// 导航列表
const navList = [
  {
    icon: '/6747e9ed0a34815816f11159/67ac12ff40e03a0b4b627f88',
    text: '精选好课',
    path: '/pages/course/course',
  },
  {
    icon: '/6747e9ed0a34815816f11159/67ac12ff40e03a0b4b627f8a',
    text: '志愿查询',
    path: '/subPages/volunteer-query/volunteer-query',
  },
  {
    icon: '/6747e9ed0a34815816f11159/67ac12ff40e03a0b4b627f87',
    text: '试题精炼',
    path: '/pages/exercises/exercises',
  },
  {
    icon: '/6747e9ed0a34815816f11159/67ac12ff40e03a0b4b627f89',
    text: 'AI答疑',
    path: '/subPages/ai-chat/chat',
  },
  {
    icon: '/6747e9ed0a34815816f11159/67ac12ff40e03a0b4b627f86',
    text: '我的课程',
    path: '/subPages/course/my-course',
  },
]

const swiperList = ref<IndexSwiper[]>([])

const toServe = (item: any) => {
  if (item.text === '志愿查询') {
    wx.navigateToMiniProgram({
      appId: 'wxde506ba13ee3ec82',
      path: '/pages/index/index',
    })
    return
  }
  if (!item.path) {
    showToast('功能开发中，敬请期待')
    return
  }
  uni.navigateTo({
    url: item.path,
    fail: () => {
      uni.switchTab({
        url: item.path,
      })
    },
  })
}
// 课程tab
const tabList = ref<
  {
    name: string
  }[]
>([
  {
    name: '大学英语',
  },
  {
    name: '高等数学',
  },
  {
    name: '大学语文',
  },
])
const tabIndex = ref(0)
const toDetail = (id: string) => {
  useStore.checkLogin()
  uni.navigateTo({
    url: `/subPages/course/detail?id=${id}`,
  })
}
const loadMoreListRef = ref<InstanceType<typeof LoadMoreList>>()
const filter = ref([
  {
    enCode: 'course_subject',
    method: 'like',
    type: 'custom',
    value: [tabList.value[tabIndex.value].name.substring(2)],
  },
])
watch(tabIndex, () => {
  filter.value[0].value = [tabList.value[tabIndex.value].name.substring(2)]
  loadMoreListRef.value?.refresh()
})

onShow(async () => {
  swiperList.value = (await getIndexSwiperDataList()).data.list
})
</script>

<style>
page {
  background: linear-gradient(180deg, #bbd7fd 0%, #f3f3f3 60%, #f3f3f3 100%);
}
</style>

<style lang="scss" scoped>
.title {
  font-family: '华文行楷';
  font-weight: bold;
  font-size: 60rpx;
  color: #07206a;
  text-align: left;
  line-height: 58rpx;
  text-transform: none;
}
.swiper {
  width: 710rpx;
  height: 200rpx;
  margin: 20rpx auto;
}
.online-course {
  margin: 20rpx auto;
  width: 710rpx;
  height: 682rpx;
  background: linear-gradient(154deg, rgba(157, 206, 255, 0.8) 0%, rgba(191, 228, 252, 0.5) 100%);
  border-radius: 30rpx 30rpx 30rpx 30rpx;
  .list {
    margin: auto;
    width: 670rpx;
    height: 570rpx;
    background: #ffffff;
    border-radius: 30rpx 30rpx 30rpx 30rpx;
  }
}
</style>
