<template>
  <view class="feedback-detail">
    <view class="bg-white p-20rpx">
      <view class="text-xl">{{ feedbackData?.f_title }}</view>
      <!-- 问题区S -->
      <view class="mt-20rpx flex items-center">
        <u-avatar size="94rpx"></u-avatar>
        <view class="flex-col ml-20rpx flex justify-between flex-col h-94rpx">
          <view class="text-base">{{ name }}</view>
          <u-text type="info" size="24rpx" :text="formatDate(feedbackData?.creatorTime)" />
        </view>
      </view>
      <view class="mt-20rpx detail">
        <view> {{ feedbackData?.f_detaile }}</view>
        <view class="image-container mt-2">
          <image
            v-for="(item, index) in feedbackData?.f_pic || []"
            :key="index"
            class="w-full h-full"
            :src="assembleImgData(item)"
            mode="aspectFill"
            @click="previewImage(index, feedbackData!.f_pic)" />
        </view>
      </view>

      <!-- 问题区E -->

      <!-- 回复区S -->
      <view v-if="feedbackData?.f_stauts === '已回复'" class="mt-20rpx flex items-center">
        <u-avatar
          size="94rpx"
          src="https://kindoucloud.com/api/file/previewImage/6613669f955ed0316d6434c9/667d1d990bb07d7cd6ed3097">
        </u-avatar>
        <view class="flex-col ml-20rpx flex justify-between flex-col h-94rpx">
          <view class="flex items-center">
            <view class="text-base">{{ feedbackData?.lastModifyUserId?.fullName }}</view>
            <view class="tag">管理员</view>
          </view>
          <u-text
            type="info"
            size="24rpx"
            :text="`编辑于 ${formatDate(feedbackData?.lastModifyTime)}`" />
        </view>
      </view>
      <view class="mt-20rpx detail">
        <view> {{ feedbackData?.f_news }}</view>
        <view class="image-container mt-2">
          <image
            v-for="(item, index) in feedbackData?.f_bpic || []"
            :key="index"
            class="w-full h-full"
            :src="assembleImgData(item)"
            mode="aspectFill"
            @click="previewImage(index, feedbackData!.f_bpic)" />
        </view>
      </view>
      <!-- 回复区S -->
    </view>
  </view>
</template>

<script setup lang="ts" name="feedback-detail">
import useDateFormatter from '@/hooks/useDateFormatter'
import { getFeedbackDetail } from '@/api/project/feedback/index'
import { assembleImgData, showFailToast } from '@/utils'
import type { FeedBackItemType } from '@/api/project/feedback/type'

const feedbackData = ref<FeedBackItemType>()
const { formatDate } = useDateFormatter('YYYY年MM月DD日 HH时mm分')
const name = computed(() => {
  return feedbackData.value?.f_enable_name === 0
    ? feedbackData.value.creatorUserId.fullName
    : '匿名用户'
})

onLoad(async (e: any) => {
  if (!e.id) {
    showFailToast('参数错误')
    return
  }

  const { data } = await getFeedbackDetail(e.id)
  feedbackData.value = data
})

function previewImage(index: number, images: FeedBackItemType['f_pic']) {
  uni.previewImage({
    current: index,
    urls: images.map(item => assembleImgData(item)),
  })
}
</script>

<style lang="scss" scoped>
.feedback-detail {
  padding: 20rpx;
  min-height: 100vh;
  background-color: #f5f7fb;
  .detail {
    border-bottom: 1px solid #eee;
    padding-bottom: 20rpx;
  }
  .tag {
    background: #6377f5;
    border-radius: 4rpx;
    color: #ffffff;
    font-size: 24rpx;
    margin-left: 10rpx;
    padding: 0rpx 5rpx;
  }
  .image-container {
    width: 100%;
    display: grid;

    grid-template-columns: repeat(3, 1fr);
    grid-gap: 10rpx;
    image {
      height: 180rpx;
      width: 100%;
      border-radius: 8rpx;
      border: 1px solid #eee;
    }
  }
}
</style>
