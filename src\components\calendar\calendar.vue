<template>
  <view class="calendar date-box" :class="[customClass]">
    <view class="box-list" :style="{ 'margin-bottom': list.length > 0 ? '20rpx' : '0' }">
      <view class="date-top">
        <view class="icon" :class="{ disabled: isLastYear }" @click="LastYear">
          <view class="icon-arrow-left-double"></view>
        </view>
        <view class="content-text">
          <view class="icon" :class="{ disabled: isLastYear && isLastMonth }" @click="LastMonth">
            <view class="icon-arrow-left"></view>
          </view>
          <text class="month" @click="handleShowPicker"> {{ year }}年{{ month }}月 </text>
          <view class="icon" :class="{ disabled: isNextYear && isNextMonth }" @click="NextMonth">
            <view class="icon-arrow-right"></view>
          </view>
        </view>
        <view class="icon" :class="{ disabled: isNextYear }" @click="NextYear">
          <view class="icon-arrow-right-double"></view>
        </view>
      </view>
      <view class="date-wrap">
        <view class="date-week">
          <view v-for="item in weekList" :key="item" class="week-item">
            <text>{{ item }}</text>
          </view>
        </view>
        <view v-if="dayList.length > 0" class="day-content">
          <view v-if="!isOpen" class="day-month">
            <text>{{ month }}</text>
          </view>
          <view
            v-for="(item, index) in dayList"
            :key="index"
            class="day-item"
            :data-index="index"
            :class="[computedClass(item, index)]"
            :style="{ zIndex: dayList.length - index }"
            @click="toActive(item, index)">
            <!-- 调整zIndex,以免相连时伪类元素遮住父元素的上一个兄弟元素 -->
            {{ item.day || '' }}
          </view>
        </view>
      </view>
      <view v-if="isShrink" style="width: 100%">
        <view class="toggle" :class="{ open: !isOpen }" @click="shrinkToggle">
          <view class="icon-btn"></view>
        </view>
      </view>
      <slot name="foot"></slot>
    </view>
    <u-datetime-picker
      v-model="timestamp"
      :show="showPicker"
      :min-date="minTime"
      :max-date="maxTime"
      mode="year-month"
      @cancel="handleClosePicker"
      @confirm="handlePickerConfirm" />
  </view>
</template>

<script>
import language from './language'

function isLeapYear(year) {
  return (year % 4 === 0 && year % 100 !== 0) || year % 400 === 0
}
function getNowTimeDate(timestamp, changeDay = true) {
  const date = timestamp ? new Date(timestamp) : new Date()
  const year = date.getFullYear()
  const month = parseInt(date.getMonth() + 1)
  const params = { year, month }
  if (changeDay) params.day = date.getDate()
  return params
}
function format(time) {
  return String(time).padStart(2, '0')
}
function getDate(that) {
  return {
    year: that.year,
    month: that.month,
    day: that.day,
  }
}
function getTime(that) {
  return [that.year, format(that.month), format(that.day)].join('-')
}
function getTimeStamp(date) {
  return new Date(String(`${date} 00:00:00`)).getTime() // 转换时间戳
}

export default {
  name: 'Calendar',
  options: { virtualHost: true, styleIsolation: 'shared' },
  emits:['input','change','active'],
  props: {
    value: {
      type: Array,
      default: () => [],
    },
    list: {
      type: Array,
      default: () => {
        return []
      },
    },
    language: {
      type: String,
      default: 'en',
    },
    extraData: {
      type: Array,
      default: () => [],
    },
    isShrink: {
      type: Boolean,
      default: false,
    },
    isUnfold: {
      type: Boolean,
      default: false,
    },
    multiple: {
      type: Boolean,
      default: false,
    },
    minTime: {
      type: Number,
      default: getTimeStamp(`${new Date().getFullYear() - 2}-01-01`),
    },
    maxTime: {
      type: Number,
      default: getTimeStamp(`${new Date().getFullYear() + 2}-01-01`),
    },
    readMode: Boolean,
    render: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    const time = getNowTimeDate()
    this.activeDate = time
    return {
      dayList: [],
      zeroTime: new Date().setHours(0, 0, 0, 0),
      timestamp: Date.now(),
      selectTime: [],
      isOpen: false,
      showPicker: false,
      ...time,
    }
  },
  computed: {
    weekList() {
      return language[this.language] || []
    },
    minDate() {
      return getNowTimeDate(this.minTime)
    },
    maxDate() {
      return getNowTimeDate(this.maxTime)
    },
    isLastYear() {
      return this.year <= this.minDate.year
    },
    isLastMonth() {
      return this.month <= this.minDate.month
    },
    isNextMonth() {
      return this.month >= this.maxDate.month
    },
    isNextYear() {
      return this.year >= this.maxDate.year
    },
  },
  watch: {
    extraData() {
      this.initDate()
    },
    value: {
      immediate: true,
      deep: true,
      handler(list) {
        if (list === false) {
          this.selectTime = this.multiple ? [] : [this.zeroTime]
        } else {
          this.selectTime = list
        }
      },
    },
    render: {
      immediate: true,
      handler() {
        this.initDate()
      },
    },
    selectTime: {
      deep: true,
      handler(list) {
        this.$emit('input', list)
      },
    },
  },
  created() {
    this.isOpen = this.isUnfold
  },
  methods: {
    computedClass(item, index) {
      const { dot, status } = item.data || {}
      let className = dot ? 'dot ' : '' // 计算点
      if ((index + 1) % 7 === 1) className += 'week-sun ' // 星期日是视图第一天,不做相连效果
      if (item.date === this.zeroTime) className += 'current ' // 计算当前
      if (status) className += `state-${status} ` // 配置状态
      if (item.active) {
        this.currentActiveDayIndex = index
        className += 'active ' // 是否激活
      }
      if (item.disabled) className += 'disabled' // 是否属于其他月份

      return className
    },
    handleShowPicker() {
      this.showPicker = true
    },
    handleClosePicker() {
      this.showPicker = false
    },
    handlePickerConfirm({ value } = {}) {
      this.handleClosePicker()
      const time = getNowTimeDate(value, false)
      Object.assign(this, time) // 同步选中时间
      this.initDate() // 重新渲染
      this.$emit('change', time)
    },
    toShrink() {
      const currentIndex = this.dayList.findIndex(o => !o.disabled && o.day === this.day)
      const groupNum = 7
      const diffNum = (currentIndex + 1) % groupNum // 当前处于星期几
      const nextSum = diffNum ? groupNum - diffNum : 0 // 后面天数
      const prevSum = (diffNum || groupNum) - 1 // 前面天数
      const next = Array.from({ length: nextSum }, (_, i) => currentIndex + i + 1)
      const prev = Array.from({ length: prevSum }, (_, i) => currentIndex - i - 1).reverse()
      const diffList = [...prev, currentIndex, ...next].map(index => this.dayList[index]) // 合并天数
      this.dayList = diffList
      this.isOpen = true
    },
    toShrinkClose() {
      this.dayList = this.createDayList(this.year, this.month)
      this.isOpen = false
    },
    shrinkToggle() {
      this.isOpen ? this.toShrinkClose() : this.toShrink()
    },
    setSelectTime(item) {
      const { timestamp } = item
      if (this.multiple) {
        // 多选操作
        const index = this.selectTime.findIndex(t => t === timestamp)
        const state = index === -1
        if (state) {
          this.selectTime.push(timestamp)
        } else {
          this.selectTime.splice(index, 1)
        }
        item.active = state
      } else {
        // 单选切换
        const [cTime] = this.selectTime
        if (cTime === timestamp) return
        this.selectTime[0] = timestamp
        if (this.currentActiveDayIndex) this.dayList[this.currentActiveDayIndex].active = false
        item.active = true
      }
    },
    initDate(year, month) {
      if (!this.render) return
      this.dayList = this.createDayList(year || this.year, month || this.month)
      if (this.isShrink && this.isOpen) this.toShrink()
    },
    createDayList(_year, _month) {
      const list = []
      const counts = Array.from({ length: this.getDayNum(_year, _month) }, (_, i) => i + 1)
      const extraData = JSON.parse(JSON.stringify(this.extraData))
      this.calculateRemainingDays(_year, _month, counts).forEach(
        ([days, month, year], countIndex) => {
          let callback = () => (this.readMode ? countIndex !== 1 : countIndex === 0) // 阅读模式只有单月可选/上一个月隐藏
          if (countIndex === 1) {
            callback = t => t > this.maxTime || t < this.minTime
          }
          // 数据状态处理
          days.forEach(day => {
            const params = { year, month, day }
            const timestamp = getTimeStamp(getTime(params))
            const disabled = callback(timestamp)
            const add = (data = {}) => {
              const active = this.selectTime.includes(timestamp)
              list.push({ ...params, data, timestamp, disabled, active })
            }
            let index = 0
            for (const item of extraData) {
              if (item.timestamp === timestamp) {
                extraData.splice(index, 1)
                return add(item)
              }
              index++
            }
            add({})
          })
        }
      )
      if (this.setOneDay) {
        list.find(o => o.day === 1).active = true
      }
      return list
    },
    getDayNum(year, month) {
      const dayNum = [31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31]
      if (month === 2 && isLeapYear(year)) return 29
      return dayNum[month - 1]
    },
    calculateRemainingDays(year, month, current) {
      // month:当前月份(1开始)
      // 计算当前月份总天数(包含上月和下月剩余天数)
      const diffMonth = month - 1

      // 计算当前月份的第一天和最后一天
      const firstDayOfCurrentMonth = new Date(year, diffMonth, 1)
      const lastDayOfCurrentMonth = new Date(year, month, 0)

      // 上个月总天数
      const prevMonth = diffMonth || 12
      const prevYear = diffMonth ? year : year - 1
      const totalDaysOfPrevMonth = this.getDayNum(prevYear, prevMonth)

      const prev = []
      // 追加上个月的剩余天数
      for (let i = firstDayOfCurrentMonth.getDay() - 1; i >= 0; i--) {
        prev.push(totalDaysOfPrevMonth - i)
      }
      const next = []
      // 追加下个月的剩余天数
      for (let i = 1; i <= 6 - lastDayOfCurrentMonth.getDay(); i++) {
        next.push(i)
      }

      const isNext = month >= 12
      const nextMonth = isNext ? 1 : month + 1
      const nextYear = isNext ? year + 1 : year

      return [
        [prev, prevMonth, prevYear],
        [current, month, year],
        [next, nextMonth, nextYear],
      ]
    },
    toActive(item, index) {
      // 点击切换激活
      this.day = item.day
      this.activeDate = getDate(item)
      this.setSelectTime(item)
      this.$emit('active', {
        ...this.activeDate,
        selectTime: this.selectTime,
        timestamp: item.timestamp,
        index,
      })
    },
    LastMonth() {
      if (this.month > 1) {
        this.month = this.month - 1
      } else {
        this.LastYear(false)
        this.month = 12
      }
      this.emitChange()
      this.initDate()
    },
    NextMonth() {
      if (this.month < 12) {
        this.month = this.month + 1
      } else {
        this.NextYear(false)
        this.month = 1
      }
      this.emitChange()
      this.initDate()
    },
    LastYear(flag = true) {
      this.year = this.year - 1
      if (this.isLastMonth) this.month = this.minDate.month
      // 年份切换时可能会超出选定时间,需要归位
      this.emitChange()
      if (flag) this.initDate()
    },
    NextYear(flag = true) {
      this.year = this.year + 1
      if (this.isNextMonth) this.month = this.maxDate.month
      this.emitChange()
      if (flag) this.initDate()
    },
    emitChange() {
      try {
        if (this.multiple) return
        if (['year', 'month'].every(k => this.activeDate[k] === this[k])) {
          this.setOneDay = false
        } else {
          this.setOneDay = true
        }
      } finally {
        // 如果切换到当前年月份时回复上一次点击,否则切换为第一天
        this.$emit('change', getDate(this))
      }
    },
    updateCurrentActiveDayStatus(status) {
      const { currentActiveDayIndex: index } = this
      this.dayList.forEach(item => {
        const data = this.extraData.find(ite => ite.timestamp === item.timestamp)
        if (data) {
          item.status = data.status
        }
        if (item.day === new Date().getDate()) {
          item.active = true
        }
      })
      // if (index) this.dayList[index].status = status || ''
    },
  },
}
</script>

<style lang="scss" scoped>
@import './iconfont.css';
.date-box {
  display: flex;
  flex-direction: column;
  background-color: white;
  border-radius: 20rpx;
  color: #333;
  .date-top {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12rpx 20rpx 24rpx;
    border-bottom: 1rpx solid #f3f4f6;
    .icon {
      width: 50rpx;
      height: 50rpx;
      line-height: 50rpx;
      &.disabled {
        opacity: 0.5;
        pointer-events: none;
      }
    }
    .content-text {
      font-size: 32rpx;
      font-weight: bold;
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: space-between;
      .month {
        padding: 0 40rpx;
      }
    }
  }
  .date-wrap {
    padding: 16rpx 20rpx;
    .date-week {
      display: flex;
      padding: 20rpx 0 30rpx;
      .week-item {
        display: flex;
        justify-content: center;
        align-items: center;
        flex: 1;
        height: 52rpx;
        font-size: 26rpx;
        color: #909193;
      }
    }
    .day-content {
      $h: 60rpx;
      position: relative;
      display: grid;
      gap: 20rpx;
      grid-template-columns: repeat(7, $h);
      justify-content: space-evenly;
      .day-item {
        position: relative;
        width: 100%;
        height: $h;
        line-height: $h;
        text-align: center;
        font-size: 24rpx;
        border-radius: 50%;
        background-color: var(--status-color);
        &.dot::after {
          position: absolute;
          content: '.';
          left: 0;
          right: 0;
          margin: auto;
          bottom: -8rpx;
          color: inherit;
          font-weight: bold;
          font-size: 40rpx;
        }
        &.disabled {
          pointer-events: none;
          opacity: 0.5;
        }
        &[class*='state-'] {
          color: #fff;
          &.active {
            box-shadow: inset 0 0 0 4rpx $u-theme-color;
          }
        }
        &:not([class*='state-']) {
          &.current:not(.disabled) {
            box-shadow: 0 0 0 2rpx $u-theme-color, inset 0 0 0 2rpx $u-theme-color;
            color: $u-theme-color;
          }
          &.active {
            color: #fff !important;
            background-color: $u-theme-color;
          }
        }
      }
      .day-month {
        position: absolute;
        top: 0;
        bottom: 0;
        left: 0;
        right: 0;
        display: flex;
        justify-content: center;
        align-items: center;
        width: 100%;
        height: 100%;
        font-size: 168px;
        font-weight: 700;
        color: #999;
        opacity: 0.1;
        pointer-events: none;
      }
    }
  }

  .toggle {
    position: relative;
    padding: 10rpx 0;
    display: flex;
    justify-content: center;
    > .icon-btn:before {
      content: '\e672';
    }
    &:not(.open) > .icon-btn:before {
      content: '\eb0b';
    }
    &:before {
      width: calc(50% - 30rpx);
      border-top: solid 1px #eaeaea;
      content: '';
      display: block;
      position: absolute;
      top: 50%;
      left: 0;
      transform: translateY(-50%);
    }
    &:after {
      width: calc(50% - 30rpx);
      border-top: solid 1px #eaeaea;
      content: '';
      display: block;
      position: absolute;
      top: 50%;
      right: 0;
      transform: translateY(-50%);
    }
  }
}
</style>
