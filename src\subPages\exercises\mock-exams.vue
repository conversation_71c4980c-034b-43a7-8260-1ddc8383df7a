<template>
  <view>
    <ExamList title="模拟考试" :request-fn="getMockExamList" @go-test="gotoTest"></ExamList>
  </view>
</template>

<script setup lang="ts" name="mock-exams">
import ExamList from './components/ExamList.vue'
import { getMockExamList } from '@/api/project/exercises/index'
import type { PaperItem } from '@/api/project/exercises/type'
// 跳转
function gotoTest(exam: PaperItem) {
  uni.navigateTo({
    url: `/subPages/exercises/answer-sheet?type=exam&examId=${exam.testPaperId}&duration=${exam.duration}&title=${exam.name}&answerType=模拟考试`,
  })
}
</script>

<style lang="scss" scoped></style>
