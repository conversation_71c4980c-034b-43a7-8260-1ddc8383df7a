<template>
  <view class="flex justify-between bg-white p20rpx box-border rounded-10rpx">
    <view class="flex items-start">
      <view
        class="w30rpx h30rpx rounded-full mt6rpx b-#459af7 b-2px b-solid flex flex-center"
        :class="getBg"
        style="background-color: white">
        <view class="w16rpx h16rpx rounded-full bg-#459af7" :class="getBg"></view>
      </view>
      <view class="flex flex-col ml20rpx">
        <view class="text-28rpx text-#333333 fw-500">{{ title || '早晨英语记忆单词500个' }}</view>
        <view class="text-22rpx text-#999999 fw-500 mt15rpx"
          >今天 &nbsp;{{ `${formatDate(time[0])}~${formatDate(time[1])}` }}
        </view>
      </view>
    </view>
    <view
      class="w160rpx h62rpx bg-#459af7 rounded-37rpx text-center lh-60rpx text-white text-28rpx"
      @click="toServe"
      >{{ status === 0 ? '补卡' : '查看' }}</view
    >
  </view>
</template>

<script setup lang="ts" name="clock-in-completeitem">
import useDateFormatter from '@/hooks/useDateFormatter'
import useUserStore from '@/store/modules/user'
import {
  getClockInRecordsDataList,
  getDistributeTaskSituationData,
  updateDistributeTaskSituationData,
} from '@/api/project/clock-in'
import { getSignInfo, getTestHistoryDetail } from '@/api/project/exercises'
import type { IDistributeTask } from '@/api/project/clock-in/type'
import { formatHours, showToast } from '@/utils'

const props = defineProps<{
  completeId?: string
  sectionId?: string
  topicId?: string
  catalogId?: string
  taskId: string
  wordId?: string
  reportId?: string
  itype: '拍照打卡' | '在线学习' | '在线刷题' | '专项打卡' | '单词打卡'
  title: string
  time: number[]
  date: number
  status: number
}>()

const userStore = useUserStore()

const getBg = computed(() => {
  if (props.status === 1) {
    return 'bg1'
  }
  if (
    new Date().getTime() > props.date &&
    new Date(props.date).getDate() !== new Date().getDate()
  ) {
    return '.bg3'
  } else {
    return '.bg2'
  }
})

const { formatDate } = useDateFormatter('HH:mm')

const toServe = async () => {
  if (!props.status) {
    if (props.itype === '拍照打卡') {
      uni.navigateTo({
        url: `/subPages/clock-in/clock-in?taskId=${props.taskId}&content=${props.title}`,
      })
    }
    if (props.itype === '在线学习' || props.itype === '在线刷题') {
      // 刷题or学习的打卡完成回调
      uni.$once('clockIn', async (times: any) => {
        const list = (await getDistributeTaskSituationData(props.taskId)).data.list
        const data: IDistributeTask = {
          sum_date: formatHours(times),
          user_id: userStore.userInfo!.id,
          user_name: userStore.userInfo!.realName,
          task_id: props.taskId,
          task_name: props.title,
          task_date: '',
          is_commit: props.itype === '在线刷题' ? '是' : times >= 60 ? '是' : '否',
        }
        if (list.length > 0) {
          data._id = list[0]._id
        }
        updateDistributeTaskSituationData(data).then(res => {
          showToast('打卡成功')
        })
      })
    }
    if (props.itype === '在线学习') {
      uni.navigateTo({
        url: `/subPages/course/detail?id=${props.sectionId}`,
      })
    }
    if (props.itype === '在线刷题') {
      uni.navigateTo({
        url: `/subPages/exercises/answer-sheet?type=test&examId=${props.topicId}&title=打卡刷题&answerType=打卡任务`,
      })
    }
    if (props.itype === '专项打卡') {
      // 专项打卡的打卡完成回调
      uni.$once('special', async () => {
        const list = (await getDistributeTaskSituationData(props.taskId)).data.list
        const data: IDistributeTask = {
          sum_date: formatHours(0),
          user_id: userStore.userInfo!.id,
          user_name: userStore.userInfo!.realName,
          task_id: props.taskId,
          task_name: props.title,
          task_date: '',
          is_commit: '是',
        }
        if (list.length > 0) {
          data._id = list[0]._id
        }
        updateDistributeTaskSituationData(data).then(res => {
          showToast('打卡成功')
        })
      })
      uni.navigateTo({
        url: `/subPages/special-marking/special1?topicId=${props.topicId}`,
      })
    }
    if (props.itype === '单词打卡') {
      uni.navigateTo({
        url: `/subPages/recite-words/word-clock?wordId=${props.wordId}&taskId=${props.taskId}&title=${props.title}&book=${props.topicId}`,
      })
    }
  } else {
    if (props.itype === '单词打卡') {
      const reportId = (await getSignInfo(props.taskId)).data.list[0].report_id
      uni.navigateTo({
        url: `/subPages/recite-words/word-report?reportId=${reportId}`,
      })
    }
    if (props.itype === '在线学习') {
      uni.navigateTo({
        url: `/subPages/course/detail?id=${props.sectionId}`,
      })
    }
    if (props.itype === '在线刷题') {
      getTestHistoryDetail(props.completeId as string).then(e => {
        uni.navigateTo({
          url: `/subPages/exercises/test-report?reportId=${encodeURIComponent(
            JSON.stringify(e.data.list[0])
          )}`,
        })
      })
    }
    if (props.itype === '拍照打卡') {
      getClockInRecordsDataList(props.taskId).then(res => {
        uni.navigateTo({
          url: `/subPages/clock-in/clock-in?content=${JSON.stringify(res.data.list[0])}`,
        })
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.bg1 {
  background-color: #0ccc8c;
  border-color: #0ccc8c;
}
.bg2 {
  background-color: #ffa600;
  border-color: #ffa600;
}
.bg3 {
  background-color: #ff3131;
  border-color: #ff3131;
}
</style>
