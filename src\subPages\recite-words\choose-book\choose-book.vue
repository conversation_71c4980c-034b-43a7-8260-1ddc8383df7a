<template>
  <view class="words-pages choose-book">
    <u-skeleton v-if="loading" rows="10" title loading></u-skeleton>
    <view v-else>
      <view class="words-tabs">
        <u-sticky bg-color="#fff">
          <view class="flex items-center">
            <view>
              <!-- 固定tabs：在学、我的 -->
              <ReciteTabs
                :list="fixedTabs"
                :current="fixedTabValue"
                :is-cancelled="isFixedTabCancelled"
                @change="changeFixedTabs" />
            </view>
            <view
              class="h-40 b-r-solid b-r-#707070 b-r-2rpx relative top-2rpx ml29rpx mr23rpx"></view>
            <!-- 动态tabs -->
            <view class="w486">
              <ReciteTabs
                v-if="dynamicTabs.length > 0"
                :list="dynamicTabs"
                :current="dynamicTabValue"
                :is-cancelled="isDynamicTabCancelled"
                @change="changeDynamicTabs" />
            </view>
          </view>
          <view v-if="pane?.tags.length > 0" class="tabs-pane flex flex-wrap">
            <view
              v-for="tag in pane.tags"
              :key="tag"
              class="tag"
              :class="[{ 'tag--active': isActive(tag) }]"
              @tap="changeTag(pane.name, tag)">
              {{ tag }}
            </view>
          </view>
        </u-sticky>
      </view>
      <view class="book-list">
        <view v-for="item in list" :key="item._id">
          <!-- 我的词书 - 使用 PrivateBookItem -->
          <PrivateBookItem
            v-if="fixedTabValue === 1 && !isFixedTabCancelled"
            :book-info="item"
            :is-selected="isBookStudying(item._id)"
            :word-count="getBookWordCount(item._id)"
            :progress="getBookProgress(item._id)"
            @click="onBookClick"
            @action="showBookActions" />

          <!-- 其他词书 - 使用 PublicBookItem -->
          <PublicBookItem
            v-else
            :book-info="item"
            :is-selected="isBookStudying(item._id)"
            :progress="getBookProgress(item._id)"
            @click="onBookClick"
            @action="showBookActions" />
        </view>
        <view v-if="!list.length" class="flex-center flex-1">
          <u-empty mode="list" text="暂无词书"> </u-empty>
        </view>
      </view>
    </view>
    <view
      class="flex w100rpx h100rpx items-center justify-center flex-col fixed bottom-190rpx right-0"
      @tap="showImportSheet = true">
      <image
        src="https://beta.kindoucloud.com/api/file/previewImage/6747e9ed0a34815816f11159/67adb76d40e03a0b4b627fbc"
        mode="widthFix"
        class="w-67rpx h-67rpx"></image>
      <view class="text-24rpx color-#459AF7"> 导入 </view>
    </view>
    <!-- 导入操作弹窗 -->
    <u-popup :show="showImportSheet" round="40rpx" @close="showImportSheet = false">
      <view class="text-center text-32rpx mt29rpx relative"
        >导入
        <view
          class="absolute top-5rpx left-40rpx text-24rpx color-#459AF7"
          @tap="showAddBook = true"
          >添加词书</view
        >
        <view class="absolute top-5rpx right-40rpx text-24rpx color-#459AF7" @tap="goTutorial"
          >导入教程</view
        >
      </view>
      <view class="p-40rpx">
        <view
          class="mb-20rpx text-30rpx border-1rpx border-solid border-#D4D4D4 rounded-14rpx p-20rpx"
          @tap="chooseTemplate">
          Excel 模版下载
        </view>
        <view
          class="mb-20rpx text-30rpx border-1rpx border-solid border-#D4D4D4 rounded-14rpx p-20rpx"
          @tap="chooseBookAndImport">
          Excel 导入
          <view class="text-24rpx color-#999999 mt8rpx">
            表格中每行是一个单词，需全部单词自带释义
          </view>
        </view>
        <view
          class="text-30rpx border-1rpx border-solid border-#D4D4D4 rounded-14rpx p-20rpx"
          @tap="importByText">
          文本导入
          <view class="text-24rpx color-#999999 mt8rpx">
            每行文本作为一个单词，系统自动匹配释义
          </view>
        </view>
      </view>
    </u-popup>
    <!-- 导入词书选项 -->
    <up-picker
      ref="bookPickerRef"
      :show="showChooseBook"
      :columns="[booksCulumn]"
      toolbar-right-slot
      @cancel="showChooseBook = false">
      <template #toolbar-right>
        <view class="text-#51a0f7" style="padding-right: 10px" @tap="confirmChooseBook"
          >导入该词书</view
        >
      </template>
    </up-picker>
    <!-- 导入模板选项 -->
    <u-action-sheet
      :actions="templateList"
      safe-area-inset-bottom
      cancel-text="取消"
      :show="showTemplate"
      @close="showTemplate = false"
      @select="onTemplateSelect">
    </u-action-sheet>
    <u-popup :show="showAddBook" round="20rpx" mode="center" @close="showAddBook = false">
      <view class="p40rpx p-y-0 box-border">
        <view class="text-center text-32rpx mt29rpx relative p20rpx pt-0 box-border"
          >添加词书
        </view>
        <view class="text-28rpx color-#999999 mt8rpx flex items-center">
          书 名：
          <input
            v-model="bookName"
            type="text"
            class="ml-20rpx pl-10rpx text-24rpx color-#999999 mt8rpx border-1rpx border-solid border-#D4D4D4 rounded-14rpx p-10rpx" />
        </view>
        <view class="mt30rpx flex flex-gap-20rpx justify-between">
          <u-button
            radius="25rpx"
            type="info"
            size="normal"
            shape="circle"
            plain
            @tap="showAddBook = false">
            取消
          </u-button>
          <u-button radius="25rpx" type="primary" size="normal" shape="circle" @tap="addBook">
            添加
          </u-button>
        </view>
      </view>
    </u-popup>

    <!-- 词书操作弹窗 -->
    <u-popup
      :show="showActionPopup"
      mode="bottom"
      :round="32"
      :safe-area-inset-bottom="true"
      @close="hideActionPopup">
      <view class="action-popup">
        <view
          v-for="(item, index) in actionMenuItems"
          :key="item.key"
          class="action-item"
          :class="[item.type, { cancel: item.type === 'cancel' }]"
          @tap="handleActionClick(item)">
          <text class="action-text" :class="[{ 'danger-text': item.type === 'danger' }]">
            {{ item.title }}
          </text>
        </view>
      </view>
    </u-popup>
  </view>
</template>

<script setup lang="ts" name="choose-book">
/* ------------------------ 导入依赖 ------------------------ */
import ReciteTabs from './components/ReciteTabs.vue'
import PublicBookItem from './components/PublicBookItem.vue'
import PrivateBookItem from './components/PrivateBookItem.vue'
import {
  addOwnerBook,
  batchAddWord,
  getAllReciteList,
  getImportTemplate,
  getLearnedReciteWordCount,
  getMyBookList,
  getReciteList,
  getReciteListById,
  getRecitePlan,
  getReciteTags,
  getReciteWordCount,
  getUserReciteBook,
  getReciteWordCountByBook,
  deleteBook,
  deleteRecitePlan,
  deleteReciteWordRecords,
  deleteBookWords,
  getBookWordIds,
} from '@/api/project/recite-words'
import type { BookInfoType, RecitePlanResultType } from '@/api/project/recite-words/type'
import {
  hideLoading,
  previewExcelFile,
  showLoading,
  showToast,
  showSuccessToast,
  showFailToast,
} from '@/utils'
import useUserStore from '@/store/modules/user'

/* ------------------------ 基础状态 ------------------------ */
// 是否加载中
const loading = ref(true)

// 当前选中标签
const currentTag = ref('')
// 词书列表
const list = ref<BookInfoType[]>([])
// 用户词书Id
const userReciteId = ref('')
// 自动跳转
const autoJump = ref(false)
// 用户计划表
const recitePlanList = ref<RecitePlanResultType[]>([])

// 词书统计信息
const bookStats = ref<Record<string, { totalCount: number; learnedCount: number }>>({})

// 判断某本书是否在学习中（当前选中的词书）
function isBookStudying(bookId: string) {
  // 根据学习计划判断是否在学习中
  // return recitePlanList.value.some(plan => plan.book === bookId)

  // 判断是否为当前选中的词书（从 useUserStore 获取）
  const userStore = useUserStore()
  return userStore.bookInfo?._id === bookId
}

// 获取词书单词数量显示
function getBookWordCount(bookId: string) {
  const stats = bookStats.value[bookId]
  if (!stats) return ''
  return stats.totalCount > 0 ? `${stats.totalCount}` : ''
}

// 获取词书学习进度
function getBookProgress(bookId: string) {
  const stats = bookStats.value[bookId]
  if (!stats || stats.totalCount === 0) return 0
  return Math.round((stats.learnedCount / stats.totalCount) * 100)
}

// 加载词书统计信息
async function loadBookStats(bookIds: string[]) {
  for (const bookId of bookIds) {
    try {
      // 获取总单词数
      const totalRes = await getReciteWordCount(bookId)
      const totalCount = totalRes.data?.[0]?.totalCount || 0

      // 获取已学单词数
      const learnedRes = await getLearnedReciteWordCount(bookId)
      const learnedCount = learnedRes.data?.[0]?.totalCount || 0

      bookStats.value[bookId] = {
        totalCount,
        learnedCount,
      }
    } catch (error) {
      console.error(`获取词书 ${bookId} 统计信息失败:`, error)
      bookStats.value[bookId] = { totalCount: 0, learnedCount: 0 }
    }
  }
}

/* ------------------------ 弹窗状态 ------------------------ */
// 导入弹窗
const showImportSheet = ref(false)
// 词书操作弹窗
const showActionPopup = ref(false)
const currentBook = ref<BookInfoType | null>(null)

/* ------------------------ Tab组件相关 ------------------------ */
// tab样式控制
const isFixedTabCancelled = ref(false)
const isDynamicTabCancelled = ref(false)

// 固定tabs配置
const fixedTabs = ref([
  { name: '在学', tags: [] as string[] },
  { name: '我的', tags: [] as string[] },
])
const fixedTabValue = ref(0)

// 动态tabs配置（从后端数据动态生成）
const dynamicTabs = ref<Array<{ name: string; tags: string[] }>>([])
const dynamicTabValue = ref(0)

// 当前标签
const pane = computed(() => {
  // 根据取消状态判断当前激活的tabs组
  if (!isFixedTabCancelled.value) {
    // 固定tabs激活
    return fixedTabs.value[fixedTabValue.value]
  } else {
    // 动态tabs激活
    return dynamicTabs.value[dynamicTabValue.value]
  }
})

/* ------------------------ 导入相关状态 ------------------------ */
// 选择词书
const chooseBook = ref<BookInfoType>()
// 我的词数列表
const myBookList = ref<BookInfoType[]>([])
// 选择词书弹窗
const showChooseBook = ref(false)
// 选择词书弹窗
const bookPickerRef = ref()
// 遮罩类型
const templateList = ref<any[]>([])
// 模板选择
const showTemplate = ref(false)
// 添加词书弹窗
const showAddBook = ref(false)
// 书名
const bookName = ref('')

/* ------------------------ 生命周期钩子 ------------------------ */
onShow(() => {
  userPlanes()
})

onMounted(() => {
  initPage()
})

onLoad(options => {
  // 处理路由参数，确保都是字符串类型
  if (options) {
    Object.keys(options).forEach(key => {
      if (typeof options[key] !== 'string') {
        options[key] = String(options[key])
      }
    })
  }

  uni.$on('autoJump', data => {
    autoJump.value = data
  })
})

/* ------------------------ 导入功能相关 ------------------------ */
function importByText() {
  showToast('暂未开放~')
}

// 添加词书
async function addBook() {
  const res = await addOwnerBook({
    title: bookName.value,
    owner: '个人',
  })
  if (res.code === 200) {
    showToast('添加成功')
    showAddBook.value = false
    bookName.value = ''
  }
}

// 导入教程
function goTutorial() {
  uni.navigateTo({
    url: '/subPages/recite-words/import-tutorial',
  })
}
// 获取模板
async function chooseTemplate() {
  const res = await getImportTemplate()
  templateList.value = res.data.list.map((item: any) => ({
    name: item.name,
    fileName: item.file[0].name,
    value: item.file[0].url,
  }))
  showTemplate.value = true
}
// 文件下载函数 - 使用原生浏览器下载
async function downloadFile(url: string, fileName: string) {
  try {
    // #ifdef H5
    // 确保 URL 是完整的
    const fullUrl = url.startsWith('http') ? url : `${import.meta.env.VITE_SERVE}${url}`
    // 直接使用浏览器原生下载，避免 getFileStream 的问题
    const link = document.createElement('a')
    link.href = fullUrl
    link.download = fileName
    link.target = '_blank'
    link.style.display = 'none'

    // 添加到页面并触发点击
    document.body.appendChild(link)
    link.click()

    // 清理
    setTimeout(() => {
      if (document.body.contains(link)) {
        document.body.removeChild(link)
      }
    }, 100)

    showSuccessToast('已开始下载')
    // #endif

    // #ifndef H5
    const res = await uni.downloadFile({
      url: import.meta.env.VITE_SERVE + url,
    })
    if (res.statusCode === 200) {
      const { savedFilePath } = await uni.saveFile({
        tempFilePath: res.tempFilePath,
      })
      uni.openDocument({
        filePath: savedFilePath,
        showMenu: true,
      })
      showSuccessToast('下载成功')
    } else {
      showFailToast('下载失败')
    }
    // #endif
  } catch (error) {
    console.error('下载失败:', error)
    showFailToast('下载失败')
  }
}

// 模板选择
async function onTemplateSelect(template: any) {
  const url = template.value
  showLoading('下载中...')
  await downloadFile(url, template.fileName)
  hideLoading()
}
// 词书列
const booksCulumn = computed(() => {
  return myBookList.value.map(item => item.title)
})
// 选择文件
function confirmChooseBook() {
  const book = myBookList.value.find(item => item.title === bookPickerRef.value.getValues()[0])
  chooseBook.value = book
  importExcelFile()
}

// 选择词书并导入文件
async function chooseBookAndImport() {
  myBookList.value = (await getMyBookList()).data.list
  if (myBookList.value.length) {
    showChooseBook.value = true
  } else {
    uni.showToast({
      title: '请先添加词书！',
      icon: 'none',
    })
  }
}
// 选择表格文件
function importExcelFile() {
  // #ifdef MP-WEIXIN
  // eslint-disable-next-line @typescript-eslint/ban-ts-comment
  // @ts-expect-error
  wx.chooseMessageFile({
    count: 1,
    type: 'file',
    extension: ['xls', 'xlsx'],
    success: (res: any) => {
      const file = res.tempFiles[0]
      if (file.size > 10 * 1024 * 1024) {
        uni.showToast({
          title: '文件大小不能超过10MB',
          icon: 'none',
        })
      }
      // 处理Excel文件上传
      previewExcelFile(file.path).then(async (res: any) => {
        const data = res.data
        const words = data.data.slice(1).map((item: any) => {
          return {
            wordbookId: chooseBook.value?._id,
            word: item[1],
            pronunciation: item[2],
            meaning: item[3].split(';'),
          }
        })
        if (words.length === 0) {
          return uni.showToast({
            title: '文件内容为空',
            icon: 'none',
          })
        }
        const result = await batchAddWord(words)
        if (result.code === 200) {
          uni.showToast({
            title: '导入成功',
            icon: 'success',
          })
          showChooseBook.value = false
          showImportSheet.value = false

          // 切换到"我的"tab并刷新列表，以便用户看到新导入的词书
          fixedTabValue.value = 1
          isFixedTabCancelled.value = false
          isDynamicTabCancelled.value = true
          await changeOnMy()
        }
      })
    },
    fail: () => {
      uni.showToast({
        title: '选择文件失败',
        icon: 'none',
      })
    },
  })
  // #endif
}
// 是否激活
function isActive(tag: string) {
  return currentTag.value === tag
}

/* ------------------------ 页面初始化相关 ------------------------ */
// 初始化获取数据
async function initPage() {
  const result = (await getReciteTags()).data.list

  // 只处理动态tabs，不包含固定的"在学"和"我的"
  const dynamicTabsData = result.reduce((prev, cur) => {
    const index = prev.findIndex(item => item.name === cur.type)
    if (index === -1) {
      prev.push({ name: cur.type, tags: [cur.tags] })
    } else {
      prev[index].tags.push(cur.tags)
    }
    return prev
  }, [] as Array<{ name: string; tags: string[] }>)

  // 设置动态tabs
  dynamicTabs.value = dynamicTabsData

  // 默认选择逻辑：优先选择第一个动态tab，如果没有动态tabs则选择"在学"
  if (dynamicTabsData.length > 0) {
    // 有动态tabs，选择第一个动态tab
    dynamicTabValue.value = 0
    isFixedTabCancelled.value = true
    isDynamicTabCancelled.value = false

    const firstTab = dynamicTabsData[0]
    currentTag.value = firstTab.tags[0] || ''
    const res = await getReciteList(firstTab.name, currentTag.value)
    list.value = res.data.list
    // 加载词书统计信息
    await loadBookStats(list.value.map(item => item._id))
  } else {
    // 没有动态tabs，默认显示"在学"
    fixedTabValue.value = 0
    isFixedTabCancelled.value = false
    isDynamicTabCancelled.value = true
    await changeOnLearn()
  }

  loading.value = false
}

// 获取用户计划表
function userPlanes() {
  // 获取在学计划表
  getRecitePlan()
    .then(res => {
      recitePlanList.value = res.data.list

      if (!recitePlanList.value.length) return
      return getUserReciteBook()
    })
    .then(res => {
      if (!res || res.data.list.length === 0) return
      const book = res.data.list[0]
      userReciteId.value = book._id
    })
}

// 点击词书
function onBookClick(book: BookInfoType) {
  // 设置当前选中的词书（通过 useUserStore）
  useUserStore().setBookInfo(book)
  uni.navigateBack()
}
/* ------------------------ Tab组件功能 ------------------------ */
// 切换标签
async function changeTag(tab: number | string, tag: string) {
  currentTag.value = tag
  if (tab === 0) {
    fixedTabValue.value = 0
    setFixedTabActive() // 设置固定tabs为激活状态
    const ids = recitePlanList.value.map(item => item.book)
    const res = await getReciteListById(ids)
    list.value = res.data.list
    // 加载词书统计信息
    await loadBookStats(list.value.map(item => item._id))
    return
  }
  // 在动态tabs中查找对应的索引
  const dynamicIndex = dynamicTabs.value.findIndex(item => item.name === `${tab}`)
  dynamicTabValue.value = dynamicIndex
  setDynamicTabActive() // 设置动态tabs为激活状态
  const res = await getReciteList(tab.toString(), tag)
  list.value = res.data.list
  // 加载词书统计信息
  await loadBookStats(list.value.map(item => item._id))
}

// 切换固定tabs
async function changeFixedTabs({ index }: { index: number }) {
  fixedTabValue.value = index

  // 互斥效果：选中固定tabs时，固定tabs正常，动态tabs取消
  isFixedTabCancelled.value = false
  isDynamicTabCancelled.value = true

  switch (index) {
    case 0:
      await changeOnLearn()
      break
    case 1:
      await changeOnMy()
      break
  }
}

// 切换动态tabs
async function changeDynamicTabs({ index }: { index: number }) {
  dynamicTabValue.value = index

  // 互斥效果：选中动态tabs时，动态tabs正常，固定tabs取消
  isFixedTabCancelled.value = true
  isDynamicTabCancelled.value = false

  // 处理动态tabs的逻辑
  if (index < dynamicTabs.value.length) {
    const tab = dynamicTabs.value[index]
    currentTag.value = tab.tags[0]
    const res = await getReciteList(tab.name, currentTag.value)
    list.value = res.data.list
    // 加载词书统计信息
    await loadBookStats(list.value.map(item => item._id))
  }
}

// 切换目前在学的词书
async function changeOnLearn() {
  const res = await getAllReciteList()
  const ids = recitePlanList.value.map(item => item.book)
  const studyBook = res.data.list.filter(item => ids.includes(item._id))
  list.value = studyBook
  // 加载词书统计信息
  await loadBookStats(list.value.map(item => item._id))
}

// 切换我的词书
async function changeOnMy() {
  const res = await getMyBookList()
  list.value = res.data.list
  // 加载词书统计信息
  await loadBookStats(list.value.map(item => item._id))
}

// tab互斥控制方法
function setFixedTabActive() {
  isFixedTabCancelled.value = false
  isDynamicTabCancelled.value = true
}

function setDynamicTabActive() {
  isFixedTabCancelled.value = true
  isDynamicTabCancelled.value = false
}

/* ------------------------ 词书操作相关 ------------------------ */
// 显示词书操作弹窗
function showBookActions(book: BookInfoType) {
  currentBook.value = book
  showActionPopup.value = true
}

// 隐藏操作弹窗
async function hideActionPopup() {
  showActionPopup.value = false
  currentBook.value = null
}

// 统一的操作菜单点击处理
function handleActionClick(item: any) {
  console.log(currentBook.value)
  if (typeof item.action === 'function') {
    item.action()
  } else {
    console.log('无效的操作:', item.action)
  }
}

/* ------------------------ 学习计划操作 ------------------------ */
// 修改学习计划
async function handleModifyPlan() {
  if (!currentBook.value) return

  try {
    showLoading('检查学习计划...')

    // 获取用户的学习计划
    const planRes = await getRecitePlan()
    const userPlans = planRes.data.list

    // 查找当前词书的学习计划
    const currentBookPlan = userPlans.find(plan => plan.book === currentBook.value!._id)

    hideLoading()

    if (!currentBookPlan) {
      // 没有找到学习计划，提示用户
      uni.showModal({
        title: '提示',
        content: '该词书还没有学习计划，请先创建学习计划',
        showCancel: false,
        confirmText: '知道了',
      })
      return
    }

    // 有学习计划，跳转到 plan.vue 页面（修改模式）
    uni.navigateTo({
      url: `/subPages/recite-words/plan?id=${currentBook.value._id}&title=${encodeURIComponent(
        currentBook.value.title
      )}&modify=true`,
    })
  } catch (error) {
    hideLoading()
    console.error('获取学习计划失败:', error)
    uni.showToast({
      title: '获取学习计划失败',
      icon: 'none',
    })
  } finally {
    hideActionPopup()
  }
}

/* ------------------------ 重新学习功能 ------------------------ */
// 重新学习
async function handleRestartStudy() {
  if (!currentBook.value) return

  // 保存当前选中的词书，避免关闭弹窗后丢失
  const book = currentBook.value

  // 先关闭操作弹窗
  hideActionPopup()

  // 确认对话框
  uni.showModal({
    title: '确认重新学习',
    content: `确定要重新学习《${book.title}》吗？这将清除所有学习进度和记录，无法恢复。`,
    confirmText: '确定重新学习',
    cancelText: '取消',
    success: async res => {
      if (res.confirm) {
        await performRestartLearning(book)
      }
    },
  })
}

// 执行重新学习
async function performRestartLearning(book: BookInfoType) {
  if (!book) return

  try {
    showLoading('正在清除学习记录...')

    // 1. 获取并删除学习计划
    const planRes = await getRecitePlan()
    const userPlans = planRes.data.list
    const bookPlan = userPlans.find(plan => plan.book === book._id)

    if (bookPlan) {
      await deleteRecitePlan(bookPlan._id)
    }

    // 2. 获取并删除学习记录
    const recordsRes = await getReciteWordCountByBook(book._id)
    const studyRecords = recordsRes.data.list || []

    if (studyRecords.length > 0) {
      const recordIds = studyRecords.map(record => record._id)
      await deleteReciteWordRecords(recordIds)
    }

    // 不删除熟词生词记录，保留用户的单词分类

    hideLoading()

    uni.showToast({
      title: '重新学习设置成功',
      icon: 'success',
    })

    // 刷新当前列表以更新进度显示
    setTimeout(async () => {
      if (!isFixedTabCancelled.value) {
        if (fixedTabValue.value === 0) {
          await changeOnLearn()
        } else if (fixedTabValue.value === 1) {
          await changeOnMy()
        }
      } else {
        if (dynamicTabValue.value < dynamicTabs.value.length) {
          const tab = dynamicTabs.value[dynamicTabValue.value]
          const res = await getReciteList(tab.name, currentTag.value)
          list.value = res.data.list
          await loadBookStats(list.value.map(item => item._id))
        }
      }
    }, 1000)
  } catch (error) {
    hideLoading()
    console.error('重新学习失败:', error)
    uni.showToast({
      title: '重新学习失败',
      icon: 'none',
    })
  }
}

/* ------------------------ 删除词书功能 ------------------------ */
// 删除词书
async function handleDeleteBook() {
  if (!currentBook.value) return

  // 保存当前选中的词书，避免关闭弹窗后丢失
  const book = currentBook.value

  // 先关闭操作弹窗
  hideActionPopup()

  uni.showModal({
    title: '确认删除',
    content: `确定要删除《${book.title}》吗？删除后无法恢复。`,
    confirmColor: '#FF3B30',
    success: async res => {
      if (res.confirm) {
        await performDeleteBook(book)
      }
    },
  })
}

// 执行删除词书
async function performDeleteBook(book: BookInfoType) {
  try {
    showLoading('正在删除词书...')

    // 检查是否是当前选中的词书
    const userStore = useUserStore()
    const isCurrentSelectedBook = userStore.bookInfo?._id === book._id

    // 1. 获取词书关联的所有单词ID
    const wordsRes = await getBookWordIds(book._id)
    const wordIds = wordsRes.data.list.map(word => word._id)

    // 2. 删除词书关联的单词
    if (wordIds.length > 0) {
      await deleteBookWords(wordIds)
    }

    // 3. 删除词书本身
    await deleteBook(book._id)

    // 4. 如果删除的是当前选中的词书，清除选择状态
    if (isCurrentSelectedBook) {
      userStore.setBookInfo(undefined)
    }

    hideLoading()

    uni.showToast({
      title: '词书删除成功',
      icon: 'success',
    })

    // 刷新"我的"词书列表
    await changeOnMy()
  } catch (error) {
    hideLoading()
    console.error('删除词书失败:', error)
    uni.showToast({
      title: '删除词书失败',
      icon: 'none',
    })
  }
}

/* ------------------------ 操作菜单配置 ------------------------ */
// 操作菜单配置（动态生成）
const actionMenuItems = computed(() => {
  const items = [
    {
      key: 'modify',
      title: '修改学习计划',
      type: 'normal',
      action: handleModifyPlan,
    },
    {
      key: 'restart',
      title: '重新学习',
      type: 'normal',
      action: handleRestartStudy,
    },
  ]

  // 只有在"我的"词书时才显示删除选项
  if (!isFixedTabCancelled.value && fixedTabValue.value === 1) {
    items.push({
      key: 'delete',
      title: '删除词书',
      type: 'danger',
      action: handleDeleteBook,
    })
  }

  items.push({
    key: 'cancel',
    title: '取消',
    type: 'cancel',
    action: hideActionPopup,
  })

  return items
})
</script>

<style lang="scss" scoped>
.words-pages {
  display: flex;
  flex-direction: column;
  .words-tabs {
    position: sticky;
    padding: 0 20rpx;
    top: 0;
    ::v-deep .tabs-swiper {
      margin-top: 0;
    }
    .tabs-pane {
      background-color: $uni-bg-color;
      padding: 10rpx 0 0;
      gap: 18rpx;
      .tag {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        padding: 4rpx 14rpx;
        min-width: 100rpx;
        text-align: center;
        font-size: 22rpx;
        border-radius: 32rpx;
        box-sizing: border-box;
        border: 2rpx solid #e5e5e5;
        background-color: #ffffff;
        color: #666666;
        transition: all 0.3s ease;
        cursor: pointer;

        &--active {
          border-color: #459af7;
          background-color: #459af7;
          color: #ffffff;
        }

        &:hover {
          opacity: 0.8;
        }
      }
    }
  }
  .book-list {
    border-top: 1rpx solid #dedede;
    background-color: $uni-bg-color;
    flex: 1;
    margin-top: 15rpx;
    .book-card {
      background-color: #fff;
      border-bottom: 1rpx solid #eeeeee;

      .book-card-content {
        display: flex;
        padding: 20rpx;
        padding-right: 32rpx;
        .book-image {
          width: 220rpx;
          height: 150rpx;
          border-radius: 10rpx;
          overflow: hidden;
          margin-right: 18rpx;
          flex-shrink: 0;

          .book-cover {
            width: 100%;
            height: 100%;
            border-radius: 12rpx;
          }
        }

        .book-info {
          flex: 1;
          display: flex;
          flex-direction: column;
          justify-content: space-between;
          color: #333333;
          .book-title {
            font-size: 30rpx;
            font-weight: bold;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }

          .book-tags {
            font-size: 22rpx;
            color: #537eef;
            background-color: #e9f6fe;
            border-radius: 5rpx;
            padding: 1rpx 8rpx;
            margin-right: 20rpx;
            height: 32rpx;
            display: inline-block;
          }
        }
      }
    }
  }
}

// 词书操作弹窗样式
.action-popup {
  .action-item {
    padding: 30rpx 0 16rpx;
    text-align: center;
    border-bottom: 1rpx solid #f5f5f5;
    overflow: hidden;
    cursor: pointer;

    &:last-child {
      border-bottom: none;
    }

    &.danger {
      .action-text {
        color: #ff3b30;
      }
    }

    .action-text {
      font-size: 32rpx;
      color: #333333;
      font-weight: 400;
    }

    .danger-text {
      color: #ff3b30;
    }

    &:active {
      background-color: #f5f5f5;
    }
  }
}
</style>
