<template>
  <view class="feedback-item" @click="jumpDetail">
    <image :src="typeImage" class="reply-bg"> </image>
    <view class="title">{{ data?.f_title }}</view>
    <view class="comment mt-2">
      <up-text type="info" :line="2" :text="data?.f_detaile"></up-text>
    </view>

    <view class="flex justify-between">
      <view class="flex items-center">
        <!-- 用户S -->
        <view v-if="data?.f_enable_name === 0" class="flex items-center mt-2">
          <u-icon name="account" color="#8A8D95" size="18"></u-icon>
          <view class="ml-2">
            <up-text type="info" size="26rpx" :text="data.creatorUserId.fullName"></up-text>
          </view>
        </view>
        <!-- 用户E -->
      </view>

      <view class="flex items-center">
        <u-icon name="clock" color="#8A8D95" size="18"></u-icon>

        <view class="ml-1">
          <u-text type="info" size="26rpx" :text="formatDate(data?.creatorTime)"></u-text>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts" name="feedback-item">
import type { PropType } from 'vue'
import type { FeedBackItemType } from '@/api/project/feedback/type'
import useDateFormatter from '@/hooks/useDateFormatter'

const props = defineProps({
  data: {
    type: Object as PropType<FeedBackItemType>,
  },
})
const typeImage = computed(() => {
  return props.data?.f_stauts === '已回复'
    ? 'https://kindoucloud.com/api/file/previewImage/6613669f955ed0316d6434c9/667d111f0bb07d7cd6ed3094'
    : 'https://kindoucloud.com/api/file/previewImage/6613669f955ed0316d6434c9/667d111f0bb07d7cd6ed3093'
})

const { formatDate } = useDateFormatter('YYYY年MM月DD日 HH时mm分')

function jumpDetail() {
  uni.navigateTo({
    url: `/subPages/feedback/detail?id=${props.data?._id}`,
  })
}
</script>

<style lang="scss" scoped>
.feedback-item {
  background: #ffffff;
  border-radius: 8rpx;
  margin: 20rpx;
  margin-top: 30rpx;
  padding: 20rpx;
  box-sizing: border-box;
  position: relative;

  .title {
    font-size: 34rpx;
    font-weight: bold;
    color: #333333;
  }

  .reply-bg {
    position: absolute;
    top: 0;
    right: 0;
    width: 100rpx;
    height: 100rpx;
  }
}
</style>
