<template>
  <view class="w-full h-full">
    <LoadMoreList :request-fn="getMarkingList">
      <template #default="{ list }">
        <view
          v-for="(item, index) in (list as MarkingItemData[])"
          :key="index"
          class="w710rpx h100rpx ma mt20rpx p20rpx box-border flex items-center justify-between rounded-20rpx bg-white"
          @click="toDetail(item._id)">
          <view>{{ item.name }}</view>
          <u-icon name="arrow-right" size="20"></u-icon>
        </view>
      </template>
    </LoadMoreList>
  </view>
</template>

<script setup lang="ts" name="marking-list">
import LoadMoreList from '@/components/LoadMoreList.vue'
import { getMarkingList } from '@/api/project/special-marking'
import type { MarkingItemData } from '@/api/project/special-marking/type'

const toDetail = (id: string) => {
  uni.navigateTo({
    url: `/subPages/special-marking/teacher-grading-papers1?id=${id}`,
  })
}
</script>

<style>
page {
  background-color: #e5e5e5;
}
</style>

<style lang="scss" scoped></style>
