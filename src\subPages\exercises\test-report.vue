<template>
  <view>
    <LoadingPage>
      <template #default="{ show }">
        <template v-if="show">
          <view class="z-99 relative">
            <up-navbar
              title="练习报告"
              :border="false"
              color="#fff"
              :title-style="titleStyle"
              :bg-color="bgColor"
              placeholder
              auto-back>
            </up-navbar>
          </view>
          <div class="report-wrap pb40rpx">
            <div class="linear z-10"></div>
            <div class="relative z-11 p30rpx">
              <header>
                <view class="exercise-report-header bg-white radius-10 padding-30 margin-b-20">
                  <view class="progress-wrap margin-b-20 divider relative">
                    <!-- <span class="difficulty bg-theme color-white"> 难度{{ 4.4}} </span> -->
                    <ChocolateProgressBar
                      :right-num="correctTopicsCount"
                      :total-num="listData.length"></ChocolateProgressBar>
                  </view>
                  <view class="text-note font-size-26 flex justify-between">
                    <view class="icon-text flex mr">
                      <u-icon name="file-text" size="36rpx" :custom-style="fillIconStyle" />
                      <text class="font-size-26 mt8rpx ml15rpx">交卷类型</text>
                    </view>
                    <text class="font-size-26">{{ cardItem.type }}</text>
                  </view>
                  <view class="text-note font-size-26 flex justify-between">
                    <view class="icon-text flex mr">
                      <u-icon name="clock" size="26rpx" :custom-style="clockIconStyle" />
                      <text class="font-size-26 mt8rpx ml16rpx">交卷时间</text>
                    </view>
                    <text class="font-size-26">{{ formatDate(cardItem.creatorTime) }}</text>
                  </view>
                </view>
              </header>
              <body>
                <view class="exercise-report-main">
                  <view class="answer-sheet bg-white padding-30 margin-b-20 radius-10">
                    <view class="flex justify-between">
                      <view class="answer-sheet-title u-main-color margin-b-20"> 答题卡 </view>
                      <view class="state-line flex items-center">
                        <view class="state-dot bg-state"></view>
                        <text class="text-note margin-r-24">未答</text>
                        <view class="state-dot bg-state correct"></view>
                        <text class="text-note margin-r-24">答对</text>
                        <view class="state-dot bg-state warn"></view>
                        <text class="text-note margin-r-24">待批改</text>
                        <view class="state-dot bg-state wrong"></view>
                        <text class="text-note">答错</text>
                      </view>
                    </view>
                    <swiper
                      class="answer-swiper"
                      indicator-dots
                      indicator-color="#eaf6ff"
                      indicator-active-color="#439cf7">
                      <swiper-item v-for="(pageIndex, index) in totalPages" :key="index">
                        <view class="topic-list">
                          <view
                            v-for="(topic, i) in getTopicsForPage(pageIndex)"
                            :key="i"
                            class="topic-item bg-state"
                            :class="checkState(topic)"
                            @tap="pickTest(i)">
                            {{ (pageIndex - 1) * itemsPerPage + i + 1 }}
                            <!-- 这里显示的是全局的索引 -->
                          </view>
                        </view>
                      </swiper-item>
                    </swiper>
                  </view>
                  <view class="analysis-result bg-white padding-30 radius-10">
                    <view class="answer-sheet-title">练习情况</view>
                    <view class="info-wrap padding-y-20 flex justify-evenly">
                      <view class="info-item">
                        <view>一共</view>
                        <text class="text-state">{{ listData.length || 0 }}题</text>
                      </view>
                      <view class="info-item">
                        <view>未答</view>
                        <text class="text-state">{{ unansweredCount }}题</text>
                      </view>
                      <view class="info-item">
                        <view>答对</view>
                        <text class="text-state correct">{{ correctTopicsCount }}题</text>
                      </view>
                      <view class="info-item">
                        <view>待批改</view>
                        <text class="text-state warn">{{ 0 }}题</text>
                      </view>
                      <view class="info-item">
                        <view>答错</view>
                        <text class="text-state wrong">{{ wrongTopicsCount }}题</text>
                      </view>
                      <view class="info-item">
                        <view>总用时</view>
                        <text class="text-state">{{
                          formatTime(Number(cardItem.answerTime)) || '00:00:00'
                        }}</text>
                      </view>
                    </view>
                    <view class="answer-sheet-title"> 基础知识 </view>
                    <view class="flex margin-t-20 margin-b-20">
                      <text class="text-note margin-r-24">全站正确率%</text>
                      <u-line-progress :percentage="30" height="32rpx" active-color="#537EEF" />
                    </view>
                    <view class="flex margin-t-20 margin-b-20">
                      <text class="text-note margin-r-24">你的正确率%</text>
                      <u-line-progress
                        :percentage="accuracy"
                        height="32rpx"
                        active-color="#537EEF" />
                    </view>
                    <view class="answer-note text-note">
                      <text class="inline-block margin-r-24">
                        答对 {{ correctTopicsCount }}/{{ listData.length }}
                      </text>
                      <text class="inline-block"
                        >总用时 {{ formatTime(Number(cardItem.answerTime)) }}</text
                      >
                    </view>
                  </view>
                </view>
              </body>
              <footer>
                <view class="exercise-report-footer flex justify-evenly safe-area bg-white">
                  <view @tap="analyze('all')">全部解析</view>
                  <view @tap="analyze('wrong')">错题解析</view>
                </view>
              </footer>
            </div>
          </div>
        </template>
      </template>
    </LoadingPage>
  </view>
</template>

<script setup lang="ts" name="test-report">
import ChocolateProgressBar from './components/ChocolateProgressBar.vue'
import LoadingPage from '@/components/LoadingPage.vue'
import { arraysEqual, getStatusBarHeight, showToast } from '@/utils/index'
// 类型
import type { ReportItem, TestHistoryItem } from '@/api/project/exercises/type'
import { getReportList } from '@/api/project/exercises/index'
import useDateFormatter from '@/hooks/useDateFormatter'
import useExercisesStore from '@/store/modules/exercises'
const useExercises = useExercisesStore()
const { formatDate } = useDateFormatter('YYYY-MM-DD HH:mm:ss')
const titleStyle = computed(() => ({ fontSize: '36rpx', color: 'black' }))
const bgColor = ref('#e2eaff')
const cardItem = ref({} as TestHistoryItem)
const listData = ref([] as ReportItem[])
const fillIconStyle = {
  width: '30rpx',
  marginTop: '4rpx',
  textIndent: '-4rpx',
}
const clockIconStyle = {
  marginTop: '8rpx',
  marginLeft: '3rpx',
}
const fullHeight = ref({
  height: `calc(100vh - ${(Number(getStatusBarHeight()) + 44) * 2}rpx)`,
})
// 计算属性：正确的主题数量
const correctTopicsCount = computed(() => {
  return listData.value.filter(item => checkState(item) === 'correct').length
})

// 计算属性：未答题目
const unansweredCount = computed(() => {
  return listData.value.filter(item => item.user_answer === '').length
})

// 计算属性：错误的数量
const wrongTopicsCount = computed(() => {
  return listData.value.filter(item => checkState(item) === 'wrong').length
})
const itemsPerPage = 30

// 计算总页数
const totalPages = computed(() => {
  return Math.ceil(listData.value.length / itemsPerPage)
})

// 根据页码获取对应的主题
const getTopicsForPage = (pageIndex: number) => {
  const start = (pageIndex - 1) * itemsPerPage // 开始索引
  const end = start + itemsPerPage // 结束索引
  return listData.value.slice(start, end)
}
function pickTest(index: number) {
  useExercises.setDefaultIndex(index)
  useExercises.setListData(listData.value)
  uni.navigateTo({ url: `/subPages/exercises/answer-sheet?title=全部分析&type=report` })
}
function analyze(type: string) {
  const title = type === 'all' ? '全部分析' : '错题分析'
  useExercises.setDefaultIndex(0)
  let filteredList
  if (type === 'all') {
    filteredList = listData.value
  } else {
    filteredList = listData.value.filter(
      item => !arraysEqual(item.type, [item.user_answer], item.answer)
    )
  }
  useExercises.setListData(filteredList)
  uni.navigateTo({ url: `/subPages/exercises/answer-sheet?title=${title}&type=report` })
}

onLoad(e => {
  const parsedData = JSON.parse(decodeURIComponent(e?.reportId))
  cardItem.value = parsedData
  useExercises.setItemData(parsedData)
  getReportList(parsedData._id).then(({ code, data }) => {
    if (code === 200) {
      listData.value = data
    }
  })
})
const formatTime = (time: number) => {
  function padStart(num: number) {
    return (num | 0).toString().padStart(2, '0') || '00'
  }
  return `${padStart(time / 60 / 60)}:${padStart((time / 60) % 60)}:${padStart(time % 60)}`
}
// 计算精度
const accuracy = computed(() => {
  const rightCount = correctTopicsCount.value // 获取正确的主题数量
  const totalCount = listData.value.length // 获取总项目数量
  // 计算精度并格式化为百分比
  return totalCount > 0 ? ((rightCount / totalCount) * 100).toFixed(2) : '0.00' // 防止除以零
})
function checkState(item: any) {
  // 判断 item 的类型
  if (item.type === '阅读理解') {
    // 根据 user_score 判断
    return item.user_score > 0 ? 'correct' : 'wrong'
  } else {
    // 使用 arraysEqual 方法判断答案
    if (item.user_answer === '') {
      return ''
    } else {
      return arraysEqual(item.type, [item.user_answer], item.answer) ? 'correct' : 'wrong'
    }
  }
}
</script>

<style>
page {
  background: #e2eaff;
}
</style>

<style lang="scss" scoped>
@import '@/common/css/index.scss';
@import '@/common/css/project.scss';
.report-wrap {
  //   height: 100vh;
  position: relative;
  background: #f4f5f7;
  .linear {
    position: absolute;
    top: 0;
    width: 750rpx;
    height: 370rpx;
    background: linear-gradient(179deg, #e2e9fb 0%, #f4f5f7 100%);
    border-radius: 0rpx 0rpx 0rpx 0rpx;
  }
}
.exercise-report {
  background-image: linear-gradient(179deg, #e2e9fb 0%, #f4f5f7 100%);
  &-header {
    .progress-wrap {
      padding-bottom: 8rpx;
      border-bottom-color: #eee;
      .difficulty {
        position: absolute;
        right: 0;
        font-size: 24rpx;
        // line-height: 1;
        padding: 8rpx 20rpx;
        border-radius: 30px 25px 25px 0;
      }
    }
    .icon-text > view {
      margin-right: 8rpx;
    }
  }
  &-main {
    margin-bottom: 120rpx;
    .answer-sheet {
      &-title {
        font-size: 34rpx;
      }

      .answer-swiper {
        height: 480rpx;
        margin-top: 24rpx;
        .topic-list {
          display: grid;
          grid-template-columns: repeat(6, 1fr);
          gap: 16rpx;
          justify-items: center;
          .topic-item {
            width: 70rpx;
            height: 70rpx;
            border-radius: 36rpx;
            text-align: center;
            line-height: 70rpx;
          }
        }
      }
    }
    .analysis-result {
      // height: rpx;
      .info-wrap {
        margin: 30rpx 0;
        background-color: #f6f7f8;
        border-radius: 10rpx;
        & > .info-item {
          text-align: center;
          > view {
            color: #969dab;
            line-height: 1;
            margin-bottom: 12rpx;
            font-size: 24rpx;
          }
          > text {
            font-size: 24rpx;
          }
        }
      }
    }
    .answer-note > text {
      min-width: 240rpx;
    }
  }
  &-footer {
    width: 100%;
    position: fixed;
    bottom: 0;
    left: 0;
    padding-top: 8rpx;
    & > view {
      width: 45%;
      height: 86rpx;
      line-height: 86rpx;
      border-radius: 46rpx;
      text-align: center;
      font-size: 34rpx;
    }
    & > view:nth-child(1) {
      background: #e5eafd;
      color: #537eef;
    }
    & > view:nth-child(2) {
      background: #537eef;
      color: #fff;
    }
  }
}
</style>
