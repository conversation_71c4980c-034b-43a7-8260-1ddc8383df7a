<template>
  <!-- 考试科目表格 -->
  <view class="h70rpx bg-#459AF7 flex text-white justify-around items-center text-18rpx row bor-b">
    <view class="text-center h74rpx lh-74rpx bor-r" style="flex: 1">{{ titles[0] }}</view>
    <view class="text-center" style="flex: 4">{{ titles[1] }}</view>
  </view>
  <view
    v-for="(ite, index) in list"
    :key="`${index}1`"
    class="w-full flex text-#333333 justify-around items-center text-18rpx bor-b row">
    <view class="h-full text-left flex flex-center col" style="flex: 1">{{ ite.major }}</view>
    <view class="text-left col bor-r" style="flex: 4">
      <!-- 考试科目 -->
      <view
        class="w-full p10rpx pl20rpx box-border h50rpx bor-b flex fw-bold justify-start items-center">
        {{ ite.list[0] }}
      </view>
      <view class="p10rpx pl20rpx box-border">
        <!-- {{ ite.books }} -->
        <view v-for="i in ite.list.splice(1)" :key="i">{{ i }}</view>
      </view>
    </view>
  </view>
  <u-empty v-if="list.length === 0" text="暂无数据"></u-empty>
</template>

<script setup lang="ts" name="exam-table">
defineProps<{
  titles: string[]
  list: {
    major: string
    // subject: string
    // books: string
    list: string[]
  }[]
}>()
</script>

<style lang="scss" scoped>
.bor-bor {
  border: 1px solid #d1d1d1;
}
.bor-b {
  border-bottom: 1px solid #d1d1d1;
}
.bor-r {
  border-right: 1px solid #d1d1d1;
}
.bor-l {
  border-left: 1px solid #d1d1d1;
}

.row {
  overflow: hidden;
  .col {
    position: relative;
    &::before {
      content: '';
      position: absolute;
      height: 600px;
      left: 0;
      background-color: #d1d1d1;
      width: 1px;
    }
    &:last-child::after {
      content: '';
      position: absolute;
      height: 600px;
      right: 0;
      background-color: #d1d1d1;
      width: 1px;
    }
  }
}
</style>
