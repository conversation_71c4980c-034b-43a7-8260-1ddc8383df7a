<template>
  <scroll-view
    v-if="topicList.length > 0"
    scroll-y
    scroll-with-animation
    :scroll-top="0"
    class="bg-#f4f5f7 container">
    <up-navbar
      :title="tabbarTitle"
      :border="false"
      color="#fff"
      :title-style="titleStyle"
      placeholder
      @left-click="goBack">
    </up-navbar>
    <LoadingPage
      empty-text="暂无题目"
      loading-text="题目加载中..."
      :loading="loading"
      :empty="empty">
      <!-- 插槽内容 -->
      <template #default="{ show }">
        <ReportMode v-if="showMode === 'report'" :topic-list="topicList" :test="show"></ReportMode>
        <OpenMode v-if="showMode === 'open'" :topic-list="topicList"></OpenMode>
      </template>
    </LoadingPage>
  </scroll-view>
</template>

<script setup lang="ts" name="topic-transform">
// 加载页面
// report 查看模式 不能作答 直接显示答案 解析
import ReportMode from './components/mode/ReportMode.vue'
// open 开放模式 可以作答 点击可显示答案 解析 不计算成绩
import OpenMode from './components/mode/OpenMode.vue'
import LoadingPage from '@/components/LoadingPage.vue'
// 类型
import type { TopicItem } from '@/api/project/exercises/type'
import useExercisesStore from '@/store/modules/exercises'
const useExercises = useExercisesStore()
// 加载中
const loading = ref(true)
// 是否空
const empty = ref(false)
const tabbarTitle = ref('')
// title样式
const titleStyle = computed(() => ({ fontSize: '36rpx', color: 'black' }))

const topicList = ref<TopicItem[]>([])

const showMode = ref('report')
function goBack() {
  uni.navigateBack()
}

onLoad(async (e: any) => {
  const { mode, title, type } = e
  showMode.value = mode || type
  /// 设置标题
  tabbarTitle.value = title
  topicList.value = useExercises.listData
  if (topicList.value.length === 0) {
    empty.value = true
  }
  loading.value = false
  // const ids = questionList.value.map(e => e.question_id)
  // await getTopicListByIds(ids).then(({ code, data }) => {
  //   if (code === 200) {
  //     topicList.value = data.list
  //     console.log(topicList.value)
  //
  //   }
  // })
})
</script>

<style lang="scss" scoped>
@import '@/common/css/index.scss';
@import '@/common/css/project.scss';
.container {
  height: 100%;
  padding-bottom: 120rpx;
  min-height: 100vh !important;
}
</style>
