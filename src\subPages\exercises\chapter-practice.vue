<template>
  <view>
    <ExamList title="章节练习" :request-fn="getChapterList" @go-test="gotoTest"></ExamList>
  </view>
</template>

<script setup lang="ts" name="chapter-practice">
import ExamList from './components/ExamList.vue'
import { getChapterList } from '@/api/project/exercises/index'
import type { PaperItem } from '@/api/project/exercises/type'
// 跳转
function gotoTest(exam: PaperItem) {
  uni.navigateTo({
    url: `/subPages/exercises/answer-sheet?type=test&examId=${exam.testPaperId}&title=${exam.name}&answerType=章节练习`,
  })
}
</script>
