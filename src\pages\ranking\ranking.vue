<template>
  <view class="rangking-container">
    <!-- 自定义导航栏S -->
    <up-navbar :auto-back="true" placeholder bg-color="rgba(0, 0, 0, 0)">
      <template #left>
        <view class="flex items-center">
          <up-icon name="arrow-left" size="40rpx" color="white" />
          <view class="text-30 text-white ml-24rpx">排行榜</view>
        </view>
      </template>
    </up-navbar>
    <!-- 自定义导航栏E -->

    <!-- 下拉框S -->
    <view class="flex items-center mt-20rpx text-28rpx">
      <view class="top-select ml-auto mr-20rpx" @click="showOneSelect = !showOneSelect">
        <view class="select-name">单词打卡</view>
        <up-icon name="arrow-down" size="28rpx" color="white" />
        <view class="select-content" v-show="showOneSelect"> </view>
      </view>
      <view class="top-select mr30rpx">
        <view class="select-name">本周</view>
        <up-icon name="arrow-down" size="28rpx" color="white" />
      </view>
    </view>
    <!-- 下拉框E -->

    <!-- 排行榜前三S -->
    <view class="px108rpx flex mt64rpx justify-between items-end">
      <!-- 第二 -->
      <view class="flex-center flex-col">
        <view class="w117rpx h117rpx relative">
          <image
            class="wfull hfull rounded-full"
            :src="imagesConstants.noLoginAvatar"
            mode="aspectFit" />
          <image
            class="w56rpx h56rpx absolute top--24rpx left--12rpx"
            :src="imagesConstants.top2"
            mode="aspectFit"></image>
        </view>
        <view class="text-30 text-white mt14rpx font-bold">苏三</view>
        <view class="w120rpx h38rpx bg-#FDD432 rounded-full mt-8rpx flex items-center">
          <view class="w38rpx h38rpx bg-#FDAD2F rounded-full box-border p8rpx flex-center">
            <image
              class="wfull hfull"
              :src="imagesConstants.rangkingScore"
              mode="aspectFit"></image>
          </view>
          <view class="text-24 text-white ml-8rpx">98分</view>
        </view>
      </view>
      <!-- 第一 -->
      <view class="flex-center flex-col pb26rpx">
        <view class="w156rpx h156rpx relative">
          <image
            class="wfull hfull rounded-full"
            :src="imagesConstants.noLoginAvatar"
            mode="aspectFit" />
          <image
            class="w56rpx h56rpx absolute top--16rpx left--8rpx"
            :src="imagesConstants.top1"
            mode="aspectFit"></image>
        </view>
        <view class="text-30 text-white mt14rpx font-bold">苏三</view>
        <view class="w120rpx h38rpx bg-#FDD432 rounded-full mt-8rpx flex items-center">
          <view class="w38rpx h38rpx bg-#FDAD2F rounded-full box-border p8rpx flex-center">
            <image
              class="wfull hfull"
              :src="imagesConstants.rangkingScore"
              mode="aspectFit"></image>
          </view>
          <view class="text-24 text-white ml-8rpx">98分</view>
        </view>
      </view>
      <!-- 第三 -->
      <view class="flex-center flex-col">
        <view class="w117rpx h117rpx relative">
          <image
            class="wfull hfull rounded-full"
            :src="imagesConstants.noLoginAvatar"
            mode="aspectFit" />
          <image
            class="w56rpx h56rpx absolute top--24rpx left--12rpx"
            :src="imagesConstants.top3"
            mode="aspectFit"></image>
        </view>
        <view class="text-30 text-white mt14rpx font-bold">苏三</view>
        <view class="w120rpx h38rpx bg-#FDD432 rounded-full mt-8rpx flex items-center">
          <view class="w38rpx h38rpx bg-#FDAD2F rounded-full box-border p8rpx flex-center">
            <image
              class="wfull hfull"
              :src="imagesConstants.rangkingScore"
              mode="aspectFit"></image>
          </view>
          <view class="text-24 text-white ml-8rpx">98分</view>
        </view>
      </view>
    </view>
    <!-- 排行榜前三E -->

    <view class="flex items-end justify-center mt8rpx text-white">
      <view
        class="w180rpx h69rpx bg-#9FCDFF font-italic font-bold text-28 flex-center rounded-tl-15rpx">
        TOP.2
      </view>
      <view
        class="w230rpx h87rpx bg-#BCDCFF font-italic font-bold text-40 flex-center rounded-t-15rpx">
        TOP.1
      </view>
      <view
        class="w180rpx h60rpx bg-#9FCDFF font-italic font-bold text-28 flex-center rounded-tr-15rpx">
        TOP.3
      </view>
    </view>

    <!-- 排行榜列表S -->
    <view
      class="w710rpx flex-1 mx-auto bg-white box-border px-56rpx py-16rpx rounded-t-50rpx text-#333333"
      style="overflow: hidden; overflow-y: auto; overflow-x: hidden">
      <up-list @scrolltolower="" style="height: calc(100%)">
        <up-list-item v-for="(item, index) in 15" :key="index">
          <up-cell title="苏三" :border="false" value="88">
            <template #icon>
              <view class="flex items-center pr16rpx">
                <view class="w72rpx text-36rpx">{{ index + 4 }}</view>
                <up-avatar
                  shape="square"
                  size="80rpx"
                  :src="imagesConstants.noLoginAvatar"
                  customStyle="margin: -3px 5px -3px 0"></up-avatar>
              </view>
            </template>
          </up-cell>
        </up-list-item>
      </up-list>
    </view>
    <!-- 排行榜列表E -->

    <!-- 占位 -->
    <view class="w-full h-160rpx"></view>

    <!-- 底部我的排名S -->
    <view
      class="w-full h-160rpx bg-white box-border pt-26rpx px-64rpx absolute bottom-0 left-0 text-26rpx bg-#333333">
      <view class="flex items-center">
        <up-icon name="account" size="46rpx" color="#333333" />
        <text class="ml-30rpx">我的排名</text>
        <text class="ml-auto mr-16rpx">分数：66</text>
      </view>
    </view>
    <!-- 底部我的排名E -->
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import imagesConstants from '@/config/images.constants'

const showOneSelect = ref(false)
const showTwoSelect = ref(false)

onMounted(() => {})
</script>

<style lang="scss" scoped>
// 样式穿透u-cell
:global(.u-cell__body) {
  padding: 30rpx 0 !important;
}

.rangking-container {
  width: 100%;
  height: 100vh;
  background: #459af7;
  display: flex;
  flex-direction: column;

  .top-select {
    position: relative;
    padding: 8rpx 26rpx;
    background: rgba(255, 255, 255, 0.12);
    border-radius: 999rpx;
    display: flex;
    align-items: center;

    .select-name {
      color: #fff;
      font-size: 28rpx;
      margin-right: 20rpx;
    }

    .select-content {
      position: absolute;
      background: white;
      top: 100%;
      border-radius: 20rpx;
      z-index: 999;
      width: 90%;
      margin: 0 auto;
      height: 300rpx;
    }
  }
}
</style>
