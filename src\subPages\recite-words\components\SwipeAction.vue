<template>
  <view class="swipe-action" ref="this_">
    <view
      class="swipe-container"
      @touchstart="onTouchStart"
      @touchmove="onTouchMove"
      @touchend="onTouchEnd"
      :style="{ transform: `translateX(${translateX}px)` }">
      <!-- 左侧操作按钮 -->
      <view v-if="leftActions.length" class="action-buttons left-actions">
        <view
          v-for="(action, index) in leftActions"
          :key="`left-${index}`"
          class="action-button"
          :style="action.style"
          @click="onActionClick('left', index, action)">
          <u-icon color="inherit" v-if="action.icon" :name="action.icon" size="20"></u-icon>
          <text>{{ action.text }}</text>
        </view>
      </view>

      <!-- 主要内容 -->
      <view class="main-content">
        <slot></slot>
      </view>

      <!-- 右侧操作按钮 -->
      <view v-if="rightActions.length" class="action-buttons right-actions">
        <view
          v-for="(action, index) in rightActions"
          :key="`right-${index}`"
          class="action-button"
          :style="action.style"
          @click="onActionClick('right', index, action)">
          <u-icon color="inherit" size="inherit" v-if="action.icon" :name="action.icon"></u-icon>
          <text>{{ action.text }}</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
interface ActionItem {
  text: string
  icon?: string
  style?: Record<string, any>
}

interface Props {
  leftActions?: ActionItem[]
  rightActions?: ActionItem[]
  threshold?: number
  autoClose?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  leftActions: () => [],
  rightActions: () => [],
  threshold: 60,
  autoClose: true,
})

const emit = defineEmits<{
  click: [direction: 'left' | 'right', index: number, action: ActionItem]
}>()

// 触摸相关状态
const startX = ref(0)
const translateX = ref(0)
const isMoving = ref(false)

// 用于存储实际测量的按钮宽度
const leftActionsWidth = ref(0)
const rightActionsWidth = ref(0)

// 动态计算最大滑动距离
const maxLeftDistance = computed(() => {
  return leftActionsWidth.value || 0
})

const maxRightDistance = computed(() => {
  return rightActionsWidth.value || 0
})

// 获取当前组件实例
const instance = getCurrentInstance()

// 测量按钮实际宽度的函数
function measureButtonsWidth() {
  if (!instance) {
    console.warn('无法获取组件实例')
    return
  }

  nextTick(() => {
    // 测量左侧按钮宽度
    if (props.leftActions.length) {
      const leftQuery = uni.createSelectorQuery().in(instance)
      leftQuery
        .select('.left-actions')
        .boundingClientRect(result => {
          if (result) {
            if (Array.isArray(result)) {
              leftActionsWidth.value = result[0]?.width || 0
            } else {
              leftActionsWidth.value = (result as any).width || 0
            }
          }
        })
        .exec()
    }

    // 测量右侧按钮宽度
    if (props.rightActions.length) {
      const rightQuery = uni.createSelectorQuery().in(instance)
      rightQuery
        .select('.right-actions')
        .boundingClientRect(result => {
          if (result) {
            if (Array.isArray(result)) {
              rightActionsWidth.value = result[0]?.width || 0
            } else {
              rightActionsWidth.value = (result as any).width || 0
            }
          }
        })
        .exec()
    }
  })
}

// 监听props变化，重新测量
watch(
  [() => props.leftActions, () => props.rightActions],
  () => {
    measureButtonsWidth()
  },
  { immediate: true }
)

// 组件挂载后测量
onMounted(() => {
  measureButtonsWidth()
})

// 触摸开始
function onTouchStart(e: TouchEvent) {
  startX.value = e.touches[0].clientX
  isMoving.value = true
}

// 触摸移动
function onTouchMove(e: TouchEvent) {
  if (!isMoving.value) return

  const currentX = e.touches[0].clientX
  const deltaX = currentX - startX.value

  // 精确的滑动范围控制
  if (deltaX > 0 && props.leftActions.length && maxLeftDistance.value > 0) {
    // 向右滑动，显示左侧按钮
    translateX.value = Math.min(deltaX, maxLeftDistance.value)
  } else if (deltaX < 0 && props.rightActions.length && maxRightDistance.value > 0) {
    // 向左滑动，显示右侧按钮
    translateX.value = Math.max(deltaX, -maxRightDistance.value)
  } else {
    // 没有对应方向的按钮或宽度未测量完成时，不允许滑动
    translateX.value = 0
  }
}

// 触摸结束
function onTouchEnd() {
  if (!isMoving.value) return
  isMoving.value = false

  // 判断是否达到阈值
  if (Math.abs(translateX.value) < props.threshold) {
    // 未达到阈值，回弹
    translateX.value = 0
  } else {
    // 达到阈值，吸附到完全展开状态
    if (translateX.value > 0) {
      translateX.value = maxLeftDistance.value
    } else {
      translateX.value = -maxRightDistance.value
    }
  }
}

// 点击操作按钮
function onActionClick(direction: 'left' | 'right', index: number, action: ActionItem) {
  emit('click', direction, index, action)

  if (props.autoClose) {
    close()
  }
}

// 关闭滑动
function close() {
  translateX.value = 0
}

// 暴露方法给父组件
defineExpose({
  close,
})
</script>

<style lang="scss" scoped>
.swipe-action {
  position: relative;
  overflow: hidden;
  border-radius: 20rpx;

  .swipe-container {
    position: relative;
    display: flex;
    align-items: center;
    transition: transform 0.3s ease;
    .main-content {
      flex: 1;
      background-color: white;
      z-index: 2;
      border-radius: 20rpx;
      position: relative;
      overflow: hidden;
    }

    .action-buttons {
      position: absolute;
      top: 0;
      bottom: 0;
      display: flex;
      align-items: center;
      z-index: 1;

      &.left-actions {
        left: 20rpx;
        transform: translateX(-100%);
      }

      &.right-actions {
        right: 20rpx;
        transform: translateX(100%);
      }

      .action-button {
        min-width: 80rpx;
        padding: 0 40rpx;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 12px;
        white-space: nowrap; // 防止文字换行

        text {
          margin-left: 8rpx; // 图标和文字之间的间距
        }
      }

      // 左侧按钮的圆角：最左边圆角，最右边直角
      &.left-actions {
        .action-button {
          &:first-child {
            border-radius: 10px 0 0 10px; // 左边圆角
          }
          &:last-child {
            border-radius: 0; // 右边直角（贴着主内容）
          }
          // 如果只有一个按钮
          &:only-child {
            border-radius: 10px 0 0 10px;
          }
        }
      }

      // 右侧按钮的圆角：最右边圆角，最左边直角
      &.right-actions {
        .action-button {
          &:first-child {
            border-radius: 0; // 左边直角（贴着主内容）
          }
          &:last-child {
            border-radius: 0 10px 10px 0; // 右边圆角
          }
          // 如果只有一个按钮
          &:only-child {
            border-radius: 0 10px 10px 0;
          }
        }
      }
    }
  }
}
</style>
