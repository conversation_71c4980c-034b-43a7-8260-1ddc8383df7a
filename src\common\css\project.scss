view {
  font-size: 28rpx;
}
// 容器样式
.container {
  &:not(.wrap) {
    height: 100vh;
    overflow: auto;
  }
  // background-color: $u-bg-color;
  &.shadow::before {
    position: absolute;
    content: '';
    width: 100%;
    top: -20rpx;
    left: 0;
    height: 20rpx;
    z-index: 9999;
    box-shadow: 0 0 20rpx 0 #ddd;
  }
  &.wrap {
    height: fit-content;
    min-height: 100vh;
  }
}
.container:not([class*='-pages']),
.safe-area {
  box-sizing: border-box;
  padding-bottom: constant(safe-area-inset-bottom) !important;
  padding-bottom: env(safe-area-inset-bottom) !important;
}
.safe-area-margin {
  margin-bottom: constant(safe-area-inset-bottom) !important;
  margin-bottom: env(safe-area-inset-bottom) !important;
}
//标题栏
.section {
  position: relative;
  padding: 16rpx 0;
  line-height: 1;
  padding-left: 0.8em;
  font-size: 32rpx;
  color: $u-main-color;
  margin-bottom: 12rpx;

  &::before {
    position: absolute;
    content: '';
    width: 6rpx;
    height: 30rpx;
    background: $u-theme-color;
    border-radius: 5px;
    left: 0;
    top: 0;
    bottom: 0;
    margin: auto;
  }
}
button::after {
  content: none; //去除按钮黑边
}
.cell-item {
  padding: 24rpx 30rpx;
  font-size: 30rpx;
  color: $uni-text-color;
  margin-bottom: 20rpx;
  line-height: 36rpx;
  &::after {
    font-family: uicon-iconfont;
    content: '\e605';
    float: right;
  }
}

// 包含icon和文字的view
.icon-text {
  line-height: 1;
  > view {
    vertical-align: middle;
    display: inline-flex !important;
    margin: 0 4rpx;
  }
  > text {
    vertical-align: middle;
    line-height: 1;
  }
}
%custom-icon {
  display: inline-block;
  font-family: uicon-iconfont;
  color: inherit;
  font-size: inherit;
}
.custom-icon-after::after,
.custom-icon-before::before {
  @extend %custom-icon;
}
// 尾部带箭头,active激活箭头向下方向
.arrow,
.arrow-fill {
  &::after {
    @extend %custom-icon;
    margin-left: 16rpx;
    font-size: 24rpx;
    color: #666;
    transition: transform 0.3s;
  }
}
.arrow::after {
  content: '\e60d';
}
.arrow.active::after {
  transform: rotate(180deg) translate(4rpx, -1rpx);
}
.arrow-fill::after {
  content: '\e6b0';
}
.arrow-fill.active::after {
  transform: rotate(180deg);
}

// 圆角按钮
.circle-button {
  width: 90%;
  margin: 0 auto;
  border-radius: 25px;
  font-size: 30rpx;
  line-height: 2.2;
  background-color: transparent;
  &:not(.plain) {
    background-color: $u-theme-color;
    color: #fff;
  }
  &.plain {
    color: $u-theme-color;
    border: 1px solid $u-theme-color;
  }
}

$tags-height: 40rpx;
// 标签盒
.tags-box {
  display: flex;
  flex-wrap: wrap;
  overflow: hidden;
  gap: 12rpx 16rpx;
  min-height: $tags-height + 10rpx;
  color: #fcb138;
}

//标签项
.tags-item {
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 4rpx;
  height: $tags-height;
  background: #fcb13833;
  padding: 0 12rpx;
  line-height: $tags-height;
  font-size: 22rpx;
  .u-icon {
    margin-right: 0.25em;
  }
}

// 提示文本
.hint-text {
  padding: 0.3em 1em;
  margin-bottom: 24rpx;

  text {
    margin-left: 0.5em;
    color: red;
  }
}

//骨架屏
.skeleton {
  background-color: #fff;

  .skeleton-item {
    position: relative;
    overflow: hidden;
  }

  .skeleton-item::before {
    position: absolute;
    width: 100%;
    height: 100%;
    left: 0;
    top: 0;
    /* #ifdef APP-NVUE */
    background-color: #f1f2f4;
    /* #endif */
    /* #ifndef APP-NVUE */
    background: linear-gradient(90deg, #f1f2f4 25%, #e6e6e6 37%, #f1f2f4 50%);
    background-size: 400% 100%;
    /* #endif */
    animation: skeleton 1.8s ease infinite;
    z-index: 2023;
  }

  &.loading .skeleton-item {
    image {
      visibility: hidden;
    }

    &::before {
      content: '';
    }
  }

  @keyframes skeleton {
    0% {
      background-position: 100% 50%;
    }

    100% {
      background-position: 0 50%;
    }
  }
}

.state-dot {
  margin-right: 10rpx;
  width: 24rpx;
  height: 24rpx;
  border-radius: 50%;
  background-color: var(--status-color);
}

.text-state {
  color: $u-theme-color;
  &.correct {
    color: #61c5a1;
  }
  &.wrong {
    color: #ff776a;
  }
  &.warn {
    color: #ffa600;
  }
}

.bg-state {
  color: #fff;
  background-color: #f0f1f2;
  &.primary {
    background-color: $u-theme-color;
  }
  &.correct {
    background-color: #61c5a1;
  }
  &.wrong {
    background-color: #ff776a;
  }
  &.warn {
    background-color: #ffa600;
  }
}
//课程价格
.course-price {
  color: #ff776a;
  > text {
    font-size: 36rpx;
  }
}
//打卡行块
.clock-in-line {
  padding: 24rpx;
  border-radius: 20rpx;
  margin-bottom: 30rpx;
  background: #ffffff;
  min-height: 120rpx;
  margin-bottom: 20rpx;
  box-shadow: 0rpx 4rpx 16rpx 1rpx #8f8f8f21;
}
//表单输入颜色
.input-color {
  color: #969dab;
}

.dot-mark {
  --status-color: #969dab;
  &::before {
    width: 16rpx;
    height: 16rpx;
    content: '';
    border-radius: 50%;
    background-color: var(--status-color);
    border: 6rpx solid #fff;
    box-shadow: 0 0 0 4rpx var(--status-color);
  }
}

.default-shadow {
  box-shadow: 0rpx 4rpx 16rpx 1rpx #8f8f8f21;
}
