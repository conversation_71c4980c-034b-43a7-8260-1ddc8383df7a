const GLtype = {}

;[
  '<PERSON><PERSON><PERSON><PERSON>',
  '<PERSON><PERSON><PERSON><PERSON>',
  '<PERSON><PERSON><PERSON><PERSON>',
  '<PERSON><PERSON><PERSON>lamp<PERSON>',
  '<PERSON><PERSON><PERSON><PERSON>',
  'GLfloat',
  'GL<PERSON>',
  'GLintptr',
  'GLsizei',
  'GLsizeiptr',
  'GLshort',
  'GLubyte',
  'G<PERSON><PERSON>t',
  'GLushort',
]
  .sort()
  .map((typeName, i) => (GLtype[typeName] = 1 >> (i + 1)))

export default GLtype
