<template>
  <view class="flex justify-between items-center m-y-30rpx">
    <view class="w70rpx h70rpx rounded-full bg-#edf2fe flex flex-center">
      <image :src="handle(itype)" class="w-36rpx h-36rpx" mode="aspectFit"></image>
    </view>
    <view class="text-28rpx text-#333333 fw-bold flex-1 pl30rpx box-border">{{ title }}</view>
    <view
      class="w160rpx h62rpx bg-#459af7 rounded-37rpx text-center lh-60rpx text-white text-28rpx"
      :class="status ? 'complete' : ''"
      @click="toServe()"
      >{{ !status ? '去打卡' : '去查看' }}</view
    >
  </view>
</template>

<script setup lang="ts" name="clock-in-taskitem">
import {
  getDistributeTaskSituationData,
  updateDistributeTaskSituationData,
} from '@/api/project/clock-in'
import type { IDistributeTask } from '@/api/project/clock-in/type'
import imagesConstants from '@/config/images.constants'
import { getSignInfo, getTestHistoryDetail } from '@/api/project/exercises/index'
import { getClockInRecordsDataList } from '@/api/project/clock-in/index'
import useUserStore from '@/store/modules/user'
import { formatHours, showToast } from '@/utils'

const props = defineProps<{
  completeId?: string
  sectionId?: string
  topicId?: string
  catalogId?: string
  taskId: string
  title: string
  wordId?: string
  reportId?: string
  status?: boolean
  itype: '拍照打卡' | '在线学习' | '在线刷题' | '专项打卡' | '单词打卡'
}>()

const userStore = useUserStore()

const handle = (val: string) => {
  if (val === '拍照打卡') {
    return imagesConstants.photo
  }
  if (val === '在线学习') {
    return imagesConstants.online
  }
  if (val === '在线刷题') {
    return imagesConstants.bruQues
  }
  if (val === '专项打卡') {
    return imagesConstants.bruQues
  }
  return imagesConstants.bruQues
}

const toServe = async () => {
  if (!props.status) {
    if (props.itype === '拍照打卡') {
      uni.navigateTo({
        url: `/subPages/clock-in/clock-in?taskId=${props.taskId}`,
      })
    }
    if (props.itype === '在线学习' || props.itype === '在线刷题') {
      // 刷题or学习的打卡完成回调
      uni.$once('clockIn', async (times: any) => {
        const list = (await getDistributeTaskSituationData(props.taskId)).data.list
        const data: IDistributeTask = {
          sum_date: formatHours(times),
          user_id: userStore.userInfo!.id,
          user_name: userStore.userInfo!.realName,
          task_id: props.taskId,
          task_name: props.title,
          task_date: '',
          is_commit: props.itype === '在线刷题' ? '是' : times >= 60 ? '是' : '否',
        }
        if (list.length > 0) {
          data._id = list[0]._id
        }
        updateDistributeTaskSituationData(data).then(res => {
          showToast('打卡成功')
        })
      })
    }
    if (props.itype === '在线学习') {
      uni.navigateTo({
        url: `/subPages/course/detail?id=${props.sectionId}`,
      })
    }
    if (props.itype === '在线刷题') {
      uni.navigateTo({
        url: `/subPages/exercises/answer-sheet?type=test&examId=${props.topicId}&title=打卡刷题&answerType=打卡任务`,
      })
    }
    if (props.itype === '专项打卡') {
      // 专项打卡的打卡完成回调
      uni.$once('special', async () => {
        const list = (await getDistributeTaskSituationData(props.taskId)).data.list
        const data: IDistributeTask = {
          sum_date: formatHours(0),
          user_id: userStore.userInfo!.id,
          user_name: userStore.userInfo!.realName,
          task_id: props.taskId,
          task_name: props.title,
          task_date: '',
          is_commit: '是',
        }
        if (list.length > 0) {
          data._id = list[0]._id
        }
        updateDistributeTaskSituationData(data).then(res => {
          showToast('打卡成功')
        })
      })
      uni.navigateTo({
        url: `/subPages/special-marking/special1?topicId=${props.topicId}`,
      })
    }
    if (props.itype === '单词打卡') {
      uni.navigateTo({
        url: `/subPages/recite-words/word-clock?wordId=${props.wordId}&taskId=${props.taskId}&title=${props.title}&book=${props.topicId}`,
      })
    }
  } else {
    if (props.itype === '单词打卡') {
      const reportId = (await getSignInfo(props.taskId)).data.list[0].report_id
      uni.navigateTo({
        url: `/subPages/recite-words/word-report?reportId=${reportId}`,
      })
    }
    if (props.itype === '在线学习') {
      uni.navigateTo({
        url: `/subPages/course/detail?id=${props.sectionId}`,
      })
    }
    if (props.itype === '在线刷题') {
      getTestHistoryDetail(props.completeId as string).then(e => {
        uni.navigateTo({
          url: `/subPages/exercises/test-report?reportId=${encodeURIComponent(
            JSON.stringify(e.data.list[0])
          )}`,
        })
      })
    }
    if (props.itype === '拍照打卡') {
      uni.navigateTo({
        url: `/subPages/clock-in/clock-in?taskId=${props.taskId}`,
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.complete {
  background-color: #ecf5ff;
  color: #459af7;
}
</style>
