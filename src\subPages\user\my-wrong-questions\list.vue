<template>
  <view class="bg-#f5f5f5 min-h-100vh">
    <!-- 筛选条件 -->
    <view class="bg-white px-93rpx py-20rpx">
      <view class="flex items-center justify-between">
        <view class="flex items-center" @click="showSubjectPicker">
          <text class="text-28rpx color-#333 whitespace-nowrap">{{ selectedSubject }}</text>
          <text class="text-20rpx color-#B7B7B7 ml-12rpx">▼</text>
        </view>
        <view class="flex items-center" @click="showTimePicker">
          <text class="text-28rpx color-#333 whitespace-nowrap">{{ selectedTime }}</text>
          <text class="text-20rpx color-#B7B7B7 ml-12rpx">▼</text>
        </view>
        <!-- <view class="flex items-center" @click="showFilterPicker">
          <text class="text-28rpx color-#333 whitespace-nowrap">{{ selectedFilter }}</text>
          <text class="text-20rpx color-#B7B7B7 ml-12rpx">▼</text>
        </view> -->
      </view>
    </view>

    <view class="px-20rpx">
      <!-- 加载状态 -->
      <view v-if="loading" class="flex flex-col items-center justify-center py-120rpx">
        <text class="text-32rpx color-#999 mb-20rpx">加载中...</text>
      </view>

      <!-- 空状态 -->
      <view
        v-else-if="questionList.length === 0"
        class="flex flex-col items-center justify-center py-120rpx">
        <text class="text-32rpx color-#999 mb-20rpx">暂无错题</text>
        <text class="text-26rpx color-#ccc">请调整筛选条件或添加错题</text>
      </view>

      <!-- 使用时间分组组件 -->
      <QuestionTimeGroup
        v-else
        v-for="(group, index) in questionGroups"
        :key="index"
        :time-label="group.timeLabel"
        :question-list="group.questions" />
    </view>

    <!-- 科目选择器 -->
    <u-popup :show="showSubjectPickerModal" mode="bottom" round="20" @close="hideSubjectPicker">
      <view class="bg-white">
        <view class="flex items-center justify-between px-32rpx py-24rpx border-b border-#f0f0f0">
          <text class="text-28rpx color-#999" @click="hideSubjectPicker">取消</text>
          <text class="text-32rpx color-#333 font-500">选择科目</text>
          <text class="text-28rpx color-#007AFF" @click="confirmSubject">确定</text>
        </view>
        <picker-view :value="subjectPickerValue" class="h-400rpx" @change="onSubjectPickerChange">
          <picker-view-column>
            <view
              v-for="(subject, index) in subjectOptions"
              :key="index"
              class="flex items-center justify-center h-80rpx">
              <text class="text-30rpx color-#333">{{ subject }}</text>
            </view>
          </picker-view-column>
        </picker-view>
      </view>
    </u-popup>

    <!-- 时间选择器 -->
    <u-datetime-picker
      v-model="datetimePickerValue"
      :show="showTimePickerModal"
      mode="datetime"
      :max-date="maxDate"
      :formatter="timeFormatter"
      :close-on-click-overlay="true"
      title="选择时间"
      confirm-text="确定"
      cancel-text="取消"
      @confirm="confirmDateTime"
      @cancel="hideTimePicker"
      @close="hideTimePicker" />

    <!-- 筛选选择器 -->
    <!-- <u-popup :show="showFilterPickerModal" mode="bottom" round="20" @close="hideFilterPicker">
      <view class="bg-white">
        <view class="flex items-center justify-between px-32rpx py-24rpx border-b border-#f0f0f0">
          <text class="text-28rpx color-#999" @click="hideFilterPicker">取消</text>
          <text class="text-32rpx color-#333 font-500">筛选条件</text>
          <text class="text-28rpx color-#007AFF" @click="confirmFilter">确定</text>
        </view>
        <picker-view :value="filterPickerValue" @change="onFilterPickerChange" class="h-400rpx">
          <picker-view-column>
            <view
              v-for="(filter, index) in filterOptions"
              :key="index"
              class="flex items-center justify-center h-80rpx">
              <text class="text-30rpx color-#333">{{ filter }}</text>
            </view>
          </picker-view-column>
        </picker-view>
      </view>
    </u-popup> -->
  </view>
</template>

<script setup lang="ts" name="wrong-questions-list">
import QuestionTimeGroup from './components/QuestionTimeGroup.vue'
import useDateFormatter from '@/hooks/useDateFormatter'
// 时间格式化工具
const { formatDate } = useDateFormatter('YYYY年MM月DD日 HH:mm')

/* ------------------------ 筛选器相关 ------------------------ */
// 选择器状态
const selectedSubject = ref('科目')
const selectedTime = ref('时间')
// const selectedFilter = ref('筛选')

// 弹窗显示状态
const showSubjectPickerModal = ref(false)
const showTimePickerModal = ref(false)
// const showFilterPickerModal = ref(false)

// Picker 科目选项数据
const subjectOptions = ref([
  '全部科目',
  '数学',
  '语文',
  '英语',
  '物理',
  '化学',
  '生物',
  '地理',
  '历史',
  '品德与社会',
  '道德与法治',
  '科学',
  '体育',
  '音乐',
  '政治',
  '信息技术',
  '其他',
])

// const filterOptions = ref([
//   '全部题目',
//   '单选题',
//   '多选题',
//   '判断题',
//   '填空题',
//   '简答题',
//   '计算题',
//   '应用题',
// ])

// 选择器当前值
const subjectPickerValue = ref([0]) // 默认选中第一项
const datetimePickerValue = ref(new Date().getTime()) // 当前时间
// const filterPickerValue = ref([0]) // 默认选中第一项

// 时间选择器配置
const maxDate = ref(Date.now()) // 限制最大时间为当前时间
// 时间格式化 - 添加中文单位
const timeFormatter = (type: string, value: string) => {
  const formatMap: Record<string, string> = {
    year: '年',
    month: '月',
    day: '日',
    hour: '时',
    minute: '分',
    second: '秒',
  }
  return formatMap[type] ? `${value}${formatMap[type]}` : value
}
/* ------------------------ 科目选择器 ------------------------ */
function showSubjectPicker() {
  showSubjectPickerModal.value = true
}
function hideSubjectPicker() {
  showSubjectPickerModal.value = false
}
function onSubjectPickerChange(e: any) {
  const index = e.detail.value[0]
  subjectPickerValue.value = [index]
}
// 选择科目
function confirmSubject() {
  const index = subjectPickerValue.value[0]
  const selectedOption = subjectOptions.value[index]
  // 选择"全部科目"时显示"科目"
  selectedSubject.value = selectedOption === '全部科目' ? '科目' : selectedOption
  showSubjectPickerModal.value = false
  // 重新加载数据
  loadWrongQuestions()
}

/* ------------------------ 时间选择器 ------------------------ */
function showTimePicker() {
  showTimePickerModal.value = true
}
function hideTimePicker() {
  showTimePickerModal.value = false
  // 重置时间筛选
  resetTimeFilter()
}
// 重置时间筛选
function resetTimeFilter() {
  selectedTime.value = '时间'
  datetimePickerValue.value = new Date().getTime()
  // 重新加载数据
  loadWrongQuestions()
}
// 选择时间
function confirmDateTime(e: any) {
  const timestamp = e.value
  datetimePickerValue.value = timestamp
  selectedTime.value = formatDate(timestamp)
  showTimePickerModal.value = false
  // 重新加载数据
  loadWrongQuestions()
}

// 筛选选择器
// function showFilterPicker() {
//   showFilterPickerModal.value = true
// }

// function hideFilterPicker() {
//   showFilterPickerModal.value = false
// }

// function onFilterPickerChange(e: any) {
//   const index = e.detail.value[0]
//   filterPickerValue.value = [index]
// }

// function confirmFilter() {
//   const index = filterPickerValue.value[0]
//   const selectedOption = filterOptions.value[index]
//   // 选择"全部题目"时显示"筛选"
//   selectedFilter.value = selectedOption === '全部题目' ? '筛选' : selectedOption
//   showFilterPickerModal.value = false
// }

onLoad(options => {
  // 接收从menu页面传递的subject参数
  if (options?.subject) {
    selectedSubject.value = options.subject

    // 同步更新科目选择器的选中状态
    nextTick(() => {
      updateSubjectPickerValue(options.subject)
    })
  }
})

import { getWrongExercises } from '@/api/project/exercises'
import type { WrongExercises } from '@/api/project/exercises/type'
// 更新科目选择器的选中状态
function updateSubjectPickerValue(subject: string) {
  const index = subjectOptions.value.findIndex(option => option === subject)
  if (index !== -1) {
    subjectPickerValue.value = [index]
  }
}

/* ------------------------ 数据加载相关 ------------------------ */
// 题目分组数据结构
interface QuestionGroup {
  timeLabel: string
  questions: WrongExercises[]
}

// 加载状态
const loading = ref(false)
// 错误状态
const errorMessage = ref('')
// 问题列表
const questionList = ref<WrongExercises[]>([])

onMounted(() => {
  // 获取错题列表数据
  loadWrongQuestions()
})

function loadWrongQuestions() {
  loading.value = true
  errorMessage.value = '' // 清空之前的错误信息

  // 获取当前选择的科目和时间参数
  const subject =
    selectedSubject.value === '科目' || selectedSubject.value === '全部科目'
      ? ''
      : selectedSubject.value

  const selectTime = selectedTime.value === '时间' ? '' : datetimePickerValue.value.toString()

  getWrongExercises(subject, selectTime)
    .then(res => {
      console.log('错题数据:', res)
      if (res.data && Array.isArray(res.data)) {
        // 直接使用接口返回的数据，确保类型完整
        questionList.value = res.data

        errorMessage.value = '' // 成功时清空错误信息
      } else {
        // 如果接口返回数据格式不正确，设置为空数组
        console.warn('接口返回数据格式异常')
        questionList.value = []
        errorMessage.value = ''
      }
    })
    .catch(error => {
      console.error('获取错题失败:', error)
      questionList.value = []

      // 静默处理所有错误，不显示错误信息给用户
      // 500错误通常表示没有数据，404表示接口不存在，都静默处理
      if (error?.code === 500 || error?.code === 404) {
        console.log('暂无错题数据或接口异常')
        errorMessage.value = ''
      } else {
        // 只有真正的网络错误才显示提示
        console.log('网络异常，请稍后重试')
        errorMessage.value = '网络连接异常，请检查网络后重试'
      }
    })
    .finally(() => {
      loading.value = false
    })
}

// 按时间分组的题目数据
const questionGroups = computed<QuestionGroup[]>(() => {
  const now = new Date()
  const today = new Date(now.getFullYear(), now.getMonth(), now.getDate())
  const yesterday = new Date(today.getTime() - 24 * 60 * 60 * 1000)
  const thisWeekStart = new Date(today.getTime() - (today.getDay() || 7) * 24 * 60 * 60 * 1000)
  const lastWeekStart = new Date(thisWeekStart.getTime() - 7 * 24 * 60 * 60 * 1000)
  const thisMonthStart = new Date(now.getFullYear(), now.getMonth(), 1)

  // 初始化分组容器
  const groupsMap = {
    today: [] as WrongExercises[],
    yesterday: [] as WrongExercises[],
    thisWeek: [] as WrongExercises[],
    lastWeek: [] as WrongExercises[],
    thisMonth: [] as WrongExercises[],
    earlier: [] as WrongExercises[],
  }

  // 一次循环完成所有分组
  questionList.value.forEach(question => {
    const questionDate = new Date(question.creatorTime)
    if (questionDate >= today) {
      groupsMap.today.push(question)
    } else if (questionDate >= yesterday) {
      groupsMap.yesterday.push(question)
    } else if (questionDate >= thisWeekStart) {
      groupsMap.thisWeek.push(question)
    } else if (questionDate >= lastWeekStart) {
      groupsMap.lastWeek.push(question)
    } else if (questionDate >= thisMonthStart) {
      groupsMap.thisMonth.push(question)
    } else {
      groupsMap.earlier.push(question)
    }
  })

  // 构建最终分组数据，过滤掉空分组
  const groups: QuestionGroup[] = [
    { timeLabel: '今天', questions: groupsMap.today },
    { timeLabel: '昨天', questions: groupsMap.yesterday },
    { timeLabel: '本周', questions: groupsMap.thisWeek },
    { timeLabel: '上周', questions: groupsMap.lastWeek },
    { timeLabel: '本月', questions: groupsMap.thisMonth },
    { timeLabel: '更早', questions: groupsMap.earlier },
  ]

  return groups.filter(group => group.questions.length > 0)
})
</script>
