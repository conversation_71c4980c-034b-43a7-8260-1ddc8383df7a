<template>
  <view class="word-select">
    <view class="word-body">
      <view class="select-header">
        <view class="spell-no">
          <text class="current">{{ index + 1 }}</text>
          <text class="total"> /{{ wordList.length }}</text>
        </view>
        <view class="familiarize" :style="[familiarizeStyle]" @click="onActionClick('熟词')">
          熟
        </view>
      </view>

      <view class="select-content flex-col flex-center">
        <text class="content-word">{{ currentWord.word || '' }}</text>
        <view class="content-pronunciation flex items-center" @click="playWord(currentWord.word)">
          <view> 英 [{{ currentWord.pronunciation || '' }}] </view>
          <image
            class="audio"
            :src="getSystemImg('6747e9ed0a34815816f11159/6691ea130bb07d7cd6ed36e8')">
          </image>
        </view>
      </view>

      <view class="select-ans">
        <scroll-view scroll-y="true">
          <template v-for="(item, index1) in currentWord.randomList" :key="item._id + index1">
            <view
              class="select-item flex-col justify-center"
              :style="[selectItemStyle(item)]"
              @click="confirmSelect(item)">
              <view v-if="isSelect" class="item-meaning mr-2">
                {{ item.word }}
              </view>
              <view v-for="cItem in item.meaning" :key="cItem" class="item-speech">
                {{ cItem }}
              </view>
            </view>
          </template>
        </scroll-view>
      </view>
    </view>

    <view class="select-foot flex items-center">
      <view class="flex flex-col flex-center" @click="onActionClick('生词')">
        <u-icon :name="starIcon" size="44rpx" :color="starColor"></u-icon>
        <text class="text-24rpx">生词</text>
      </view>
      <view class="flex-1 ml-44rpx">
        <u-button
          type="primary"
          color="rgba(69, 154, 247, 0.20)"
          :disabled="!isSelect"
          :custom-style="{ color: '#459AF7', border: 'none' }"
          text="下一题"
          shape="circle"
          @click="nextWord"></u-button>
      </view>
    </view>

    <u-modal
      :show="showModal"
      title="提示"
      content="单词训练完成，是否查看报告？"
      confirm-text="确认"
      content-text-align="center"
      cancel-text="返回"
      show-cancel-button
      @confirm="onModalConfirm"
      @cancel="onModalCancel">
    </u-modal>
  </view>
</template>

<script setup lang="ts" name="word-select">
import useCommonStore from '@/store/modules/common'
import {
  deleteReciteWordStatus,
  getDistributeWordRandom,
  getReciteWordErrorById,
  getReciteWordRandom,
  insertReciteWordError,
  insertReciteWordStatus,
  submitReciteWord,
  updateReciteWordErrorCount,
  updateReciteWordLog,
  updateReciteWordStatus,
} from '@/api/project/recite-words'
import type {
  IReciteWordInfo,
  IReciteWordRandom,
  SubmitReciteWordType,
} from '@/api/project/recite-words/type'
import { formatHours, getSystemImg, showFailToast } from '@/utils'
import {
  getDistributeTaskSituationData,
  updateDistributeTaskSituationData,
} from '@/api/project/clock-in'
import type { IDistributeTask } from '@/api/project/clock-in/type'
import useUserStore from '@/store/modules/user'

const commonStore = useCommonStore()

type QueryType = {
  name: string
  userDetailId: string
}
const index = ref(0)
const query = ref<any>()
const wordList = ref<IReciteWordRandom[]>([])
const showModal = ref(false)
const isSelect = ref(false)
const userWord = ref('')
const error = ref(false)
const startTime = ref(Date.now())
const record = ref<{
  bookId: string
  type: string
}>()
const answerRecords = ref<
  Array<{
    wordId: string
    right: number
  }>
>([])

const currentWord = computed(() => wordList.value[index.value])
const starColor = computed(() => (currentWord.value?.type === '生词' ? '#FCB138' : '#333'))
const starIcon = computed(() => (currentWord.value?.type === '生词' ? 'star-fill' : 'star'))
const familiarizeStyle = computed(() => {
  if (!currentWord.value) return {}
  const isActive = currentWord.value?.type === '熟词'
  return {
    background: isActive ? '#459AF7' : '#fff',
    color: isActive ? '#fff' : '#333',
    borderColor: isActive ? '#459AF7' : '#333',
  }
})
const userStore = useUserStore()
const reportId = ref('')
onLoad(async (e: any) => {
  query.value = e
  startTime.value = Date.now()
  const instance = getCurrentInstance()!.proxy as any
  const eventChannel = instance.getOpenerEventChannel()

  eventChannel.once('emitRecord', (data: { bookId: string; type: string }) => {
    record.value = data
  })
  let result
  if (e.wordId) {
    result = await getDistributeWordRandom(e.userDetailId)
  } else {
    result = await getReciteWordRandom(e.userDetailId)
  }

  wordList.value = result.data
    .map(item => {
      const { randomList, ...reset } = item
      const hasSomeWord = randomList.some(o => o.word === item.word)
      if (hasSomeWord) return item
      const list = [...randomList.slice(0, 3), reset]
      return {
        ...item,
        randomList: list.sort(() => Math.random() - 0.5),
      }
    })
    .sort(() => Math.random() - 0.5)
  playWord()
})

function calculateAccuracy(): number {
  if (!answerRecords.value.length) return 0
  const correctCount = answerRecords.value.filter(record => record.right === 1).length
  return Number(((correctCount / answerRecords.value.length) * 100).toFixed(2))
}

async function onModalConfirm() {
  uni.redirectTo({ url: `/subPages/recite-words/word-report?reportId=${reportId.value}` })
}

function onModalCancel() {
  uni.navigateBack()
}
// 播放音频
function playWord(word = currentWord.value.word) {
  commonStore.playWord(word)
}

async function nextWord() {
  // 检查 wordList 是否为空
  if (!wordList.value?.length) {
    showFailToast('单词列表为空')
    return
  }

  // 检查 index 是否在有效范围内
  if (index.value >= wordList.value.length) {
    showFailToast('已经是最后一个单词')
    return
  }

  if (index.value === wordList.value.length - 1) {
    try {
      await addStudyReport()
      if (query.value?.wordId) {
        await finishDistribute()
      }
      showModal.value = true
    } catch (error) {
      console.error('提交失败:', error)
      showFailToast('提交失败，请重试')
      return
    }
    return
  }

  try {
    // 重置状态
    isSelect.value = false
    userWord.value = ''
    error.value = false
    index.value++

    // 播放音频
    await playWord()
  } catch (error) {
    console.error('状态重置或播放失败:', error)
    showFailToast('操作失败，请重试')
    // 回滚 index
    index.value--
  }
}

function addAnswerRecord(isCorrect: boolean) {
  const existingIndex = answerRecords.value.findIndex(
    record => record.wordId === currentWord.value._id
  )

  if (existingIndex !== -1) {
    answerRecords.value[existingIndex].right = isCorrect ? 1 : 0
  } else {
    answerRecords.value.push({
      wordId: currentWord.value._id,
      right: isCorrect ? 1 : 0,
    })
  }
}

async function confirmSelect(item: IReciteWordInfo) {
  if (isSelect.value) {
    playWord(item.word)
    return
  }

  playWord()
  isSelect.value = true
  const { word, _id } = currentWord.value
  userWord.value = item.word
  const isCorrect = item.word === word

  addAnswerRecord(isCorrect)

  if (!isCorrect) {
    error.value = true
    const hasErrorWord = await getReciteWordErrorById(_id)
    if (!hasErrorWord.data.list.length) {
      await insertReciteWordError({
        wordId: _id,
        wrongTimes: 1,
        word,
      })
    } else {
      // 更新错误次数
      const { _id, wrongTimes } = hasErrorWord.data.list[0]
      await updateReciteWordErrorCount({
        id: _id,
        wrongTimes: wrongTimes + 1,
      })
    }
  }

  // 日志添加
  updateReciteWordLog({
    wordId: _id,
    correct: isCorrect ? 1 : 0,
    testType: '选择题测试',
  })
}

async function onActionClick(setType: '熟词' | '生词') {
  if (!currentWord.value) return
  const { type, wordbookId, _id, typeId } = currentWord.value
  if (!typeId) {
    const result = await insertReciteWordStatus({
      book: wordbookId,
      wordId: _id,
      type: setType,
    })
    uni.$u.toast(`添加${setType}成功`)
    currentWord.value.typeId = result.data
    currentWord.value.type = setType
  } else if (type === setType) {
    // 取消
    await deleteReciteWordStatus(typeId)
    uni.$u.toast(`取消${setType}成功`)
    delete currentWord.value.type
    delete currentWord.value.typeId
  } else {
    // 更新
    await updateReciteWordStatus({
      id: typeId,
      type: setType,
    })

    uni.$u.toast(`更新${setType}成功`)
    currentWord.value.type = setType
  }
}
function selectItemStyle(item: IReciteWordInfo) {
  if (!isSelect.value) return {}
  if (userWord.value === item.word && error.value) {
    return {
      background: 'rgba(255, 0, 0, 0.10)',
      borderColor: '#E45656',
    }
  } else if (currentWord.value?.word === item.word) {
    return {
      background: '#D9EBFE',
      borderColor: '#459AF7',
    }
  }
  return {}
}

// 提交完成打卡
async function finishDistribute() {
  const list = (await getDistributeTaskSituationData(query.value.taskId)).data.list
  const data: IDistributeTask = {
    sum_date: formatHours(0),
    user_id: userStore.userInfo!.id,
    user_name: userStore.userInfo!.realName,
    task_id: query.value.taskId,
    task_name: query.value.title,
    task_date: '',
    is_commit: '是',
    report_id: reportId.value,
  }
  if (list.length > 0) {
    data._id = list[0]._id
  }
  updateDistributeTaskSituationData(data)
}
// 添加学习报告
async function addStudyReport() {
  if (!record.value?.bookId) return

  const submitData: SubmitReciteWordType = {
    practiceNo: '',
    type: record.value.type,
    state: '提交',
    accuracy: calculateAccuracy(),
    bookID: record.value.bookId,
    tableField106: answerRecords.value,
    answerTime: Math.floor((Date.now() - startTime.value) / 1000),
  }
  const res = await submitReciteWord(submitData)
  reportId.value = res.data
}
</script>

<style lang="scss" scoped>
.word-select {
  background-color: $uni-bg-color;
  height: 100%;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  .word-body {
    padding: 60rpx;
    height: 100%;
    overflow: hidden;
    flex: 1;
    display: flex;
    flex-direction: column;
  }
  .audio {
    width: 25rpx;
    height: 25rpx;
    margin-left: 40rpx;
  }
  .select-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    .spell-no {
      display: flex;
      align-items: center;
      font-size: 32rpx;
      .current {
        color: #333;
      }
      .total {
        color: #666;
      }
    }
    .familiarize {
      border-radius: 10rpx;
      border: 6rpx solid #0f2237;
      height: 46rpx;
      width: 46rpx;
      font-size: 26rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: bold;
    }
  }
  .select-content {
    height: 300rpx;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    .content-word {
      font-weight: 800;
      font-size: 60rpx;
      color: #333333;
    }

    .content-pronunciation {
      background: rgba(69, 154, 247, 0.1);
      border-radius: 24rpx;
      margin-top: 20rpx;
      padding: 8rpx 30rpx;
      font-size: 24rpx;
      color: #999999;
      width: fit-content;
    }
  }
  .select-ans {
    flex: 1;
    overflow: scroll;
    margin-bottom: 80rpx;
    .select-item {
      min-height: 88rpx;
      transition: height 0.3s;
      padding: 20rpx 52rpx;
      font-size: 30rpx;
      color: #333333;
      background: #f5f5f5;
      border-radius: 10rpx 10rpx 10rpx 10rpx;
      border: 1rpx solid #f5f5f5;
      margin-bottom: 25rpx;
      display: flex;
      flex-direction: column;
      .item-meaning {
        color: #333333;
      }
      .item-speech {
        // color: #c9c9c9;
        margin: 5rpx 0;
      }
    }
  }

  .select-foot {
    position: fixed;
    padding: 0 60rpx;
    padding-top: 20rpx;
    bottom: 0;
    width: 100%;
    background-color: #fff;
    box-sizing: border-box;
    padding-bottom: 50rpx;
    ::v-deep .u-button--active {
      background-color: #e8eef5;
    }
  }
}
</style>
