<template>
  <view class="w690rpx min-h-240rpx ma mt30rpx rounded-20rpx bg-white p30rpx box-border box-shadow">
    <view class="w-full h30rpx flex items-center justify-between">
      <view class="flex items-center" @click="toServe">
        <u-icon
          name="calendar"
          :label="formatDate(new Date())"
          label-color="#333333"
          label-size="28rpx"
          size="30"
          space="5px"></u-icon>
        <view class="ml10rpx">
          <u-icon name="arrow-down"></u-icon>
        </view>
      </view>
      <slot name="right"></slot>
    </view>
    <view class="w-full m-y-40rpx flex justify-between">
      <view
        v-for="(item, index) in weekList"
        :key="index"
        class="w84rpx h100rpx rounded-14rpx bg-#ecf5ff flex flex-col flex-center"
        :class="{ sele: selectWeekList.filter(ite => item.date === ite).length !== 0 }"
        @click="seleWeek(item.date)">
        <view class="text-25rpx text-#B4D8FF day">{{ item.eng }}</view>
        <view class="w-full h10rpx"></view>
        <view class="text-24rpx text-#05407F fw-bold day">{{ item.dt }}</view>
      </view>
    </view>
    <slot></slot>
  </view>
  <up-calendar
    v-if="flag"
    :show="show"
    mode="multiple"
    close-on-click-overlay
    :default-date="defaultDate"
    @close="show = false"
    @confirm="seleDate"></up-calendar>
</template>

<script setup lang="ts" name="select-date">
import useDateFormatter from '@/hooks/useDateFormatter'

const props = defineProps<{
  flag?: boolean
  list?: number[]
}>()

const emits = defineEmits(['change', 'select'])

const { formatDate } = useDateFormatter('YYYY-MM-DD')

const show = ref(false)

// 周英文
const weeks = ref(['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'])

// 周列表
const weekList = computed(() => {
  const now = new Date()
  let month = now.getMonth()
  let nowDay = now.getDay()
  let nowDate = now.getDate()
  const list: any = []
  weeks.value.forEach((item, index) => {
    list.push({
      eng: weeks.value[nowDay],
      dt: nowDate,
      date: new Date(`${now.getFullYear()}/${now.getMonth() + 1}/${nowDate}`).getTime(),
    })
    if (nowDay === 6) {
      nowDay = 0
    } else {
      nowDay++
    }
    now.setDate(nowDate + 1)
    if (now.getMonth() !== month) {
      nowDate = 1
      month = now.getMonth()
    } else {
      nowDate++
    }
  })
  return list
})
const selectWeekList = ref<number[]>([])
selectWeekList.value = props.list || []

const defaultDate = computed(() => {
  const list: string[] = []
  selectWeekList.value.forEach(item => {
    const date = new Date(item)
    date.setHours(0, 0, 0, 0)
    list.push(`${date.getFullYear()}-${date.getMonth() + 1}-${date.getDate()}`)
  })
  return list
})

const toServe = (val: any) => {
  if (!props.flag) {
    uni.navigateTo({
      url: `/subPages/clock-in/record?times=${val}`,
    })
    return 0
  }
  show.value = true
}

const seleWeek = (val: number) => {
  emits('select', val)
  if (props.flag) {
    if (selectWeekList.value.filter(item => item === val).length > 0) {
      selectWeekList.value = selectWeekList.value.filter(item => item !== val)
    } else {
      selectWeekList.value.push(val)
    }
    emits('change', selectWeekList.value)
  } else {
    return 0
  }
}

const seleDate = (val: any) => {
  selectWeekList.value.length = 0
  val.forEach((item: string, index: number) => {
    selectWeekList.value.push(new Date(item).setHours(0, 0, 0, 0))
  })
  show.value = false
}
</script>

<style lang="scss" scoped>
.sele {
  background-color: #459af7;
  .day {
    color: white;
  }
}
</style>
