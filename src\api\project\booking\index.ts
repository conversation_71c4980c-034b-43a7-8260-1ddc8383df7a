import type * as ReciteType from './type.d'
import { createModelData, getModelList, updateModelData } from '@/api/visual'

export function getBookCourseList(startDate: number, endDate: number) {
  return getModelList<ReciteType.BookCourseListItem>({
    menuId: '66b5849569fdde293b820b99',
    pageSize: -1,
    filter: [
      {
        enCode: 'start_class_date',
        method: 'range',
        type: 'custom',
        value: [startDate, endDate],
      },
    ],
  })
}
export function getMyActiveList() {
  return getModelList<ReciteType.BookingListItem>({
    menuId: '66b58d1369fdde293b820b9b',
    pageSize: -1,
    filter: [
      {
        enCode: 'creatorUserId',
        method: 'eq',
        type: 'systemField',
        value: ['currentUser'],
      },
    ],
  })
}

export function clockIn(id: any) {
  return updateModelData({
    menuId: '66b58d1369fdde293b820b9b',
    _id: id,
    data: JSON.stringify({ type: 1 }),
  })
}
export function createBooking(data: any) {
  return createModelData({
    menuId: '66b58d1369fdde293b820b9b',
    data: JSON.stringify({
      chapter: data.chapter,
      locations: data.locations,
      course_id: data._id,
      course_name: data.course_name,
      endTime: data.endTime,
      startTime: data.startTime,
      school: data.school,
      start_class_date: data.start_class_date,
      timeRange: data.timeRange,
    }),
  })
}
export function getBookingList(filter: any) {
  return getModelList<ReciteType.MyBookingListItem>({
    menuId: '66b58d1369fdde293b820b9b',
    pageSize: -1,
    connect: 'and',
    filter,
    sort: { creatorTime: 'desc' },
  })
}
