<template>
  <view v-if="Object.keys(detail).length !== 0" class="w-full">
    <u-image :src="assembleImgData(detail?.school_banner?.[0] || '')" width="100%" height="300rpx">
    </u-image>
  </view>
  <SchoolItem
    :item-data="detail"
    :img-size="{
      width: '180rpx',
      height: '180rpx',
    }"
    :flag="true"
    :collect="collect"
    @click-collect="handleCollect" />
  <!-- 学校介绍 -->
  <view v-if="detail?.school_intro" class="mb-30rpx">
    <view class="px20rpx text-22rpx relative c-[#969DAB] lh-[1.6]">
      <text>{{ displayText }}</text>
      <text v-if="showToggleBtn" class="inline-toggle-btn" @tap="toggleExpand">
        {{ isExpanded ? '收起↑' : '更多>' }}
      </text>
    </view>
  </view>
  <view class="flex justify-around m-y-20rpx">
    <view
      v-for="item in detailInfo"
      :key="item.key"
      class="w215rpx h134rpx rounded-10rpx leading-32rpx bg-#F5F5F5 py20rpx px-30rpx box-border text-24rpx text-right">
      <view class="text-#969DAB">{{ item.title }}</view>
      <view class="text-#000000 mt10rpx">
        <text class="text-50rpx text-#333333 fw-bold">
          {{ item.value }}
        </text>
        {{ item.unit }}
      </view>
    </view>
  </view>
  <view class="w-full bg-#f3f4f6 pb10rpx"></view>
  <view class="w-full bg-#f3f4f6 pb20rpx">
    <up-sticky>
      <view class="bg-white mt20rpx sticky">
        <up-tabs
          :list="tabs"
          :item-style="{ width: '350rpx', height: '100rpx', 'font-size': '30rpx' }"
          line-width="80rpx"
          line-height="8rpx"
          :active-style="{ color: '#459AF7', 'font-weight': '700' }"
          :inactive-style="{ color: '#333333' }"
          :current="tabIndex"
          @change="tabChange"></up-tabs>
      </view>
    </up-sticky>
    <swiper :style="`height:${swiperHeight}px`" :current="tabIndex" @change="swiperChange">
      <!-- 招生计划 -->
      <swiper-item>
        <view class="p10rpx">
          <view class="flex justify-between items-center">
            <Section title="招生计划" />
            <view
              class="w132rpx h42rpx flex flex-center rounded-21rpx bg-white text-22rpx text-#333333"
              @click="showYear = !showYear">
              <text>
                {{ currentYear }}
              </text>
              <view
                class="ml10rpx"
                style="transition: transform 0.5s"
                :style="`transform: rotate(${showYear ? 180 : 0}deg);`">
                <u-icon name="arrow-up-fill" size="15"></u-icon>
              </view>
            </view>
          </view>
          <MyTable :titles="studentTitle" :list="studentData" />
          <view class="text-#FC3838 text-18rpx p20rpx">
            该统计是由湖南专升本网报平台的志愿填报前截止数据与学校官方公布的数据整合生成，仅供参考
          </view>
        </view>
      </swiper-item>
      <!-- 考试科目 -->
      <swiper-item>
        <view class="px32rpx">
          <Section title="考试科目" />
          <view>
            <ExamTable :titles="examTitle" :list="examData" />
          </view>
        </view>
      </swiper-item>
      <!-- 简章考纲 -->
      <swiper-item>
        <view class="px32rpx">
          <Section title="简章考纲" />
          <FileItem
            v-for="(item, index) in examSyllabus"
            :key="index"
            :name="item?.[0].name"
            :url="item?.[0].url" />
          <view v-if="examSyllabus?.length === 0" class="mt40rpx">
            <up-empty text="暂无文件"></up-empty>
          </view>
        </view>
      </swiper-item>
    </swiper>
  </view>
  <up-action-sheet
    :actions="listYear"
    title="选择年份"
    :show="showYear"
    @close="showYear = false"
    @select="selectYear"></up-action-sheet>
  <FloatWeixin />
</template>

<script setup lang="ts" name="school-detail">
import SchoolItem from './SchoolItem/index.vue'
import FileItem from './FileItem/index.vue'
import ExamTable from './ExamTable/index.vue'
import FloatWeixin from '@/components/FloatWeixin.vue'
import Section from '@/components/Section.vue'
import MyTable from '@/subPages/index/report-major/MajorTable/index.vue'
import {
  addUserCollectSchool,
  delUserCollectSchool,
  getBKSchoolData,
  getBKSchoolMajorDataList,
  getUserCollectSchool,
} from '@/api/project/index'
import type { IBKSchoolData, IBKSchoolMajorData } from '@/api/project/index/type'
import { assembleImgData, extractNumbers } from '@/utils'

const detail = ref<IBKSchoolData>({} as IBKSchoolData)

// 展开收起相关状态
const isExpanded = ref(false)
const showToggleBtn = ref(true)

// 切换展开收起状态
const toggleExpand = () => {
  isExpanded.value = !isExpanded.value
}

// 显示的文本内容
const displayText = computed(() => {
  if (!detail.value?.school_intro) return ''

  // 提取纯文本内容
  const textContent = detail.value.school_intro.replace(/<[^>]*>/g, '')

  if (showToggleBtn.value && !isExpanded.value) {
    // 收起状态：截断文字并添加省略号
    const maxLength = 110
    if (textContent.length > maxLength) {
      return `${textContent.substring(0, maxLength)}...`
    }
  }

  // 展开状态或内容不长时，返回完整文本
  return textContent
})

// 检查内容是否需要截断
const checkContentLength = () => {
  if (detail.value?.school_intro) {
    const textContent = detail.value.school_intro.replace(/<[^>]*>/g, '')
    // 如果文字长度超过110个字符，显示展开按钮
    showToggleBtn.value = textContent.length > 110
  }
}

// 本科院校专业列表
const majorList = ref<IBKSchoolMajorData[]>([])
// 招生计划表格数据
const studentTitle = ['专业名称', '招生计划', '报考人数', '录取人数', '分数线', '录取率', '学费']
const studentData = ref<string[][]>([])
const listYear = ref<{ name: string }[]>([])
const showYear = ref(false)

const currentYear = ref()
const examTitle = ['专业名称', '考试科目+参考材料']
const examData = ref<
  {
    major: string
    // subject: string
    // books: string
    list: string[]
  }[]
>([])
const handleStudent = () => {
  studentData.value = majorList.value
    .filter(item => item.sum_year === currentYear.value)
    .map(item => {
      return [
        item.Undergraduate_name,
        item.enrollment_plan,
        item.application_nums,
        item.admission_nums,
        item.score,
        item.acceptance_rate,
        item.tuition,
      ]
    })
}
const handleExam = () => {
  examData.value = majorList.value
    .filter(item => item.sum_year === currentYear.value)
    .map(item => {
      return {
        major: item.Undergraduate_name,
        list: item.other_info.split('\n'),
      }
      // return {
      //   major: item.Undergraduate_name,
      //   subject: item.other_info.split('\n')[0],
      //   books: item.other_info.split('\n').splice(1).join(''),
      // }
    })
}
// 简章考纲
const examSyllabus = ref<UploadFzData[][]>([])
const handleSyllabus = () => {
  examSyllabus.value = majorList.value
    .filter(item => item.sum_year === currentYear.value)
    .map(item => {
      return item.annex_files
    })
    .filter(files => {
      return files?.length > 0
    })
}
const selectYear = ({ name }: { name: string }) => {
  currentYear.value = name
  handleStudent()
  handleExam()
  handleSyllabus()
}

// 收藏状态
const collect = ref(false)
const collectId = ref('') // 收藏记录的ID，用于删除

// 检查收藏状态
const checkCollectStatus = async (schoolId: string) => {
  try {
    const res = await getUserCollectSchool(schoolId)
    const collectList = res.data?.list || []
    if (collectList.length > 0) {
      collect.value = true
      collectId.value = collectList[0]._id
    } else {
      collect.value = false
      collectId.value = ''
    }
  } catch (error) {
    console.error('检查收藏状态失败:', error)
    collect.value = false
    collectId.value = ''
  }
}

// 处理收藏/取消收藏
const handleCollect = async () => {
  try {
    if (collect.value) {
      // 取消收藏
      if (collectId.value) {
        await delUserCollectSchool(collectId.value)
        collect.value = false
        collectId.value = ''
        uni.showToast({
          title: '已取消收藏',
          icon: 'success',
        })
      }
    } else {
      // 添加收藏
      const res = await addUserCollectSchool(detail.value._id)
      collect.value = true
      collectId.value = res.data // addUserCollectSchool返回的是创建记录的ID
      uni.showToast({
        title: '收藏成功',
        icon: 'success',
      })
    }
  } catch (error) {
    console.error('收藏操作失败:', error)
    uni.showToast({
      title: '操作失败，请重试',
      icon: 'error',
    })
  }
}

const detailInfo = ref([
  {
    key: 'found_time',
    title: '建校时间',
    value: '1900',
    unit: '年',
  },
  {
    key: 'floor_space',
    title: '占地面积',
    value: '1000',
    unit: '亩',
  },
  {
    key: 'school_ranking',
    title: '高校排行',
    value: '150',
    unit: '名',
  },
])

// tabs导航
const flag = ref(0)
const tabs = ref([
  {
    id: 1,
    name: '招生计划',
    top: 0,
    url: '#plan',
  },
  {
    id: 2,
    name: '考试科目',
    top: 0,
    url: '#exam',
  },
  {
    id: 3,
    name: '简章考纲',
    top: 0,
    url: '#general',
  },
])
// 当前tab
const tabIndex = ref(0)
const tabChange = (val: any) => {
  tabIndex.value = val.index
}
const toId = (id: number, val: any) => {
  if (flag.value++ < 3) {
    tabs.value[id].top = val
  }
}

const swiperChange = (val: any) => {
  tabIndex.value = val.detail.current
}
const swiperHeight = ref(400)

onLoad(async (val: any) => {
  detail.value = (await getBKSchoolData(val.id)).data
  uni.setNavigationBarTitle({
    title: detail.value.school_name,
  })
  detailInfo.value[0].value = extractNumbers(detail.value.found_time).strs
  detailInfo.value[1].value = extractNumbers(detail.value.floor_space).strs
  detailInfo.value[2].value = extractNumbers(detail.value.school_ranking).strs
  majorList.value = (await getBKSchoolMajorDataList(detail.value.school_name)).data.list
  listYear.value = [...new Set(majorList.value.map(item => item.sum_year))].map(item => {
    return {
      name: item,
    }
  })
  currentYear.value = listYear.value[listYear.value.length - 1].name

  handleStudent()

  handleExam()
  handleSyllabus()

  // 检查学校介绍内容长度
  checkContentLength()

  // 检查收藏状态
  await checkCollectStatus(detail.value._id)
})

watchEffect(() => {
  if (tabIndex.value === 0) {
    // 招生计划：表格行数 * 行高 + 标题区域 + 底部说明
    swiperHeight.value =
      studentData.value.length === 0
        ? 300 // 空状态高度
        : studentData.value.length * 80 + 200
  }
  if (tabIndex.value === 1) {
    // 考试科目：数据行数 * 行高 + 标题区域
    swiperHeight.value =
      examData.value.length === 0
        ? 300 // 空状态高度
        : examData.value.length * 120 + 150
  }
  if (tabIndex.value === 2) {
    // 简章考纲：文件数量 * 文件项高度 + 标题区域
    swiperHeight.value =
      examSyllabus.value.length === 0
        ? 300 // 空状态高度
        : examSyllabus.value.length * 80 + 150
  }
})
</script>

<style lang="scss" scoped>
/* 自定义展开收起样式 */
.rich-text-content {
  line-height: 1.6;
  transition: all 0.3s ease;
}

/* 内联按钮样式 */
.inline-toggle-btn {
  cursor: pointer;
  user-select: none;
}
</style>
