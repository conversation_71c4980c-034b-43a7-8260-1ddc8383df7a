<template>
  <view class="w750rpx p30rpx box-border">
    <!-- <view class="text-32rpx text-#333333">试卷ID:{{ paperId }}</view> -->
    <view class="flex flex-wrap mt20rpx gap-30rpx justify-between">
      <view
        v-for="(item, index) in imgList"
        :key="index"
        class="w330rpx h400rpx rounded-20rpx b-8rpx b-#c7e0fd b-solid box-border flex-center">
        <image
          class="w-full h-full"
          :src="getHeadIcon(item.url)"
          @click="previewImage(item.url)"></image>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts" name="clock-in-special">
import { getHeadIcon } from '@/utils'
import type { ISpecialClockIn } from '@/api/project/clock-in/type.d'
import { getSpecialClockInDetail } from '@/api/project/clock-in'

// 专项练习打卡的数据
const specialClockInData = ref<ISpecialClockIn>({
  code: '',
  images: [],
  grade: '',
  subject: '',
  paperId: '',
  special_type: '专项打卡',
})

const paperId = ref('')
const imgList = ref<UploadImgData[]>([
  {
    name: '上传第一页',
    url: '',
  },
  {
    name: '上传第二页',
    url: '',
  },
  {
    name: '上传第三页',
    url: '',
  },
  {
    name: '上传第四页',
    url: '',
  },
])

const getData = async (id: string) => {
  const { data } = await getSpecialClockInDetail(id)
  paperId.value = data.paperId as string
  imgList.value = data.images as unknown as UploadImgData[]
}

const previewImage = (url: string) => {
  uni.previewImage({
    urls: [getHeadIcon(url)],
  })
}

onLoad((e: any) => {
  getData(e.id)
})
</script>

<style lang="scss" scoped></style>
