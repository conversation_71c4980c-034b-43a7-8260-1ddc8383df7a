<template>
  <view v-if="show" class="w710rpx ma mt20rpx bg-white rounded-20rpx p10rpx box-border">
    <LoadMoreList ref="loadMoreList" :request-fn="getPageCourseList" :request-params="{ filter }">
      <template #default="{ list }">
        <CourseItem
          v-for="item in (list as ICourseItemResultType[])"
          :key="item._id"
          :title="item.course_name"
          :tags="item.course_tag"
          :cover="item.course_cover?.[0]"
          :price="item.course_price"
          @tap="toDetail(item._id)" />
      </template>
    </LoadMoreList>
  </view>
</template>

<script setup lang="ts" name="my-course">
import CourseItem from './CourseItem/index.vue'
import { getMemberRight } from '@/api/project/member'
import { getPageCourseList } from '@/api/project/course'
import type { ICourseItemResultType } from '@/api/project/course/type'
import type { FilterType } from '@/es/request'
import LoadMoreList from '@/components/LoadMoreList.vue'

const loadMoreList = ref<InstanceType<typeof LoadMoreList>>()

const filter = ref<FilterType[]>([
  {
    enCode: 'course_no',
    method: 'in',
    type: 'custom',
    value: [],
  },
])

const show = ref(false)

const toDetail = (id: string) => {
  uni.navigateTo({
    url: `/subPages/course/detail?id=${id}`,
  })
}

onLoad(async () => {
  const { data } = await getMemberRight(undefined, '课程')
  const courseNos = data.list.map(item => item.equity_id)
  filter.value[0].value = courseNos
  show.value = true
})
</script>

<style>
page {
  background-color: #f4f5f7;
}
</style>

<style lang="scss" scoped></style>
