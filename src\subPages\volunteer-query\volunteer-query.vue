<template>
  <view
    v-if="flag"
    class="w100vw h100vh absolute top-0rpx left-0rpx z-997"
    @click="flag = false"></view>
  <view class="home-pages container bg-theme">
    <view class="home-header flex-center">
      <view class="home-header__title color-white">
        <text v-if="currentYear">{{ currentYear }}年湖南省普通专升本</text>
        <text>院校专业查询系统</text>
      </view>
      <view class="search-input radius-25 flex justify-center bg-white z-9999">
        <u-input
          v-model="searchValue"
          font-size="28rpx"
          placeholder="请输入想要查询的专科专业"
          shape="circle"
          border="surround"
          @confirm="toJump"
          @focus="flag = true">
          <template #suffix>
            <u-icon class="search-icon" name="search" size="54rpx" @tap="toJump" />
          </template>
        </u-input>
        <view
          v-if="flag"
          class="w560rpx h300rpx box-shadow absolute z-999 bg-white top-100rpx rounded-10rpx">
          <scroll-view scroll-y="true" class="h300rpx" @scrolltolower="loadMore">
            <LoadMoreList
              ref="loadMoreList"
              :request-fn="getZKmajorDataList"
              :request-params="{ filter }">
              <template #default="{ list }">
                <view
                  v-for="item in (list as IZKMajorData[])"
                  :key="item._id"
                  class="p20rpx text-#333333"
                  @click="seleMajor(item.college_name)">
                  <rich-text :nodes="handleLight(item.college_name)"> </rich-text>
                </view>
              </template>
            </LoadMoreList>
          </scroll-view>
        </view>
      </view>
    </view>
    <view class="home-wrap bg-white radius-20 padding-24">
      <view class="home-main">
        <view class="options-warp">
          <view
            v-for="item in options"
            :key="item.id"
            class="options-item flex-center padding-y-24 padding-x-20 radius-25"
            :name="item.title"
            @click="toServe(item.url)">
            <image class="options-icon" :src="getSystemImg(item.icon)" mode="aspectFit" />
            <text class="options-text text-left">{{ item.title }}</text>
          </view>
        </view>
        <image
          class="exercises-ad margin-t-24 default-shadow"
          :src="getIcon('6747e9ed0a34815816f11159/66da9ab2e2014112f21bc716')"
          mode="aspectFit"
          @click="TargetWeChatService" />

        <u-notice-bar :text="noticeInfo" direction="column" :duration="3000" @click="setModel" />
      </view>
      <view class="home-footer margin-auto-top">
        <view class="hint-content">
          <view class="hint-title flex">
            <u-icon name="bell" color="#fcb138" size="42rpx" />
            <text>温馨提示</text>
          </view>
          <view class="hint-line text-note">
            {{ global.indexPrompt }}
          </view>
        </view>
      </view>
      <u-modal
        :show="showModal"
        title="系统公告"
        :content="modelContent"
        @confirm="showModal = false" />
    </view>
  </view>
</template>

<script setup lang="ts">
import { getNoticeDataList, getZKmajorDataList } from '@/api/project/index'
import type { IZKMajorData } from '@/api/project/index/type'
import useIndexStore from '@/store/modules/index'
import LoadMoreList from '@/components/LoadMoreList.vue'
import global from '@/config/constants'
import useUserStore from '@/store/modules/user'
import { TargetWeChatService, getSystemImg, showToast } from '@/utils'
import type { FilterType } from '@/es/request'

const userStore = useUserStore()
const getIcon = (iconId: any) => getSystemImg(iconId)
const loadMoreList = ref<InstanceType<typeof LoadMoreList>>()
const loadMore = () => {
  loadMoreList.value!.onReachBottom()
}

const indexStore = useIndexStore()

// 首页功能
const options = [
  {
    id: 1,
    title: '院校库',
    url: '/subPages/volunteer-query/school/list',
    icon: '/6747e9ed0a34815816f11159/66a709ec1f1cb273f7dbeb2f',
  },
  {
    id: 2,
    title: '专业库',
    url: '/subPages/volunteer-query/major/list',
    icon: '/6747e9ed0a34815816f11159/66a709ec1f1cb273f7dbeb2e',
  },
  {
    id: 3,
    title: '录取分数',
    url: '/subPages/volunteer-query/score/list',
    icon: '/6747e9ed0a34815816f11159/66a709ec1f1cb273f7dbeb32',
  },
  {
    id: 4,
    title: '可报专业',
    url: '/subPages/volunteer-query/search/list',
    icon: '/6747e9ed0a34815816f11159/66a709ec1f1cb273f7dbeb33',
  },
]

// 当前年份
const currentYear = ref()

const toServe = (url: string) => {
  uni.navigateTo({
    url,
  })
}
// 滚动通知
const noticeInfo = ref()
const getNoticeInfo = async () => {
  const {
    data: { list },
  } = await getNoticeDataList()
  noticeInfo.value = list.map(item => item.content)
}
// 公告
const showModal = ref(false)
const modelContent = ref('')
const setModel = (index: number) => {
  modelContent.value = noticeInfo.value[index]
  showModal.value = true
}

// 专业搜索
const searchValue = ref('')
const flag = ref(false)
const filter = ref<FilterType[]>([
  {
    enCode: 'college_name',
    method: 'like',
    type: 'custom',
    value: [searchValue.value],
  },
])
watch(searchValue, () => {
  filter.value[0].value = [searchValue.value]
  loadMoreList.value!.refresh()
})
const toJump = () => {
  const list = (loadMoreList.value?.list || []) as IZKMajorData[]
  if (
    list.filter(item => item.college_name === searchValue.value).length === 0 ||
    list.length === 0
  ) {
    showToast('请输入完整/正确的专业名称')
    return 0
  }
  uni.navigateTo({
    url: `/subPages/volunteer-query/search/list?name=${searchValue.value}`,
  })
}
// 专业关键词替换
const handleLight = (val: string) => {
  const query = searchValue.value
  return val.replace(query, `<span style="color:#459af7">${query}</span>`)
}

const seleMajor = (name: string) => {
  searchValue.value = name
  flag.value = false
  toJump()
}

onLoad(async ({ q }: any) => {
  getNoticeInfo()
  currentYear.value = await indexStore.getCurrentYear()
})

onMounted(async () => {})
</script>

<style lang="scss" scoped>
@import '../../common/css/project.scss';
@import '../../common/css/index.scss';
.exercises-ad {
  width: 100%;
  height: 120rpx;
  border-radius: 20rpx;
}
.home {
  &-header {
    position: relative;
    height: 612rpx;
    background-size: cover;
    background-image: url('https://kindoucloud.com/api/file/previewImage/668b9011e8661c0a3fbe0285/66a6fa281f1cb273f7dbeb29');
    &__title {
      position: absolute;
      bottom: 92rpx;
      > text {
        display: block;
        text-align: center;
        line-height: 1.4;
        &:first-child {
          font-size: 39rpx;
          letter-spacing: 2rpx;
        }
        &:last-child {
          margin-top: 20rpx;
          font-size: 78rpx;
          font-weight: bold;
          letter-spacing: 4rpx;
        }
      }
    }
    ::v-deep .search-input {
      position: absolute;
      bottom: -48rpx;
      width: 580rpx;
      border: 1rpx solid rgb(218, 219, 222);
      box-shadow: 0 5px 6px 1px #69aee640;
      input {
        padding: 10rpx 20rpx;
        // padding-right: 0;
        margin-right: 32rpx;
      }
    }
  }
  &-wrap {
    margin: 0 20rpx;
    margin-bottom: 36rpx;
  }
  &-main {
    margin-top: 84rpx;
    .options {
      &-warp {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 20rpx;
      }
      &-item {
        box-shadow: 0rpx 3rpx 20rpx 1rpx #c5c5c552;
      }
      &-icon {
        width: 48rpx;
        height: 48rpx;
        margin-right: 24rpx;
      }
      &-text {
        width: 4.2em;
        font-size: 30rpx;
        color: #192e46;
      }
    }
    &-ad {
      margin-top: 60rpx;
      margin-bottom: 30rpx;
      height: 120rpx;
      background-image: url('https://kindoucloud.com/api/file/previewImage/668b9011e8661c0a3fbe0285/66a701171f1cb273f7dbeb2b');
      overflow: hidden;
      &.switch {
        .ad-title {
          transform: translateY(-200%);
        }
        .ad-note {
          transform: translateY(200%);
        }
      }
      .ad-title {
        font-size: 29rpx;
        margin-bottom: 10rpx;
        padding-left: 16rpx;
        max-width: 90%;
        font-weight: bold;
      }
      .ad-note {
        $h: 36rpx;
        width: fit-content;
        min-width: 240rpx;
        max-width: 85%;
        height: $h;
        font-size: 18rpx;
        line-height: $h;
        margin-left: 24rpx;
        padding-left: 16rpx;
        border-radius: 18rpx 0 0 18rpx;
        color: #fcb138;
        font-weight: bold;
        letter-spacing: 2rpx;
        background: linear-gradient(90deg, #fff 20%, #ffffff00 80%);
      }
      & > ::v-deep .u-icon {
        position: absolute;
        right: 18rpx;
        top: 0;
        bottom: 0;
        margin: auto;
        animation: move 2.4s infinite linear;
      }

      @keyframes move {
        25% {
          transform: translateX(-16rpx);
        }
        75% {
          transform: translateX(8rpx);
        }
      }
    }
  }
  &-footer > .hint-content {
    padding: 60rpx 0 24rpx;
    .hint-title {
      margin-bottom: 14rpx;
      > text {
        font-size: 30rpx;
        font-weight: bold;
        color: $u-main-color;
        line-height: 1.5;
        text-indent: 0.5em;
      }
    }
    > .hint-line {
      padding-bottom: 1em;
      text-indent: 2em;
    }
  }
}
</style>
