<template>
  <view class="bg-#f4f5f7">
    <up-tabs
      :list="tabs"
      :item-style="{ width: '180rpx', height: '80rpx' }"
      :line-width="40"
      :line-height="3"
      :current="tabIndex"
      @change="tabChange"></up-tabs>
  </view>

  <swiper class="h94vh bg-#f4f5f7" :current="tabIndex" @change="swiperChange">
    <swiper-item :key="111" style="overflow: auto">
      <Item
        v-for="item in scheduleList.filter(item => item.tabs === '政策公布')"
        :key="item._id"
        :left="item.header"
        :right="item.content" />
      <view
        class="mt20rpx w690rpx h90rpx ma text-center lh-90rpx rounded-20rpx bg-#f5e0e2 text-#FC3838 text-30rpx">
        当前数据年份为{{ currentYear }}
      </view>
    </swiper-item>
    <swiper-item :key="112" style="overflow: auto">
      <Item
        v-for="item in scheduleList.filter(item => item.tabs === '报名流程')"
        :key="item._id"
        :left="item.header"
        :right="item.content" />
      <view
        class="mt20rpx w690rpx h90rpx ma text-center lh-90rpx rounded-20rpx bg-#f5e0e2 text-#FC3838 text-30rpx">
        当前数据年份为{{ currentYear }}
      </view>
    </swiper-item>
    <swiper-item :key="113" style="overflow: auto">
      <view class="p20rpx">
        <MyTable :titles="['日期', '时间', '科目']" :list="list" />
      </view>
      <view
        class="mt20rpx w690rpx h90rpx ma text-center lh-90rpx rounded-20rpx bg-#f5e0e2 text-#FC3838 text-30rpx">
        具体见准考信息，以上时间仅供参考
      </view>
    </swiper-item>
    <swiper-item :key="114" style="overflow: auto">
      <Item
        v-for="item in scheduleList.filter(item => item.tabs === '成绩查询')"
        :key="item._id"
        :left="item.header"
        :right="item.content" />
      <view
        class="mt20rpx w690rpx h90rpx ma text-center lh-90rpx rounded-20rpx bg-#f5e0e2 text-#FC3838 text-30rpx">
        当前数据年份为{{ currentYear }}
      </view>
    </swiper-item>
  </swiper>
</template>

<script setup lang="ts" name="index-schedule">
import Item from './components/Item/index.vue'
import MyTable from './report-major/MajorTable/index.vue'
import { getSbExamTime, getSbScheduleData } from '@/api/project/index'
import type { ISbExamTimeData, ISbScheduleData } from '@/api/project/index/type'
import useIndexStore from '@/store/modules/index'
import useDateFormatter from '@/hooks/useDateFormatter'

const scheduleList = ref<ISbScheduleData[]>([])
const examTime = ref<ISbExamTimeData[]>([])

const indexStore = useIndexStore()

const { formatDate } = useDateFormatter('YYYY-MM-DD')

// 当前年份
const currentYear = ref()

// tabs导航
const tabs = ref([
  {
    id: 1,
    name: '政策公布',
  },
  {
    id: 2,
    name: '报名流程',
  },
  {
    id: 3,
    name: '考试时间',
  },
  {
    id: 4,
    name: '成绩查询',
  },
])
// 当前tab
const tabIndex = ref(0)
const tabChange = (val: any) => {
  tabIndex.value = val.index
}
const swiperChange = (val: any) => {
  tabIndex.value = val.detail.current
}

// 考试时间表格数据
const list = ref<string[][]>([])

onLoad(async () => {
  scheduleList.value = (await getSbScheduleData()).data.list.sort((x, y) => x.index - y.index)
  examTime.value = (await getSbExamTime()).data.list

  list.value = examTime.value.map(item => [formatDate(item.date), item.time, item.subject])

  currentYear.value = await indexStore.getCurrentYear()
})
</script>

<style lang="scss" scoped></style>
