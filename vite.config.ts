import { resolve } from 'path'
import { defineConfig } from 'vite'
import createVitePlugins from './vite/plugins'
/** @type {import('vite').UserConfig} */
// https://vitejs.dev/config/
export default defineConfig({
  plugins: createVitePlugins(),
  resolve: {
    alias: [
      {
        find: '@',
        replacement: resolve(__dirname, 'src'),
      },
    ],
  },
  // fix(https://github.com/dcloudio/uni-app/issues/3603): 修复unocss资源异常占用bug
  build: {
    watch: {
      exclude: ['node_modules/**', '/__uno.css'],
    },
  },
  css: {
    preprocessorOptions: {
      scss: {
        quietDeps: true,
        logger: {
          warn: () => {},
        },
      },
    },
  },
})
