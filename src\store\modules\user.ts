import { defineStore } from 'pinia'
import { getCurrentUser, getPublicToken } from '@/api/user'
import type { UserInfo } from '@/api/user/type'
import type { MenuItem } from '@/api/menu/type'
import type { BookInfoType } from '@/api/project/recite-words/type'

export default defineStore(
  'user',
  () => {
    const token = ref<string>()
    const openToken = ref<string>()
    const userInfo = ref<UserInfo>()
    const menuList = ref<MenuItem[]>()
    const bookInfo = ref<BookInfoType>()

    function setToken(val: string) {
      token.value = val
    }
    // 刷新用户信息
    async function refreshUserInfo() {
      userInfo.value = undefined
      const result = await getCurrentUser()
      userInfo.value = result.data.userInfo
      menuList.value = result.data.menuList
    }
    // 获取用户信息
    async function getUserInfo() {
      if (!token.value) return {}
      const result = await getCurrentUser()
      userInfo.value = result.data.userInfo
      menuList.value = result.data.menuList
    }
    // 用户退出
    async function userLogout(): Promise<void> {
      token.value = ''
      openToken.value = ''
      userInfo.value = undefined
      menuList.value = []
      uni.removeStorageSync('token')
      uni.removeStorageSync('openToken')
      uni.removeStorageSync('userInfo')
      uni.removeStorageSync('menuList')
      uni.removeStorageSync('corpId')
    }

    // 方法：检查用户是否登录
    function checkLogin() {
      if (!token.value) {
        uni.showToast({
          title: '请先登录',
          icon: 'none',
          duration: 2000,
        })

        // 3秒后跳转到用户页面
        setTimeout(() => {
          uni.switchTab({
            url: '/pages/user/user',
          })
        }, 3000) // 3000 毫秒 = 3 秒
      }
    }

    async function setRequestOpenToken() {
      const { data } = await getPublicToken()
      openToken.value = data
    }
    function setBookInfo(val: BookInfoType | undefined) {
      bookInfo.value = val
    }
    return {
      bookInfo,
      token,
      openToken,
      userInfo,
      refreshUserInfo,
      checkLogin,
      getUserInfo,
      setToken,
      userLogout,
      setRequestOpenToken,
      setBookInfo,
    }
  },
  {
    persist: {
      storage: {
        getItem: uni.getStorageSync,
        setItem: uni.setStorageSync,
      },
    },
  }
)
