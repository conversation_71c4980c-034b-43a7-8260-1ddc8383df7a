import type * as ClockInType from './type'
import {
  batchOperation,
  createModelData,
  dataInterface,
  deleteModelData,
  getModelDataDetail,
  getModelList,
  updateModelData,
} from '@/api/visual'
import type { FilterType } from '@/es/request'
// 批量新增打卡任务
export const batchAddClockData = (tasks: ClockInType.IAddClockReq[]) => {
  return batchOperation({
    menuId: '680b32b36353160fb27779b5',
    data: tasks as unknown as Record<string, unknown>[],
  })
}

export const getMyTeacher = (phone: string) => {
  return getModelList<any>({
    menuId: '67879d213c77470f25803629',
    currentPage: 1,
    pageSize: -1,
    connect: 'and',
    filter: [{ enCode: 'phone', method: 'eq', type: 'custom', value: phone }],
  })
}

// 删除打卡任务
export const deleteClockData = (taskId: string) => {
  return deleteModelData({
    menuId: '680b32b36353160fb27779b5',
    _id: taskId,
  })
}

// 获取连续打卡的天数
export const getContinuousDay = () => {
  return getModelList<ClockInType.IClockInConfirmData>({
    menuId: '669a1d0ac523aa5a70e0a39d',
    currentPage: 1,
    pageSize: -1,
    connect: 'and',
    filter: [
      { enCode: 'creatorUserId', method: 'eq', type: 'systemField', value: ['currentUser'] },
      { enCode: 'is_commit', method: 'eq', type: 'custom', value: ['是'] },
    ],
  })
}

// 获取指定时间段拍照打卡的记录
export const getCameraClockInRecordsDataList = (startTime: number, endTime: number) => {
  return getModelList<any>({
    menuId: '680b32b36353160fb27779b5',
    currentPage: 1,
    pageSize: -1,
    connect: 'and',
    filter: [
      {
        enCode: 'date',
        method: 'range',
        type: 'custom',
        value: [startTime, endTime],
      },
      {
        enCode: 'creatorUserId',
        method: 'eq',
        type: 'systemField',
        value: ['currentUser'],
      },
    ],
  })
}

// 获取在线学习/刷题的记录
export const getOnlineRecordsDataList = (startTime: number, endTime: number) => {
  return dataInterface({
    id: '6555b830f5590000ae004b62',
    data: {
      '@startTime': String(startTime),
      '@endTime': String(endTime),
    },
  })
}

export const getClockInRecordsById = (taskId: string) => {
  return getModelList<any>({
    menuId: '680b32b36353160fb27779b5',
    currentPage: 1,
    pageSize: -1,
    connect: 'and',
    filter: [{ enCode: '_id', method: 'eq', type: 'custom', value: [taskId] }],
  })
}

// 获取学生自己打卡的记录
export const getClockInRecordsDataList = (taskId: string) => {
  return getModelList<ClockInType.IClockInComplete>({
    menuId: '669a1c6ac523aa5a70e0a39b',
    currentPage: 1,
    pageSize: -1,
    connect: 'and',
    filter: [
      { enCode: 'creatorUserId', method: 'eq', type: 'systemField', value: ['currentUser'] },
      { enCode: 'task_id', method: 'eq', type: 'custom', value: [taskId] },
    ],
  })
}

// 完成学习打卡
export const CompleteClockInReq = (data: ClockInType.IClockInComplete, id: string) => {
  return updateModelData({
    menuId: '680b32b36353160fb27779b5',
    _id: id,
    data: JSON.stringify(data),
  })
}

// 获取派发打卡任务完成情况
export const getDistributeTaskSituationData = (taskId: string) => {
  return getModelList<ClockInType.IDistributeTask>({
    menuId: '669a1d0ac523aa5a70e0a39d',
    pageSize: -1,
    currentPage: 1,
    connect: 'and',
    filter: [
      {
        enCode: 'task_id',
        method: 'eq',
        type: 'custom',
        value: [taskId],
      },
      {
        enCode: 'user_id',
        method: 'eq',
        type: 'systemField',
        value: ['currentUser'],
      },
    ],
  })
}

// 创建/更新派发打卡任务完成情况
export const updateDistributeTaskSituationData = (data: ClockInType.IDistributeTask) => {
  if (data._id)
    return updateModelData({
      menuId: '669a1d0ac523aa5a70e0a39d',
      _id: data._id,
      data: JSON.stringify(data),
    })

  return createModelData({
    menuId: '669a1d0ac523aa5a70e0a39d',
    data: JSON.stringify(data),
  })
}

// 专项练习打卡
export const specialClockIn = (data: ClockInType.ISpecialClockIn) => {
  return createModelData(
    {
      menuId: '6758f6c8805f686c4209daca',
      data: JSON.stringify(data),
    },
    {
      unLoading: true,
    }
  )
}

// 获取专项练习打卡历史
export const getSpecialClockInHistoryList = ({
  pageSize = 20,
  currentPage = 1,
  filter = [] as FilterType[],
}) => {
  return getModelList<ClockInType.ISpecialClockIn>({
    menuId: '6758f6c8805f686c4209daca',
    connect: 'and',
    filter,
    pageSize,
    currentPage,
  })
}

// 获取专项练习打卡详情
export const getSpecialClockInDetail = (id: string) => {
  return getModelDataDetail<ClockInType.ISpecialClockIn>({
    menuId: '6758f6c8805f686c4209daca',
    _id: id,
  })
}

// 获取专项练习打卡成绩记录
export const getSpecialClockInScoreDetail = (id: string) => {
  return getModelDataDetail<ClockInType.ISpecialClockInScore>({
    menuId: '675bdc30805f686c4209dae4',
    _id: id,
  })
}
