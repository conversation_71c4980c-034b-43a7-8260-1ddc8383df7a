<template>
  <!-- 试卷列表 -->
  <view class="w-full pb100rpx">
    <view v-for="(ite, inde) in taskFacet" :key="ite._id">
      <view v-for="(item, index) in ite.tableField102" :key="item._id" class="item">
        <image
          :src="getHeadIcon(item.topicImg[0].url)"
          class="w670rpx ma m-y20rpx"
          mode="widthFix"
          @tap="markingChange(inde, index)"></image>
        <view class="text-#333333 fw-bold text-28rpx flex">
          <view>【正确答案】：</view>
          <mp-html :content="getTopicDetail(item.topic_id)?.answer![0]"></mp-html>
        </view>
        <view class="text-28rpx flex fw-bold justify-between">
          <view class="flex">
            <view class="text-#333333">AI阅卷结果：</view>
            <view class="text-#FF0000 ml20rpx">{{ item.ai_isCorrect || '未阅' }}</view>
          </view>
          <view class="flex">
            <view class="text-#333333 mb20rpx">分数：</view>
            <view class="text-#FF0000">{{ item.ai_score || 0 }}</view>
          </view>
        </view>
        <view class="text-28rpx flex fw-bold justify-between">
          <view class="flex">
            <view class="text-#333333">教师阅卷结果：</view>
            <view class="text-#FF0000 ml20rpx">{{ item.isCorrect || '未阅' }}</view>
          </view>
          <view class="flex">
            <view class="text-#333333 mb20rpx">分数：</view>
            <view class="text-#FF0000">{{ item.score || 0 }}</view>
          </view>
        </view>
        <up-transition :show="item.showAnswer">
          <view class="flex items-center gap-10rpx text-28rpx text-#333333 fw-bold">
            <image
              :src="getSystemImg('/6747e9ed0a34815816f11159/6778d6c9f8730075feadad5d')"
              class="w29rpx h19rpx"></image>
            <view>解析</view>
          </view>
          <mp-html
            v-if="getTopicDetail(item.topic_id)?.analysis"
            :content="getTopicDetail(item.topic_id)?.analysis"
            class="mt-10rpx text-28rpx text-#333333 indent-2rem"></mp-html>
          <u-empty v-else text="暂无解析"></u-empty>
        </up-transition>
        <u-divider dashed></u-divider>
        <view class="flex-center">
          <view
            class="w42rpx h30rpx text-#459AF7 text-30rpx"
            :class="item.showAnswer ? 'collapse-active' : 'collapse'"
            @click="toggleAnswer(item)">
            》
          </view>
        </view>
      </view>
    </view>
  </view>
  <u-popup
    :show="markingDialog"
    mode="center"
    :safe-area-inset-bottom="false"
    @close="markingDialog = false">
    <view class="w-750rpx min-h-100rpx">
      <image
        :src="getHeadIcon(taskFacet[currentIndex].tableField102[currentTableIndex].topicImg[0].url)"
        width="750rpx"
        mode="widthFix"
        @click="
          previewImg(
            getHeadIcon(taskFacet[currentIndex].tableField102[currentTableIndex].topicImg[0].url)
          )
        "></image>

      <view class="w-full bg-white p30rpx box-border">
        <view class="text-28rpx flex fw-bold justify-between">
          <view class="flex">
            <view class="text-#333333">AI阅卷结果：</view>
            <view class="text-#FF0000 ml20rpx">{{
              taskFacet[currentIndex].tableField102[currentTableIndex].ai_isCorrect || '未阅'
            }}</view>
          </view>
          <view class="flex">
            <view class="text-#333333 mb20rpx">分数：</view>
            <view class="text-#FF0000">{{
              taskFacet[currentIndex].tableField102[currentTableIndex].ai_score || 0
            }}</view>
          </view>
        </view>
        <view class="text-28rpx flex fw-bold justify-between">
          <view class="flex">
            <view class="text-#333333">教师阅卷结果：</view>
            <view class="text-#FF0000 ml20rpx">{{
              taskFacet[currentIndex].tableField102[currentTableIndex].isCorrect || '未阅'
            }}</view>
          </view>
          <view class="flex">
            <view class="text-#333333 mb20rpx">分数：</view>
            <view class="text-#FF0000">{{
              taskFacet[currentIndex].tableField102[currentTableIndex].score || 0
            }}</view>
          </view>
        </view>
        <view class="m-y-20rpx flex-center">
          <view class="toggle-group1">
            <view
              class="toggle-group1-toggle-btn1"
              :class="{
                active: taskFacet[currentIndex].tableField102[currentTableIndex].isCorrect === '对',
              }"
              @click="
                toggleResult(
                  taskFacet[currentIndex].tableField102[currentTableIndex],
                  '对',
                  currentTableIndex
                )
              "
              >对</view
            >
            <view
              class="toggle-group1-toggle-btn2"
              :class="{
                active: taskFacet[currentIndex].tableField102[currentTableIndex].isCorrect === '错',
              }"
              @click="
                toggleResult(
                  taskFacet[currentIndex].tableField102[currentTableIndex],
                  '错',
                  currentTableIndex
                )
              "
              >错</view
            >
          </view>
        </view>
        <view
          class="w-full m-y-20rpx h86rpx rounded-10rpx b-1rpx b-solid b-#D9D9D9 flex justify-between items-center p20rpx box-border">
          <view class="text-28rpx text-#333333">评分：</view>
          <view>
            <up-input
              v-model="taskFacet[currentIndex].tableField102[currentTableIndex].score"
              type="number"
              placeholder="请输入评分"
              border="none"
              input-align="right"
              readonly
              @change="
                handleChange(
                  taskFacet[currentIndex].tableField102[currentTableIndex],
                  currentTableIndex,
                  $event
                )
              "></up-input>
          </view>
        </view>
        <view class="w-full flex">
          <u-button text="上一题" shape="circle" :custom-style="prevBtnStyle" @click="prevQuestion">
          </u-button>
          <u-button text="下一题" shape="circle" :custom-style="nextBtnStyle" @click="nextQuestion">
          </u-button>
        </view>
      </view>
    </view>
  </u-popup>
  <view class="w-full fixed bottom-30rpx p30rpx box-border">
    <u-button text="批改提交" shape="circle" type="primary" @click="handleSubmit"></u-button>
  </view>
</template>

<script setup lang="ts" name="student-grading-papers">
import { getHeadIcon, getSystemImg, showToast, showToastBack } from '@/utils'
import {
  createSpecialClockInResultCard,
  getMarkingDetail,
  submitMarking,
} from '@/api/project/special-marking'
import type {
  CreateResultCardType,
  PaperFacetItem,
  TaskFacetItem,
} from '@/api/project/special-marking/type'
import useUserStore from '@/store/modules/user'
import mpHtml from '@/components/mp-html/mp-html.vue'

const userStore = useUserStore()

// 阅卷题目明细
const taskFacet = ref<TaskFacetItem[]>([] as TaskFacetItem[])
// 试卷题目明细
const paperFacet = ref<PaperFacetItem[]>([] as PaperFacetItem[])

// 改卷弹窗
const markingDialog = ref<boolean>(false)
const currentIndex = ref<number>(0)
const currentTableIndex = ref<number>(0)
const markingChange = (index1: number, index2: number) => {
  // 主表
  currentIndex.value = index1
  // 子表
  currentTableIndex.value = index2
  markingDialog.value = !markingDialog.value
}

// 下拉切换
const toggleAnswer = (item: any) => {
  item.showAnswer = !item.showAnswer
}

// 查询题目明细
const getTopicDetail = (topicId: string) => {
  const item = paperFacet.value!.find(ite => ite._id === topicId)
  return item
}

// 按钮样式
const prevBtnStyle = computed(() => ({
  flex: '1',
  margin: '20rpx',
  color: '#3388FF',
  backgroundColor: '#ffffff',
  border: '1px solid #3388FF',
}))

const nextBtnStyle = computed(() => ({
  flex: '1',
  margin: '20rpx',
  color: '#ffffff',
  backgroundColor: '#3388FF',
  border: '1px solid #3388FF',
}))

// 图片预览
const previewImg = (url: string) => {
  uni.previewImage({
    urls: [url],
  })
}

// 上一题
const prevQuestion = () => {
  currentTableIndex.value--
  if (currentTableIndex.value < 0) {
    currentTableIndex.value = taskFacet.value![currentIndex.value].tableField102.length - 1
  }
}

// 下一题
const nextQuestion = () => {
  currentTableIndex.value++
  if (currentTableIndex.value >= taskFacet.value![currentIndex.value].tableField102.length) {
    currentTableIndex.value = 0
  }
}

// 切换结果
const toggleResult = (item: any, value: string, index: number) => {
  item.isCorrect = value

  const score = paperFacet.value![index].score || 0
  if (value === '对') {
    item.score = score
  } else {
    item.score = 0
  }
  item.judges = new Array(1)
  item.judges[0] = userStore.userInfo?.id
}

// 评分
const handleChange = (ite: any, index: number, value: number) => {
  const score = Number(value)
  const maxScore = paperFacet.value![index].score
  setTimeout(() => {
    if (score > maxScore) {
      showToast(`评分不能大于${maxScore}分`)
      ite.tableField102[index].score = maxScore
    } else if (score < 0) {
      showToast('评分不能小于0分')
      ite.tableField102[index].score = 0
    }
  }, 500)
}

// 添加专项试卷答题记录
const addTopicResult = async (
  val: TaskFacetItem['tableField102'],
  paperId: string,
  userId: string
) => {
  const data = {} as CreateResultCardType
  data.state = '提交'
  data.userId = userId
  data.answerTime = ''
  data.type = '专项打卡'
  data.tableField112 = val.map(ite => {
    return {
      topicId: ite.topic_id,
      userAnswer: ite.userAnswer,
      index: ite.index,
      score: ite.score,
    }
  })
  data.totalScore = val.reduce((pre, cur) => pre + (cur.score || 0), 0)
  data.accuracy = Number((val.filter(ite => ite.isCorrect === '对').length / val.length).toFixed(2))
  data.fid = paperId
  await createSpecialClockInResultCard(data)
}

// 批改提交
const handleSubmit = async () => {
  for (const item of taskFacet.value!) {
    addTopicResult(item.tableField102, item.paperId, item.userId)
    await submitMarking(item._id, item.tableField102)
  }
  showToastBack('批改成功')
}

onLoad(async (val: any) => {
  const { data } = await getMarkingDetail(val.id)
  taskFacet.value = data[0].taskFacet
  taskFacet.value.forEach(item => {
    item.tableField102.forEach(ite => {
      if (!ite?.score) {
        // 设置默认分数
        ite.score = 0
        ite.showAnswer = false
      }
    })
  })
  paperFacet.value = data[0].paperFacet
})
</script>

<style>
page {
  background-color: #f2f8ff;
  padding-bottom: 20rpx;
}
</style>

<style lang="scss" scoped>
.item {
  margin: auto;
  margin-top: 20rpx;
  margin-bottom: 20rpx;
  width: 710rpx;
  background-color: #ffffff;
  padding: 20rpx;
  box-sizing: border-box;
  border-radius: 20rpx;
}
.score {
  width: 148rpx;
  height: 45rpx;
  background: rgba(69, 154, 247, 0.1);
  border-radius: 0rpx 23rpx 23rpx 0rpx;
  color: #459af7;
  font-size: 24rpx;
  text-align: center;
  line-height: 45rpx;
}

.collapse {
  transform: rotate(90deg);
  transition: all 0.3s;
}
.collapse-active {
  transform: rotate(270deg);
  transition: all 0.3s;
}

.toggle-group1 {
  display: flex;
  width: 260rpx;
  justify-content: space-between;
  text-align: center;
  line-height: 100rpx;
  &-toggle-btn1 {
    width: 100rpx;
    height: 100rpx;
    border-radius: 50%;
    border: 1px solid #459af7;
    color: #459af7;
    &.active {
      background-color: #459af7;
      color: #fff;
    }
  }
  &-toggle-btn2 {
    width: 100rpx;
    height: 100rpx;
    border-radius: 50%;
    border: 1px solid #f33a3a;
    color: #f33a3a;
    &.active {
      background-color: #f33a3a;
      color: #fff;
    }
  }
}
</style>
