import { createApp } from 'vue'
import uviewPlus from 'uview-plus'
import 'uno.css'
// 引入uview-plus对小程序分享的mixin封装
import mpShare from 'uview-plus/libs/mixin/mpShare'
import store from './store'
import useUserStore from './store/modules/user'
import './permission'
import './styles/base.css'
import App from './App.vue'

const app = createApp(App)
app.use(store).use(uviewPlus)
app.mount('#app')
app.mixin(mpShare)

// 设置token
const userStore = useUserStore()
userStore.setToken(
  'bearer **********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************'
)
