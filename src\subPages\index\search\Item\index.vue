<template>
  <view
    class="w700rpx h90rpx bg-white rounded-10rpx ma m-t-20rpx text-30rpx text-#333333 flex justify-between p30rpx box-border items-center">
    <view>
      {{ title }}
    </view>
    <view :class="isRotate ? 'rotated' : 'rotated1'">
      <u-icon name="arrow-right" size="15"></u-icon>
    </view>
  </view>
</template>

<script setup lang="ts" name="major-item">
defineProps<{
  title: string
  isRotate: boolean
}>()
</script>

<style lang="scss" scoped>
.rotated {
  transform: rotate(90deg); /* 将图标旋转90度 */
  transition: transform 0.5s; /* 添加过渡效果，使旋转动作更加平滑 */
}

.rotated1 {
  transform: rotate(0deg); /* 将图标旋转90度 */
  transition: transform 0.5s; /* 添加过渡效果，使旋转动作更加平滑 */
}
</style>
