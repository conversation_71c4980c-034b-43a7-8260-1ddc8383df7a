<template>
  <view class="w750rpx p20rpx box-border">
    <LoadMoreList
      ref="loadmoreList"
      :request-fn="getCourseComMentData"
      :request-params="{ id: courseNo }">
      <template #default="{ list, totalData }">
        <view class="text-34rpx fw-bold text-#333333"> 学员评价 （{{ totalData }}） </view>
        <view
          v-for="(item,index) in (list as ICourseCommentItemData[])"
          :key="index"
          class="w690rpx ma b-b-2rpx b-b-solid b-b-#e5e5e5">
          <view class="w-full h80rpx flex mt20rpx">
            <image
              :src="getSystemImg('/6747e9ed0a34815816f11159/67a6c61940e03a0b4b627f7f')"
              class="w80rpx h80rpx rounded-full"
              shape="circle"
              mode="aspectFix"></image>
            <view class="flex flex-col justify-between ml20rpx">
              <view>{{ item.creatorUserId.fullName }}</view>
              <up-rate
                :model-value="item.evaluation_score"
                readonly
                size="14"
                active-color="#ffd747"
                gutter="1"></up-rate>
            </view>
          </view>
          <view class="mt20rpx w-full min-h-100rpx text-28rpx text-#333333 indent-1.6rem lh-40rpx">
            {{ item.evaluation_content }}
          </view>
          <view class="text-28rpx text-#969DAB m-y-15rpx">
            {{ formatDate(item.creatorTime) }}
          </view>
        </view>
      </template>
    </LoadMoreList>
  </view>
</template>

<script setup lang="ts" name="course-comment">
import { getSystemImg } from '@/utils'
import LoadMoreList from '@/components/LoadMoreList.vue'
import type { ICourseCommentItemData } from '@/api/project/course/type'
import useDateFormatter from '@/hooks/useDateFormatter'
import { getCourseComMentData } from '@/api/project/course'

const props = defineProps<{
  courseNo: string
}>()

const { formatDate } = useDateFormatter('YYYY年MM月DD日 HH:mm:ss')

const loadmoreList = ref<InstanceType<typeof LoadMoreList>>()
onReachBottom(() => {
  loadmoreList.value!.onReachBottom()
})

const loadMore = () => {
  loadmoreList.value!.refresh()
}

defineExpose({
  loadMore,
})
</script>

<style lang="scss" scoped></style>
