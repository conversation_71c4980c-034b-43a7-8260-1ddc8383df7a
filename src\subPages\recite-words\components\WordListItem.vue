<template>
  <view class="flex items-center word-item-wrapper">
    <view v-if="showCheckbox" class="ml-33rpx">
      <u-checkbox :name="wordInfo._id" :checked="isSelected" shape="circle"> </u-checkbox>
    </view>
    <view class="flex-1">
      <view
        class="word-entry"
        :data-number="no"
        :style="{
          borderLeftColor: color || (no ? 'rgba(69, 154, 247, 0.10)' : 'transparent'),
          borderLeftWidth: no ? undefined : '0px',
        }"
        @click="onWordClick">
        <view class="flex-col flex word-info justify-between">
          <text class="word">{{ wordInfo?.word || '' }}</text>
          <text class="articulation">英[{{ wordInfo?.pronunciation || '' }}]</text>
        </view>
        <view class="flex-col flex meaning justify-between">
          <text v-for="item in wordInfo.meaning || []" :key="item" class="meaning-item">
            {{ item }}
          </text>
        </view>
        <view class="hidden-container">
          <view
            :style="{ background: 'transparent' }"
            class="flex-center hidden-element left"
            @click="hiddenEleClick('left')">
            <!-- <text v-show="hiddenEleLeft"></text> -->
            <text></text>
          </view>
          <view
            :style="{ background: hiddenEleRight ? '#EAF3FD' : 'transparent' }"
            class="flex-center hidden-element right"
            @click="hiddenEleClick('right')">
            <text v-show="hiddenEleRight">点击显示</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts" name="word-list-item">
import useCommonStore from '@/store/modules/common'

const props = defineProps({
  wordInfo: {
    type: Object,
    default: () => ({}),
  },
  // showEnterLeft: {
  //   type: Boolean,
  //   default: true,
  // },
  showEnterRight: {
    type: Boolean,
    default: true,
  },
  no: {
    type: Number,
    default: undefined,
  },
  showCheckbox: {
    type: Boolean,
    default: false,
  },
  isSelected: {
    type: Boolean,
    default: false,
  },
  swipeOptions: {
    type: Array,
    default: () => [],
  },
  color: {
    type: String,
  },
})

const emit = defineEmits(['hiddenEleClick'])

const { playWord } = useCommonStore()
// 隐藏左边
// const hiddenEleLeft = ref(!props.showEnterLeft)
// 隐藏右边
const hiddenEleRight = ref(!props.showEnterRight)

// watch(
//   () => props.showEnterLeft,
//   val => {
//     hiddenEleLeft.value = !val
//   }
// )

watch(
  () => props.showEnterRight,
  val => {
    hiddenEleRight.value = !val
  }
)

function onWordClick() {
  if (props.wordInfo?.word) {
    // 点击单词播放发音
    playWord(props.wordInfo.word)
  }
}
function resetHiddenEle() {
  // hiddenEleLeft.value = !props.showEnterLeft
  hiddenEleRight.value = !props.showEnterRight
}
function hiddenEleClick(type: string) {
  if (type === 'left') {
    // hiddenEleLeft.value = !hiddenEleLeft.value
  } else {
    hiddenEleRight.value = !hiddenEleRight.value
  }
  emit('hiddenEleClick', type)
}

// 多选框变化现在由外层的u-checkbox-group统一处理

defineExpose({
  resetHiddenEle,
})
</script>

<style lang="scss" scoped>
.word-item-wrapper {
  .flex {
    display: flex;
  }

  .items-center {
    align-items: center;
  }

  .flex-1 {
    flex: 1;
  }

  .mr-16rpx {
    margin-right: 16rpx;
  }
}
.word-entry {
  $containerHeight: 120rpx;
  $borderWidth: 55rpx;
  display: flex;
  align-items: center;
  min-height: $containerHeight;
  position: relative;
  border-left: $borderWidth solid transparent;
  border-bottom: 1rpx solid #dedede;
  &[data-number]:not([data-number='null']):not([data-number='']):not([data-number='0'])::before {
    content: attr(data-number);
    color: #999999;
    position: absolute;
    left: -$borderWidth;
    text-align: center;
    width: $borderWidth;
    font-size: 24rpx;
  }

  .word-info {
    flex: 2;
    padding: 16rpx 28rpx;
    position: relative;
    text-align: center;
    .word {
      font-weight: bold;
      font-size: 30rpx;
      margin-bottom: 13rpx;
      word-wrap: break-word;
      word-break: break-all;
      white-space: normal;
      line-height: 1.4;
    }
    .articulation {
      color: #c9c9c9;
      font-size: 24rpx;
    }
  }
  .meaning {
    padding: 16rpx 28rpx;
    flex: 3;
    font-size: 28rpx;
    position: relative;
    .meaning-item {
      margin-bottom: 15rpx;
      &:last-child {
        margin-bottom: 0;
      }
    }
  }
  .hidden-container {
    position: absolute;
    width: 100%;
    height: 100%;
    display: flex;
    font-size: 28rpx;
    .hidden-element {
      background: #e5ecf7;
      z-index: 10;
      color: #333333;
    }
    .left {
      margin-left: 5rpx;
      flex: 2;
    }
    .right {
      flex: 3;
    }
  }
}
</style>
