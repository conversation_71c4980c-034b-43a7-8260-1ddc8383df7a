<template>
  <SearchInput
    v-if="tabIndex === 0"
    :value="searchValue"
    placeholder="请输入学校"
    @on-change="onChange" />
  <SearchInput v-else :value="searchValue" placeholder="请输入专业" @on-change="onChange" />
  <up-tabs
    :list="tabs"
    :item-style="{ width: '350rpx', height: '100rpx' }"
    :line-width="40"
    :line-height="6"
    :current="tabIndex"
    @change="tabChange"></up-tabs>
  <swiper :style="`height:${swiperHeight}px`" :current="tabIndex" @change="swiperChange">
    <swiper-item item-id="111" class="overflow-auto">
      <view v-for="(item, index) in majorlist" :key="`${index}1`">
        <Item
          :title="item.title"
          :is-rotate="clickIndex === index && majorShow"
          @tap="toOpen(index, item.title, 'major')" />
      </view>
    </swiper-item>
    <swiper-item item-id="222" class="overflow-auto">
      <view v-for="(item, index) in schoollist" :key="index">
        <Item
          :title="item.title"
          :is-rotate="clickIndex === index && schoolShow"
          @tap="toOpen(index, item.title, 'school')" />
      </view>
    </swiper-item>
  </swiper>
  <u-popup :show="majorShow" :round="10" mode="bottom" @close="closeMajor">
    <view class="max-h-70vh w-full p20rpx box-border">
      <view class="text-center mb20rpx fw-bold">
        <text class="text-#459af7">
          {{ now_title }}
        </text>
        {{ currentYear }}年专升本招生院校
      </view>
      <view class="overflow-auto max-h-65vh">
        <MajorTable
          type="major"
          :titles="[
            '学校名称',
            '考试科目',
            `2024年<br/>招生计划`,
            `2024年<br/>报考人数`,
            `2024年<br/>分数线`,
          ]"
          :list="majorlist[clickIndex]?.list"
          @to-jump="toSchool" />
      </view>
      <view class="w-full h5vh"></view>
    </view>
  </u-popup>
  <u-popup :show="schoolShow" :round="10" mode="bottom" @close="closeSchool">
    <view class="max-h-70vh w-full p20rpx box-border">
      <view class="text-center mb20rpx fw-bold">
        <text class="text-#459af7">
          {{ now_title }}
        </text>
        {{ currentYear }}年专升本招生专业
      </view>
      <view class="overflow-auto max-h-65vh">
        <MajorTable
          type="major"
          :titles="[
            '专业名称',
            '考试科目',
            `2024年<br/>招生计划`,
            `2024年<br/>报考人数`,
            `2024年<br/>分数线`,
          ]"
          :list="schoollist[clickIndex]?.list"
          @to-jump="toMajor" />
      </view>
      <view class="w-full h5vh"></view>
    </view>
  </u-popup>
  <view class="h30rpx w-full"> </view>
</template>

<script setup lang="ts" name="report-major-list">
import SearchInput from '.././components/SearchInput/index.vue'
import Item from '.././search/Item/index.vue'
import MajorTable from './MajorTable/index.vue'
import { getBKSchoolDataList, getReportMajorDataList } from '@/api/project/index'
import type { IBKSchoolMajorData } from '@/api/project/index/type'
import useIndexStore from '@/store/modules/index'

const searchValue = ref('')
const onChange = (val: string) => {
  searchValue.value = val
}

const currentYear = ref(2024)

// 总本科专业数据
const majorbkList = ref<IBKSchoolMajorData[]>([])

const now_title = ref('')
// 按专业选择
const majorTitles = ref<string[]>([])
const majorShow = ref(false)
const majorlist = computed<
  {
    title: string
    list: string[][]
  }[]
>(() => {
  const list: {
    title: string
    list: string[][]
  }[] = []
  majorTitles.value.forEach(item => {
    list.push({
      title: item,
      list: majorbkList.value
        .filter(ite => ite.Undergraduate_name === item)
        .map(i => {
          return [
            i.school_name,
            i.other_info.split('\n')[0],
            `${i.enrollment_plan}`,
            i.application_nums,
            i.score,
          ]
        }),
    })
  })
  return list.filter(item => item.title.includes(searchValue.value))
})
const toMajor = (val: string) => {
  const data = majorbkList.value.filter(item => item.Undergraduate_name === val)[0]
  uni.navigateTo({
    url: `/subPages/index/major/detail?name=${data.Undergraduate_name}&code=${data.Undergraduate_code}`,
  })
}

const toSchool = async (val: string) => {
  const data = (
    await getBKSchoolDataList({
      filter: [
        {
          enCode: 'school_name',
          type: 'custom',
          method: 'eq',
          value: [val],
        },
      ],
    })
  ).data.list.filter(item => item.school_name === val)[0]
  uni.navigateTo({
    url: `/subPages/index/school/detail?id=${data._id}`,
  })
}

// 按学校选择
const schoolTitles = ref<string[]>([])
const schoolShow = ref(false)
const schoollist = computed<
  {
    title: string
    list: string[][]
  }[]
>(() => {
  const list: {
    title: string
    list: string[][]
  }[] = []
  schoolTitles.value.forEach(item => {
    list.push({
      title: item,
      list: majorbkList.value
        .filter(ite => ite.school_name === item)
        .map(i => {
          return [
            i.Undergraduate_name,
            i.other_info.split('\n')[0],
            `${i.enrollment_plan}`,
            i.application_nums,
            i.score,
          ]
        }),
    })
  })
  return list.filter(item => item.title.includes(searchValue.value))
})

const clickIndex = ref(-1)
const toOpen = (index: number, title: string, type: string) => {
  if (type === 'major') {
    majorShow.value = !majorShow.value
  } else {
    schoolShow.value = !schoolShow.value
  }
  if (index === clickIndex.value) {
    clickIndex.value = -1
    return 0
  }
  now_title.value = title
  clickIndex.value = index
}

const closeMajor = () => {
  majorShow.value = false
  clickIndex.value = -1
}

const closeSchool = () => {
  schoolShow.value = false
  clickIndex.value = -1
}

// tabs导航
const tabs = ref([
  {
    id: 1,
    name: '按专业选择',
  },
  {
    id: 2,
    name: '按学校选择',
  },
])
// 当前tab
const tabIndex = ref(0)
const tabChange = (val: any) => {
  tabIndex.value = val.index
  clickIndex.value = -1
  searchValue.value = ''
}
const swiperChange = (val: any) => {
  tabIndex.value = val.detail.current
}
const swiperHeight = ref(800)

onMounted(async () => {
  const systemInfo = uni.getSystemInfoSync()
  const screenHeight = systemInfo.screenHeight
  swiperHeight.value = screenHeight - 200
})

onLoad(async (val: any) => {
  const { data } = await getReportMajorDataList(val.code)
  majorbkList.value = (data[0] as any).school_list
  schoolTitles.value = [...new Set(majorbkList.value.map(item => item.school_name))]
  majorTitles.value = [...new Set(majorbkList.value.map(item => item.Undergraduate_name))]
  currentYear.value = await useIndexStore().getCurrentYear()
})
</script>

<style lang="scss">
page {
  background-color: #f4f5f7;
}
</style>
