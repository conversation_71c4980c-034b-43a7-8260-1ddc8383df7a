<template>
  <view
    v-for="item in items"
    :key="item._id"
    class="w-630rpx p-30rpx bg-#f5f5f5 box-border rounded-2rpx mt-20rpx"
    @click="goTest(item.enCode)">
    <view class="flex">
      <view>
        <view class="u-line-1 bold text-28rpx">{{ item.title }}</view>
        <!-- 使用 item.title 替换死数据 -->
        <view class="mt-18rpx flex">
          <view
            class="w-auto h-40rpx bg-#f7e7cf text-#FCB138 text-24rpx text-center lh-40rpx mr-20rpx">
            {{ item.scoreType }}
            <!-- 使用 item.scoreType 替换状态 -->
          </view>
          <view class="w-auto h-40rpx bg-#d2e3f6 text-#459AF7 text-24rpx text-center lh-40rpx">
            {{ item.type }}
            <!-- 使用 item.type 替换比赛类型 -->
          </view>
        </view>
      </view>
    </view>
    <view class="mt-15rpx text-#999999 text-24rpx lh-38rpx u-line-4">
      {{ item.info }}
      <!-- 使用 item.info 替换描述 -->
    </view>
    <view class="mt-13rpx flex justify-between">
      <view class="flex"> </view>
      <view class="text-#459AF7 text-24rpx lh-45rpx">
        奖励：{{ item.enCode }}
        <!-- 这里我选择了显示 enCode，假设它是奖励 -->
      </view>
    </view>
    <view
      class="border-t border-dashed border-#537EEF border-[1rpx] border-l-0 border-r-0 border-b-0 mt10rpx text-center pt-21rpx text-24rpx text-#459AF7">
      排行榜>
    </view>
  </view>
</template>

<script setup lang="ts">
import type { PropType } from 'vue'
import type * as VisualType from '@/api/contest/type'
// 定义 props
const props = defineProps({
  items: {
    type: Array as PropType<VisualType.ContestListTableField[]>,
    required: true,
  },
})
// console.log("props.items:", props.items);

function goTest(enCode: string) {
  uni.navigateTo({
    url: `/subPages/contest/content?enCode=${enCode}`,
  })
}

// 打印接收到的 props
// console.log("接收到的 items:",props.items);
</script>

<style lang="scss" scoped></style>
