<template>
  <view>
    <view class="flex flex-center">
      <Search
        icon-position="prefix"
        placeholder="搜索团队名称"
        @search="handleSearch"
        @input="handleInput"></Search>
    </view>
    <view>
      <view
        v-for="item in contestTeamList"
        :key="item._id"
        class="flex justify-between py-31rpx border-1rpx border-b border-solid border-#E2E2E2 border-t-none border-l-none border-r-none">
        <view class="flex">
          <view>
            <image
              class="w-120rpx h-120rpx"
              :src="getSystemImg('6694ec5cc523aa5a70e0a30f/67357112905da85a42991c15')"
              radius="4"></image>
          </view>
          <view class="ml-26rpx flex flex-col justify-between">
            <view>{{ item.name }}</view>
            <!-- 使用实际的团队名称 -->
            <view class="text-#777C8B text-24rpx">{{ item.introduce }}</view>
            <!-- 使用实际的团队介绍 -->
            <view class="flex">
              <view>
                <image
                  class="w-37rpx h-37rpx"
                  :src="getSystemImg('6694ec5cc523aa5a70e0a30f/67357112905da85a42991c15')"
                  shape="circle"></image>
              </view>
              <view class="ml16rpx text-#459AF7 text-22rpx flex items-center">队长</view>
              <view class="ml45rpx text-22rpx text-#777C8B flex items-center"
                >{{ item.captain.length }}/5</view
              >
              <!-- 使用实际的队长数量 -->
            </view>
          </view>
        </view>
        <view class="flex flex-col justify-center">
          <up-button
            type="primary"
            text="申请加入"
            :plain="true"
            :custom-style="{ width: '120rpx', height: '52rpx', fontSize: '26rpx' }"></up-button>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts" name="team-info">
import { computed, onMounted, ref } from 'vue'
import type * as VisualType from '@/api/project/contest/type'
import Search from '@/components/Search.vue'
import {
  getParticipatingTeamsList,
  getParticipatingTeamsListByKeyword,
} from '@/api/project/contest'
import { getSystemImg, showToast } from '@/utils' // 导入提示方法

const enCode = inject<any>('enCode') // 指定 enCode 的类型为 Ref<string>,这个是从爷爷组件注入的

const contestTeamList = ref<VisualType.List[]>([]) // 定义响应式变量
const searchKeyword = ref('') // 用户输入的搜索关键词

// 处理搜索
async function handleSearch(value: string) {
  try {
    const result = await getParticipatingTeamsListByKeyword(searchKeyword.value, enCode.value) // 调用异步请求方法
    console.log(searchKeyword.value)

    // 判断搜索结果是否为空
    if (!Array.isArray(result.data.list) || result.data.list.length === 0) {
      showToast('没有找到相关的团队') // 调用封装好的提示方法
      contestTeamList.value = [] // 清空团队列表
    } else {
      contestTeamList.value = result.data.list // 将返回的结果赋值给 contestTeamList
    }
  } catch (error) {
    showToast('搜索请求失败，请稍后再试') // 处理错误
  }
}

// 处理输入
function handleInput(value: string) {
  searchKeyword.value = value // 更新输入的值
  if (searchKeyword.value === '') {
    fetchAllTeams(enCode.value) // 清空搜索框时重新请求全部数据
  }
  // else {
  //   handleSearch(searchKeyword.value); // 有输入时进行搜索
  // }
}

// 获取当前encode中全部团队
async function fetchAllTeams(enCode: string) {
  // 指定 enCode 类型为 string
  try {
    const response = await getParticipatingTeamsList(enCode) // 直接传递 enCode
    contestTeamList.value = response.data.list // 假设返回的数据结构中有 list
    console.log('该赛道所有团队个数:', response.data.list.length)
  } catch (error) {
    // 提示用户没有登录
    showToast('您尚未登录，请先登录') // 显示提示信息

    // 等待1秒后跳转到登录页面
    setTimeout(() => {
      uni.switchTab({
        url: '/pages/user/user', // 跳转到登录页面
      })
    }, 2000) // 1000毫秒 = 1秒

    console.error('获取竞赛团队列表失败:', error) // 处理错误
  }
}

// 在组件挂载时请求数据
onMounted(async () => {
  if (enCode && enCode.value) {
    // 确保 enCode 不为 null 或 undefined
    console.log('在 TeamInfo 中接收到的赛题编码:', enCode.value) // 直接访问 enCode.value
    await fetchAllTeams(enCode.value) // 初始请求全部数据
  } else {
    console.error('未能注入 enCode')
  }
})
</script>

<style lang="scss" scoped></style>
