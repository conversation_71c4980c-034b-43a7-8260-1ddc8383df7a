<template>
  <view class="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-100 safe-area-bottom">
    <view class="flex h-100rpx">
      <!-- 单词 Tab -->
      <view
        class="flex-1 flex flex-col items-center justify-center cursor-pointer mb-8rpx"
        :class="{
          'text-blue-500': currentPage === 'words',
          'text-gray-400': currentPage !== 'words',
        }"
        @click="onTabClick('words')">
        <view class="text-44rpx">
          <image
            :src="getTabIcon('words', currentPage === 'words')"
            :style="{
              width: currentPage === 'words' ? '48rpx' : '38rpx',
              height: currentPage === 'words' ? '48rpx' : '38rpx',
            }"
            mode="aspectFit">
          </image>
        </view>
        <text class="text-22rpx leading-none">单词</text>
      </view>

      <!-- 我的 Tab -->
      <view
        class="flex-1 flex flex-col items-center justify-center cursor-pointer mb-8rpx"
        :class="{
          'text-#459AF7': currentPage === 'home',
          'text-#616A75': currentPage !== 'home',
        }"
        @click="onTabClick('home')">
        <view class="text-44rpx">
          <image
            :src="getTabIcon('home', currentPage === 'home')"
            class="w-44rpx h-44rpx"
            mode="aspectFit">
          </image>
        </view>
        <text class="text-22rpx leading-none">我的</text>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts" name="recite-tabbar">
import { getSystemImg } from '@/utils'

interface Props {
  currentPage: 'words' | 'home'
}

interface Emits {
  (e: 'tab-change', page: 'words' | 'home'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 获取tab图标
function getTabIcon(tab: 'words' | 'home', isActive: boolean) {
  // 根据tab类型和激活状态返回对应的网络图片路径
  const iconMap = {
    'words-inactive': '687758481807a96b974f9916/688879cecfdce7607d9ce04c',
    'words-active': '687758481807a96b974f9916/688879cecfdce7607d9ce04f',
    'home-inactive': '687758481807a96b974f9916/688879cecfdce7607d9ce04e',
    'home-active': '687758481807a96b974f9916/688879cecfdce7607d9ce050',
  }

  const key = `${tab}-${isActive ? 'active' : 'inactive'}` as keyof typeof iconMap
  return getSystemImg(iconMap[key])
}

// tab点击事件
function onTabClick(page: 'words' | 'home') {
  if (page === props.currentPage) {
    // 如果点击的是当前页面，不做任何操作
    return
  }

  emit('tab-change', page)

  // 页面跳转
  if (page === 'words') {
    uni.navigateTo({
      url: '/subPages/recite-words/recite-words',
    })
  } else if (page === 'home') {
    uni.navigateTo({
      url: '/subPages/recite-words/home',
    })
  }
}
</script>

<style lang="scss" scoped>
// 使用UnoCSS，这里只需要少量自定义样式
.safe-area-bottom {
  padding-bottom: env(safe-area-inset-bottom);
}

// 如果需要自定义过渡动画
.cursor-pointer {
  transition: color 0.2s ease;
}
</style>
