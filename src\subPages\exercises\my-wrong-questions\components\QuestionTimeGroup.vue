<template>
  <view class="question-time-group">
    <!-- 时间标签 -->
    <view class="py-24rpx">
      <text class="text-30rpx color-#333333">{{ timeLabel }}</text>
    </view>

    <!-- 题目列表 -->
    <view>
      <view
        v-for="(question, index) in questionList"
        :key="index"
        class="bg-white rounded-16rpx py-36rpx px-28rpx mb-20rpx"
        @click="handleQuestionClick(question)">
        <!-- 题目内容 -->
        <view class="mb-20rpx">
          <mpHtml class="text-28rpx color-#333 leading-58rpx" :content="question.title"></mpHtml>
        </view>

        <!-- 选项 -->
        <view
          class="options-container"
          :class="{ 'force-single-column': shouldUseSingleColumn(question.option) }">
          <view
            v-for="(option, optionIndex) in question.option"
            :key="optionIndex"
            class="option-item"
            :class="{ 'long-option': isLongOption(option) }">
            <text class="option-label">{{ String.fromCharCode(65 + optionIndex) }}.</text>
            <mpHtml class="option-text" :content="option"></mpHtml>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts" name="question-time-group">
import type { WrongExercises } from '@/api/project/exercises/type'
import mpHtml from '@/components/mp-html/mp-html.vue'

// 组件属性
defineProps<{
  timeLabel: string
  questionList: WrongExercises[]
}>()

// 组件事件
const emits = defineEmits<{
  questionClick: [question: WrongExercises]
}>()

// 处理题目点击
const handleQuestionClick = (question: WrongExercises) => {
  emits('questionClick', question)
}

// 判断选项文本是否过长（超过15个字符认为是长选项）
const isLongOption = (option: string) => {
  return option.length > 15
}

// 判断是否应该使用单列布局（如果有任何一个选项过长）
const shouldUseSingleColumn = (options: string[]) => {
  return options.some(option => isLongOption(option))
}
</script>

<style lang="scss" scoped>
.options-container {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;

  /* 默认田字排列 */
  .option-item {
    flex: 0 0 calc(50% - 10rpx);
  }

  /* 当需要单列布局时，所有选项占满一行 */
  &.force-single-column {
    .option-item {
      flex: 0 0 100%;
    }
  }
}

.option-item {
  display: flex;
  align-items: flex-start;
  font-size: 26rpx;
  color: #616a75;
  min-width: 0; /* 允许文本收缩 */
  margin-bottom: 0;
}

.option-label {
  margin-right: 8rpx;
  flex-shrink: 0; /* 标签不收缩 */
}

.option-text {
  flex: 1;
  word-break: break-word;
  line-height: 1.4;
}
</style>
