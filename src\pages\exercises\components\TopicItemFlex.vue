<template>
  <view class="w320rpx mt20rpx flex-center flex-col">
    <image
      :src="assembleImgData(cover)"
      class="w320rpx h220rpx rounded-26rpx"
      mode="aspectFit"></image>
    <view class="flex-1 flex flex-col justify-around items-start">
      <view class="fw-bold text-32rpx m-y-10rpx"> {{ title }} </view>
      <view class="flex flex-wrap">
        <view
          v-for="(item, index) in tags"
          :key="index"
          class="mr10rpx p6rpx text-22rpx text-#537EEF bg-#e9f6fe"
          >{{ item }}</view
        >
      </view>
      <view class="w-full text-28rpx text-right fwbold text-#FF776A"
        >￥{{ price?.toFixed(2) }}</view
      >
    </view>
  </view>
</template>

<script setup lang="ts" name="course-item">
import { assembleImgData, getSystemImg } from '@/utils'
defineProps<{
  title?: string
  tags?: string[]
  price?: number
  cover?: UploadImgData
}>()
</script>

<style lang="scss" scoped></style>
