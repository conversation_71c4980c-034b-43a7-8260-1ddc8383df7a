<template>
  <view class="w-690rpx p-30rpx bg-#ffffff box-border rounded-10rpx mt-20rpx mx-auto">
    <view class="flex">
      <view>
        <view class="u-line-1 bold text-28rpx">{{ CompetitionDetails?.title }}</view>
        <view class="mt-18rpx flex">
          <view
            class="w-auto h-40rpx bg-#f7e7cf text-#FCB138 text-24rpx text-center lh-40rpx mr-20rpx">
            进行中
          </view>
          <view class="w-auto h-40rpx bg-#d2e3f6 text-#459AF7 text-24rpx text-center lh-40rpx">
            {{ CompetitionDetails?.tags[0] }}
          </view>
        </view>
      </view>
    </view>
    <view class="mt-15rpx text-#999999 text-24rpx lh-38rpx" v-html="CompetitionDetails?.annexInfo">
    </view>
    <view class="mt-13rpx flex justify-between mt30rpx">
      <view class="flex text-#999999 text-24rpx lh-45rpx">
        举办方：{{ Competition?.organizer }}</view
      >
      <view
        class="w-auto h45rpx text-#666666 text-24rpx bg-#f4f4f4 text-center rounded-22rpx lh-45rpx"
        >{{ Competition?.tags[0] }}</view
      >
    </view>
    <view class="mt-13rpx flex justify-between mt30rpx">
      <view class="flex text-#999999 text-24rpx"
        >参与人数 <span class="text-#459AF7 ml-20rpx">54</span>
      </view>
      <view class="flex text-#999999 text-24rpx"
        >团队数 <span class="text-#459AF7 ml-20rpx">54 </span>
      </view>
    </view>
    <view class="mt30rpx">
      <up-steps
        :current="timeCurrent"
        :dot="true"
        direction="column"
        active-color="#459AF7"
        inactive-color="#bcd8fb">
        <up-steps-item :title="enrollTimeTitle"></up-steps-item>
        <up-steps-item :title="startTimeTitle"></up-steps-item>
        <up-steps-item :title="endTimeTitle"></up-steps-item>
      </up-steps>
      <!-- 上面是 uview-plus -->
    </view>
    <view class="mt-31rpx">
      <!-- 按钮 -->
      <button
        class="w-630rpx h62rpx rounded-37rpx bg-#459AF7 text-#FFFFFF text-28rpx lh-62rpx"
        :disabled="!buttonStates[currentButtonState].enabled"
        @click="handleButtonClick">
        {{ buttonText }}
      </button>
    </view>

    <!-- 报名模态框 -->
    <up-overlay :show="showModal">
      <view class="warp">
        <view class="w592rpx h351rpx rounded-20rpx bg-#ffffff flex flex-col justify-between">
          <view>
            <image
              class="w-592rpx h-138rpx"
              :src="getSystemImg('6694ec5cc523aa5a70e0a30f/6735b03e905da85a42991d11')"
              width="592rpx"
              height="138rpx"></image>
          </view>
          <view class="text-center text-#999999 text-24rpx mt-0rpx"> 确认报名比赛？ </view>
          <view>
            <view class="flex pb-28rpx flex-center">
              <view class="mr-30rpx">
                <up-button
                  text="取消"
                  :custom-style="{ width: '204rpx', height: '74rpx' }"
                  :plain="true"
                  type="primary"
                  :hairline="true"
                  @click="cancelRegistration"></up-button>
              </view>
              <view>
                <up-button
                  text="确定"
                  type="primary"
                  :custom-style="{ width: '204rpx', height: '74rpx' }"
                  @click="signUp"></up-button>
              </view>
            </view>
          </view>
          <view
            class="absolute left-1/2 transform -translate-x-1/2 text-30rpx text-#FFFFFF mt-33rpx">
            温馨提示
          </view>
        </view>
      </view>
    </up-overlay>

    <!-- 创建团队模态框 -->
    <CreatTeam :show-modal="showCreat" @close="closeTeam" @success-registered="Success" />
  </view>
</template>

<script setup lang="ts" name="topic-progress">
import { onMounted, ref } from 'vue'
import CreatTeam from './CreatTeam.vue'
import { getContestInfoByenCode, getTeamDataList } from '@/api/project/contest'
import type * as VisualType from '@/api/project/contest/type'
import { formatSingleTimestamp, formatTimestamps, getSystemImg, showToast } from '@/utils' // 格式化时间戳
// 定义 props
const props = defineProps<{
  enCode: string // 父组件传递的赛题编码
}>()

// 定义竞赛详细的数据,用于渲染界面和一些操作
const CompetitionDetails = ref<VisualType.ContestListTableField>()
const Competition = ref<VisualType.ContestListType>()
const myTeam = ref<any[]>() // 这个数据可以知道用户是否已经注册

// 赛事时间
const times = ref({
  enrollTime: [] as number[], // 报名时间
  startTime: [] as number[], // 开始时间
  endTime: null as number | null, // 结束时间
})

// 当前步骤，0: 报名中, 1: 进行中, 2: 已结束
const timeCurrent = ref(0)

// 计算属性
const enrollTimeTitle = computed(() => {
  // 确保访问前检查具体数据是否存在
  return CompetitionDetails.value && CompetitionDetails.value.enrollTime
    ? `比赛报名: ${formatTimestamps(CompetitionDetails.value.enrollTime)}`
    : '等待数据...' // 提供默认值
})

const startTimeTitle = computed(() => {
  return CompetitionDetails.value && CompetitionDetails.value.startTime
    ? `比赛开始: ${formatTimestamps(CompetitionDetails.value.startTime)}`
    : '等待数据...'
})

const endTimeTitle = computed(() => {
  return CompetitionDetails.value && CompetitionDetails.value.endTime
    ? `比赛结束: ${formatSingleTimestamp(CompetitionDetails.value.endTime)}`
    : '等待数据...'
})

// 模态框控制
const showModal = ref(false) // 报名框
const showCreat = ref(false) // 创建团队框

// 按钮状态管理
const buttonStates = {
  notRegistered: { label: '报名', enabled: true, action: 'showSignUpModal' },
  registered: { label: '答题', enabled: true, action: 'startAnswering' },
  registeredNotInTeam: { label: '已报名，未加入团队', enabled: true, action: 'showJoinTeamModal' }, // 新增状态
  deadline: { label: '报名截止', enabled: false, action: null },
  settlingScores: { label: '成绩结算中', enabled: false, action: null }, // 新增状态
  ended: { label: '比赛已结束', enabled: false, action: null },
  error: { label: '加载失败', enabled: true, action: 'reload' },
} as const

const currentButtonState = ref<keyof typeof buttonStates>('notRegistered') // 当前按钮状态

// 动态计算按钮属性
const buttonText = computed(() => buttonStates[currentButtonState.value].label) // 这里 TypeScript 现在知道 currentButtonState.value 是 keyof typeof buttonStates
const buttonAction = computed(() => buttonStates[currentButtonState.value].action)
// 关闭创建团队框
function closeTeam() {
  currentButtonState.value = 'registeredNotInTeam' // 更新按钮状态已报名，未加入团队
  console.log('关闭创建团队框,更新按钮状态为已报名，未加入团队')
  showCreat.value = false
}
// 报名逻辑
const signUp = () => {
  showModal.value = false
  currentButtonState.value = 'registered' // 更新按钮状态为已报名
  console.log('报名完成')
  showCreat.value = true
}

// 取消报名逻辑
const cancelRegistration = () => {
  currentButtonState.value = 'notRegistered' // 更新按钮状态为未报名
  showModal.value = false
  console.log('取消报名')
}
// 成功创建团队逻辑,成功报名逻辑
const Success = () => {
  uni.showToast({
    title: '报名成功',
    icon: 'success', // 显示成功图标
  })
  currentButtonState.value = 'registered' // 更新按钮状态为已报名，已加入团队
  showCreat.value = false
}
// 答题逻辑
const startAnswering = async () => {
  const res = await getTeamDataList(props.enCode)
  // 这里必须重新请求一次接口,如果不这样可以在用户第一次报名后,直接答题会导致myTeam.value为空myTeam.value[0].teamEnCode报错
  myTeam.value = res.data[0].myTeam
  if (Competition.value && myTeam.value && myTeam.value.length > 0) {
    uni.navigateTo({
      url: `/subPages/exercises/answer-sheet?type=test&examId=${
        CompetitionDetails.value?.topicId
      }&duration=100&title=${CompetitionDetails.value?.title}&answerType=${
        CompetitionDetails.value?.title
      }&enCode=${encodeURIComponent(props.enCode)}&competitionEnCode=${encodeURIComponent(
        Competition.value.competitionEnCode
      )}&teamEnCode=${encodeURIComponent(myTeam.value[0].teamEnCode)}`,
    })
  } else {
    console.error('myTeam.value is undefined or empty')
  }
}

// 重新加载逻辑
const reload = () => {
  console.log('重新加载数据')
}
// 按钮点击事件处理
const handleButtonClick = () => {
  if (!buttonAction.value) return

  switch (buttonAction.value) {
    case 'showSignUpModal':
      showModal.value = true
      break
    case 'showJoinTeamModal':
      showCreat.value = true // 打开加入团队的模态框
      break
    case 'startAnswering':
      startAnswering()
      break
    case 'reload':
      reload()
      break
    default:
      console.warn('未知动作:', buttonAction.value)
  }
}
// 请求我的团队数据的函数,用于判断当前用户是否已经报名
const fetchTeamData = async (enCode: string) => {
  try {
    const res = await getTeamDataList(enCode) // 这个接口看当前团队中的人
    const isRegister = res.data[0].myTeam // 这个里面可以看到当前团队是否报名
    console.log('测试的接口,看这个接口返回的数据', isRegister)
    return isRegister // 返回团队数据
  } catch (error) {
    console.error('获取团队数据失败:', error)
    return null // 返回 null 表示请求失败
  }
}
// 更新当前步骤
const updateCurrentStep = async (enCode: string) => {
  const now = Date.now() // 获取当前时间戳

  const enrollStarts = times.value.enrollTime // 报名开始时间数组
  const startTimes = times.value.startTime // 比赛开始时间数组
  const endTime = times.value.endTime // 比赛结束时间

  // 检查报名时间
  const isEnrolling = now >= enrollStarts[0] && now <= enrollStarts[1] // 判断当前时间是否在报名时间内
  if (isEnrolling) {
    timeCurrent.value = 0 // 报名中
    console.log('报名中')
    // 只有在报名中时才请求团队数据
    const isRegister = await fetchTeamData(enCode) // 请求团队数据
    if (isRegister && Array.isArray(isRegister) && isRegister.length > 0) {
      myTeam.value = isRegister // 更新我的团队数据
      currentButtonState.value = 'registered' // 更新按钮状态
      console.log('myTeam 不是一个空数组，按钮状态已更新为 registered', isRegister)
    } else {
      currentButtonState.value = 'notRegistered' // 更新按钮状态为 notRegistered
      console.log('myTeam 是空数组，当前团队没有人:', isRegister)
    }
    return // 直接返回，不再检查后续条件
  }

  // 检查比赛进行中
  const lastStartTime = Math.max(...startTimes) // 获取最大开始时间
  if (now >= startTimes[0] && now <= lastStartTime) {
    timeCurrent.value = 1 // 进行中
    currentButtonState.value = 'deadline' // 设置按钮状态为报名截止
    uni.showToast({
      title: '比赛报名截止,目前比赛进行中',
      icon: 'none',
    })
    console.log('比赛进行中')
    // 这里有特殊情况在比赛进行中,但是用户已经报名,如果这种情况报名了就显示答题
    const isRegister = await fetchTeamData(enCode) // 请求团队数据
    if (isRegister && Array.isArray(isRegister) && isRegister.length > 0) {
      currentButtonState.value = 'registered' // 更新按钮状态
      console.log('myTeam 不是一个空数组，按钮状态已更新为 registered', isRegister)
    } else {
      currentButtonState.value = 'deadline' // 更新按钮状态为 notRegistered
      console.log('myTeam 是空数组，当前团队没有人:', isRegister)
    }
    return // 直接返回，不再检查后续条件
  }

  // 检查中间阶段个阶段
  if (now > lastStartTime && endTime != null && now < endTime) {
    currentButtonState.value = 'settlingScores' // 设置按钮状态为比赛还在结算分数
    timeCurrent.value = 1 // 在第二个阶段
    console.log('比赛还在结算分数')
    return // 直接返回，不再检查后续条件
  }
  // 检查结束时间
  if (endTime && now >= endTime) {
    timeCurrent.value = 2 // 已结束
    currentButtonState.value = 'ended' // 设置按钮状态为比赛已结束
    uni.showToast({
      title: '比赛已结束',
      icon: 'none',
    })
    console.log('已结束')
  }
}
// 组件挂载后获取竞赛详情
onMounted(async () => {
  console.log('父组件传递的赛题编码', props.enCode)
  try {
    const res = await getContestInfoByenCode(props.enCode) // 通过编码获取赛题信息
    // 假设返回的数据结构符合接口定义
    Competition.value = res.data.list[0]
    CompetitionDetails.value = res.data.list[0].tableField118[0]
    // 更新赛事时间状态
    if (CompetitionDetails.value) {
      times.value.enrollTime = CompetitionDetails.value.enrollTime || []
      times.value.startTime = CompetitionDetails.value.startTime || []
      times.value.endTime = CompetitionDetails.value.endTime || null
      // console.log('赛事时间', times.value);
      updateCurrentStep(props.enCode) // 更新当前步骤,传递 props.enCode 给 updateCurrentStep
    }
  } catch (error) {
    console.error('获取竞赛详情失败:', error)
  }
})
</script>

<style lang="scss" scoped>
.warp {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
}

.custom-title {
  color: blue;
  /* 设置文字颜色为蓝色 */
  font-size: 25rpx;
  /* 放大文字大小为1.2倍 */
}
</style>
