import type * as ReciteType from './type.d'
import type { FilterType } from '@/es/request'
import {
  batchDelete,
  batchOperation,
  createModelData,
  dataInterface,
  deleteModelData,
  getModelList,
  importTable,
  updateModelData,
} from '@/api/visual'

export function getReciteTags() {
  return getModelList<ReciteType.TagsResultType>({
    menuId: '65deae9b5efd5153ff5644c0',
    sort: {
      sort: 'desc',
    },
    pageSize: -1,
  })
}

export function getReciteList(type: string, tag: string) {
  return getModelList<ReciteType.BookInfoType>({
    menuId: '668e2fb4c523aa5a70e0a2a3',
    pageSize: -1,
    connect: 'and',
    filter: [
      {
        enCode: 'type',
        method: 'eq',
        type: 'custom',
        value: [type],
      },
      {
        enCode: 'tags',
        method: 'in',
        type: 'custom',
        value: [tag],
      },
    ],
  })
}

export function getAllReciteList() {
  return getModelList<ReciteType.BookInfoType>({
    menuId: '668e2fb4c523aa5a70e0a2a3',
    pageSize: -1,
  })
}
export function getMyBookList() {
  return getModelList<ReciteType.BookInfoType>({
    menuId: '668e2fb4c523aa5a70e0a2a3',
    pageSize: -1,
    connect: 'and',
    filter: [
      {
        enCode: 'creatorUserId',
        method: 'eq',
        type: 'systemField',
        value: ['currentUser'],
      },
      {
        enCode: 'owner',
        method: 'eq',
        type: 'custom',
        value: ['个人'],
      },
    ],
  })
}

// 获取当前用户全部词书的背诵计划
export function getRecitePlan() {
  return getModelList<ReciteType.RecitePlanResultType>({
    menuId: '668e3212c523aa5a70e0a2a9',
    pageSize: -1,
    filter: [
      {
        enCode: 'creatorUserId',
        method: 'eq',
        type: 'systemField',
        value: ['currentUser'],
      },
    ],
  })
}

// 获取当前用户词书的背诵计划
export function getUserReciteBook() {
  return getModelList<ReciteType.UserReciteBookType>({
    connect: 'and',
    menuId: '668e33ebc523aa5a70e0a2ae',
    pageSize: -1,
    filter: [
      {
        enCode: 'creatorUserId',
        method: 'eq',
        type: 'systemField',
        value: ['currentUser'],
      },
    ],
  })
}
// 获取词书列表通过ID
export function getReciteListById(ids: string[]) {
  return getModelList<ReciteType.BookInfoType>({
    menuId: '668e2fb4c523aa5a70e0a2a3',
    pageSize: -1,
    connect: 'and',
    filter: [
      {
        enCode: '_id',
        method: 'in',
        type: 'custom',
        value: ids,
      },
    ],
  })
}

// 获取词书单词量
export function getReciteWordCount(id: string) {
  return dataInterface<ReciteType.ReciteWordCountType[]>({
    id: '65def05c136f0000f80065e2',
    data: {
      '@wordbookId': id,
    },
  })
}

// 创建计划
export function createRecitePlan(data: ReciteType.CreateRecitePlanType) {
  return createModelData({
    menuId: '668e3212c523aa5a70e0a2a9',
    data: JSON.stringify(data),
  })
}

// 修改用户词书
export function updateUserReciteBook(data: ReciteType.IUpdateUserReciteBookParams) {
  return updateModelData({
    menuId: '668e33ebc523aa5a70e0a2ae',
    _id: data.id,
    data: JSON.stringify(data),
  })
}

// 获取用户所有打卡记录（降序）
export function getReciteWordCountByBook(bookId: string) {
  return getModelList<ReciteType.IReciteWordDetail>({
    menuId: '668e32acc523aa5a70e0a2aa',
    connect: 'and',
    pageSize: -1,
    sort: {
      days: 'asc',
    },
    filter: [
      {
        enCode: 'book',
        method: 'eq',
        type: 'custom',
        value: [bookId],
      },
      {
        enCode: 'creatorUserId',
        method: 'eq',
        type: 'systemField',
        value: ['currentUser'],
      },
    ],
  })
}
// 修改用户存入单词
export function updateReciteWordDetailById(data: ReciteType.IReciteWordDetail) {
  return updateModelData({
    menuId: '668e32acc523aa5a70e0a2aa',
    _id: data._id,
    data: JSON.stringify(data),
  })
}
// 获取用户词书已学单词量
export function getLearnedReciteWordCount(id: string) {
  return dataInterface<ReciteType.ReciteWordCountType[]>({
    id: '65e045942e710000cd003926',
    data: {
      '@book': id,
    },
  })
}
// 随机抽取单词
export const getReciteWord = (bookId: string, num: number) => {
  return dataInterface<ReciteType.IReciteWordInfo[]>(
    {
      id: '65dff55c2e710000cd003922',
      data: {
        '@wordbookId': bookId,
        '@num': num,
      },
    },
    {
      timeout: 30000,
    }
  )
}
// 获取单词列表
export const getReciteWordList = (id: string) => {
  return dataInterface<ReciteType.IReciteWordInfo[]>({
    id: '65e036ea2e710000cd003925',
    data: {
      '@id': id,
    },
  })
}
// 获取派发单词列表
export const getDistributeWordList = (id: string) => {
  return dataInterface<ReciteType.IReciteWordInfo[]>({
    id: '677b7e969c31abbc91025211',
    data: {
      '@id': id,
    },
  })
}

// 单词入库
export const insertReciteWord = (data: ReciteType.IInsertWordParams) => {
  return createModelData({
    menuId: '668e32acc523aa5a70e0a2aa',
    data: JSON.stringify(data),
  })
}
// 熟词生词入库
export const insertReciteWordStatus = (data: ReciteType.IInsertReciteWord) => {
  return createModelData({
    menuId: '668e3346c523aa5a70e0a2ac',
    data: JSON.stringify(data),
  })
}

// 修改单词详情表
export const updateReciteWordDetail = (data: { id: string; sign: number }) => {
  return updateModelData({
    menuId: '668e32acc523aa5a70e0a2aa',
    _id: data.id,
    data: JSON.stringify({ sign: data.sign }),
  })
}
// 获取指定天数的详细表
export const getReciteWordCountByDay = (bookId: string, day: number) => {
  return getModelList<ReciteType.IReciteDetailWord>({
    menuId: '668e32acc523aa5a70e0a2aa',
    connect: 'and',
    pageSize: 1,
    sort: {
      days: 'desc',
    },
    filter: [
      {
        enCode: 'book',
        method: 'eq',
        type: 'custom',
        value: [bookId],
      },
      {
        enCode: 'days',
        method: 'eq',
        type: 'custom',
        value: [day],
      },
      {
        enCode: 'creatorUserId',
        method: 'eq',
        type: 'systemField',
        value: ['currentUser'],
      },
    ],
  })
}
// 删除熟词生词
export const deleteReciteWordStatus = (_id: string) => {
  return deleteModelData({
    menuId: '668e3346c523aa5a70e0a2ac',
    _id,
  })
}

// 获取用户词书的熟词生词记录
export const getReciteWordStatus = (bookId: string) => {
  return getModelList<ReciteType.IInsertReciteWord>({
    menuId: '668e3346c523aa5a70e0a2ac',
    connect: 'and',
    pageSize: -1,
    filter: [
      {
        enCode: 'book',
        method: 'eq',
        type: 'custom',
        value: [bookId],
      },
      {
        enCode: 'creatorUserId',
        method: 'eq',
        type: 'systemField',
        value: ['currentUser'],
      },
    ],
  })
}
// 更新熟词生词
export const updateReciteWordStatus = (data: { id: string; type: string }) => {
  return updateModelData({
    menuId: '668e3346c523aa5a70e0a2ac',
    _id: data.id,
    data: JSON.stringify(data),
  })
}

// 获取错误单词通过Id 668e3420c523aa5a70e0a2b0 - 65e178c95efd5153ff5647c9
export const getReciteWordErrorById = (wordId: string) => {
  return getModelList<ReciteType.IReciteWordError>({
    menuId: '668e3420c523aa5a70e0a2b0',
    connect: 'and',
    pageSize: 1,
    filter: [
      {
        enCode: 'wordId',
        type: 'custom',
        method: 'eq',
        value: [wordId],
      },
      {
        enCode: 'creatorUserId',
        method: 'eq',
        type: 'systemField',
        value: ['currentUser'],
      },
    ],
  })
}

// 添加错误单词
export const insertReciteWordError = (data: {
  wordId: string
  wrongTimes: number
  word: string
}) => {
  return createModelData({
    menuId: '668e3420c523aa5a70e0a2b0',
    data: JSON.stringify(data),
  })
}
// 更新错误单词的词数
export const updateReciteWordErrorCount = (data: { id: string; wrongTimes: number }) => {
  return updateModelData({
    menuId: '668e3420c523aa5a70e0a2b0',
    _id: data.id,
    data: JSON.stringify({ wrongTimes: data.wrongTimes }),
  })
}
// 随机获取单词
export const getReciteWordRandom = (id: string) => {
  return dataInterface<ReciteType.IReciteWordRandom[]>({
    id: '65e17172c84e0000de004b53',
    data: {
      '@id': id,
    },
  })
}
// 随机获取派发单词
export const getDistributeWordRandom = (id: string) => {
  return dataInterface<ReciteType.IReciteWordRandom[]>({
    id: '677b85a49c31abbc91025212',
    data: {
      '@id': id,
    },
  })
}

// 获取用户词书生词熟词数量
export const getNewWordCountAndFamiliarWordCount = (id: string) => {
  return dataInterface<ReciteType.IReciteWordTotal[]>({
    id: '65e04a1b2e710000cd003927',
    data: {
      '@book': id,
    },
  })
}

// 获取生词熟词列表
export const getNewWordCountAndFamiliarWordList = (
  id: string,
  type: string,
  currentPage: number,
  pageSize: number = 20
) => {
  return dataInterface<ReciteType.INewWordAndFamiliarWord[]>({
    id: '688c1b88a70ff88fc40b8e61',
    data: {
      '@book': id,
      '@type': type,
      '@currentPage': currentPage,
      '@pageSize': pageSize,
    },
  })
}
// 获取已学单词列表
export const getLearnedReciteWordList = (id: string, currentPage: number) => {
  return dataInterface({
    id: '65e13a1d2e710000cd00392a',
    data: {
      '@book': id,
      '@currentPage': currentPage,
      '@pageSize': 20,
    },
  })
}

// 获取待学单词列表
export const getNewWordList = (id: string, currentPage: number) => {
  return dataInterface({
    id: '65e13c062e710000cd00392b',
    data: {
      '@wordbookId': id,
      '@currentPage': currentPage,
      '@pageSize': 20,
    },
  })
}

export const submitReciteWord = (data: ReciteType.SubmitReciteWordType) => {
  return createModelData({
    menuId: '676e6772805f686c4209db93',
    data: JSON.stringify(data),
  })
}

export const getReciteWordReport = (id: string) => {
  return getModelList<ReciteType.SubmitReciteWordType>({
    menuId: '676e6772805f686c4209db93',
    connect: 'and',
    pageSize: -1,
    filter: [{ enCode: '_id', method: 'eq', type: 'custom', value: [id] }],
  })
}

export const getReciteWordReportByIds = (ids: string[], currentPage: number) => {
  return getModelList<ReciteType.SubmitReciteWordType>({
    menuId: '668e301dc523aa5a70e0a2a5',
    connect: 'and',
    pageSize: 10,
    currentPage,
    filter: [{ enCode: '_id', method: 'in', type: 'custom', value: ids }],
  })
}

export function getReciteWordHistoryList({
  pageSize = 20,
  currentPage = 1,
  filter = [] as FilterType[],
}) {
  return getModelList<ReciteType.TestHistoryItem[]>({
    menuId: '676e6772805f686c4209db93',
    pageSize,
    currentPage,
    filter: [
      {
        enCode: 'creatorUserId',
        method: 'eq',
        type: 'systemField',
        value: ['currentUser'],
      },
      ...filter,
    ],
    connect: 'and',
  })
}

export const getWrongWordList = () => {
  return getModelList<ReciteType.IReciteWordError>({
    menuId: '668e3420c523aa5a70e0a2b0',
    connect: 'and',
    pageSize: -1,
    filter: [
      { enCode: 'creatorUserId', method: 'eq', type: 'systemField', value: ['currentUser'] },
    ],
  })
}

export const updateReciteWordLog = (data: {
  wordId: string
  correct: number
  testType: string
}) => {
  return createModelData({
    menuId: '677e311cb66b52314c33421b',
    data: JSON.stringify(data),
  })
}
export const getMyLogWordList = () => {
  return dataInterface<{ accuracy: number; word: string }[]>({
    id: '6781e3424df3b061580102c2',
  })
}

// 导入单词
export const importWord = (data: any) => {
  return importTable({
    authGroupId: '678768358c642464728f6fc7',
    menuId: '668e301dc523aa5a70e0a2a5',
    data,
    fileId: data.fileId,
    importField: ['wordbookId', 'word', 'pronunciation', 'meaning'],
    mode: 'create',
    primary: '',
    startRow: 0,
  })
}

// 批量新增单词
export const batchAddWord = (words: ReciteType.IAddWord) => {
  return batchOperation({
    menuId: '668e301dc523aa5a70e0a2a5',
    data: words as unknown as Record<string, unknown>[],
  })
}

// 获取导入模板文件
export const getImportTemplate = () => {
  return getModelList({
    menuId: '68872f591807a96b974fa371',
    pageSize: -1,
  })
}
// 获取导入教程
export const getTutorial = () => {
  return getModelList({
    menuId: '68872f8b1807a96b974fa374',
    pageSize: -1,
  })
}

// 添加词书
export const addOwnerBook = (data: ReciteType.IAddBook) => {
  return createModelData({
    menuId: '668e2fb4c523aa5a70e0a2a3',
    data: JSON.stringify(data),
  })
}

// 删除词书
export const deleteBook = (bookId: string) => {
  return deleteModelData({
    menuId: '668e2fb4c523aa5a70e0a2a3',
    _id: bookId,
  })
}

// 删除学习计划
export const deleteRecitePlan = (planId: string) => {
  return deleteModelData({
    menuId: '668e3212c523aa5a70e0a2a9',
    _id: planId,
  })
}

// 批量删除学习记录
export const deleteReciteWordRecords = (recordIds: string[]) => {
  return batchDelete({
    menuId: '668e32acc523aa5a70e0a2aa',
    ids: recordIds,
  })
}

// 批量删除词书关联的单词
export const deleteBookWords = (wordIds: string[]) => {
  return batchDelete({
    menuId: '668e301dc523aa5a70e0a2a5',
    ids: wordIds,
  })
}

// 获取词书关联的所有单词ID
export const getBookWordIds = (bookId: string) => {
  return getModelList<{ _id: string }>({
    menuId: '668e301dc523aa5a70e0a2a5',
    connect: 'and',
    pageSize: -1,
    filter: [
      {
        enCode: 'wordbookId',
        method: 'eq',
        type: 'custom',
        value: [bookId],
      },
    ],
  })
}
