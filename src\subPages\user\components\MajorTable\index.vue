<template>
  <view
    class="sticky top-0 w-full z-9 min-h-86rpx row bg-#459AF7 flex text-white justify-around items-center text-20rpx bor-b">
    <view
      v-for="(i, index) in titles"
      :key="index"
      class="flex-1 h-full text-center col flex flex-center"
      :style="(index === 1 || index === 0) && type === 'major' ? 'flex: 2' : ''">
      <rich-text :nodes="i"> </rich-text>
    </view>
  </view>
  <view
    v-for="(ite, index) in list"
    :key="`${index}1`"
    class="min-h-80rpx w-full row flex text-#333333 justify-around items-center text-18rpx bor-b">
    <view
      v-for="(it, inde) in ite"
      :key="`${inde}11`"
      :style="(inde === 1 || inde === 0) && type === 'major' ? 'flex: 2' : ''"
      :class="inde === 0 ? 'color-b' : ''"
      class="col flex-1 text-center bor-r flex flex-center"
      @click="toDetail(it, inde)">
      <view class="m6rpx">
        {{ it || '—' }}
      </view>
    </view>
  </view>
  <u-empty v-if="list?.length === 0" text="暂无数据"></u-empty>
</template>

<script setup lang="ts" name="report-major-major-table">
const props = defineProps<{
  titles: string[]
  list: string[][]
  type?: string
}>()

const emits = defineEmits(['toJump'])

const toDetail = (val: string, index: number) => {
  if (index === 0) {
    if (props.titles[0] === '专业名称') {
      emits('toJump', val)
    } else if (props.titles[0] === '学校名称') {
      emits('toJump', val)
    }
  }
}
</script>

<style lang="scss" scoped>
.color-b {
  color: #459af7;
}

.bor-bor {
  border: 1px solid #edeeee;
}
.bor-b {
  border-bottom: 1px solid #edeeee;
}
.row {
  overflow: hidden;
  .col {
    position: relative;
    &::before {
      content: '';
      position: absolute;
      height: 300px;
      left: 0;
      background-color: #edeeee;
      width: 1px;
    }
    &:last-child::after {
      content: '';
      position: absolute;
      height: 300px;
      right: 0;
      background-color: #edeeee;
      width: 1px;
    }
  }
}
</style>
