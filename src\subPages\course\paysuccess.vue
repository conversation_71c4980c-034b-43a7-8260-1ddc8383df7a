<template>
  <TitleBar nav-color="#459af7" text-color="white" show-back :flag="false" />
  <view class="w-full h340rpx bg-#459af7 flex flex-col items-center justify-around">
    <view class="flex items-center">
      <u-icon name="checkmark-circle-fill" color="white" size="40"></u-icon>
      <view class="ml30rpx text-white text-32rpx">支付成功</view>
    </view>
    <view class="flex justify-between w-450rpx">
      <view
        class="w161rpx h63rpx bg-#459af7 rounded-10rpx text-white text-28rpx text-center lh-63rpx b-2rpx b-white b-solid"
        @click="toJump"
        >返回主页</view
      >
      <view
        class="w161rpx h63rpx bg-white rounded-10rpx text-#459AF7 text-28rpx text-center lh-63rpx"
        @click="toDetail(courseId)"
        >开始学习</view
      >
    </view>
  </view>
  <view class="w-full h100rpx text-#999999 text-30rpx text-center lh-100rpx"> 相似推荐 </view>
  <view class="bg-white w690rpx ma p10rpx box-border rounded-20rpx">
    <CourseItem
      v-for="(item, index) in recommendList"
      :key="index"
      class="m-y-20rpx"
      :title="item.course_name"
      :tags="item.course_tag"
      :cover="item.course_cover[0]"
      :price="item.course_price"
      @tap="toDetail(item.recommend)" />
  </view>
</template>

<script setup lang="ts" name="course-paysuccess">
import TitleBar from '@/components/TitleBar.vue'
import CourseItem from '@/pages/course/components/CourseItem.vue'
import type { ICourseRecommendTable } from '@/api/project/course/type'
import { getCourseRecommendData } from '@/api/project/course'

// 推荐课程列表
const recommendList = ref<ICourseRecommendTable[]>([])

// 当前课程id
const courseId = ref()

onLoad(async (val: any) => {
  const {
    data: { list },
  } = await getCourseRecommendData(val.id)
  courseId.value = val.id
  recommendList.value = list[0].tableField102
})

const toDetail = (id: string) => {
  uni.redirectTo({
    url: `/subPages/course/detail?id=${id}`,
  })
}

const toJump = () => {
  uni.switchTab({
    url: '/pages/course/course',
  })
}
</script>

<style lang="scss">
page {
  background-color: #f4f5f7;
}
</style>
