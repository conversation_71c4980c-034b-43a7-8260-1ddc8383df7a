<template>
  <view class="points-page">
    <!-- 顶部积分展示 -->
    <view class="points-header">
      <view class="points-title">我的积分</view>
      <view class="points-value">{{ memberStore.points_num }}</view>
    </view>

    <!-- 积分明细列表 -->
    <view class="points-list">
      <view class="list-header">积分明细</view>
      <view v-for="(item, index) in pointsList" :key="index" class="list-item">
        <view class="item-left">
          <text class="item-title">{{ item.action_name }}</text>
          <text class="item-time">{{ formatDate(item.creatorTime) }}</text>
        </view>
        <text class="item-points" :class="{ 'points-plus': item.points_type === '增加' }">
          {{ item.points_type === '增加' ? '+' : '-' }}{{ item.points_num }}
        </text>
      </view>
    </view>

    <!-- 底部按钮 -->
    <view class="bottom-buttons">
      <u-button
        class="btn"
        :custom-style="inviteBtnStyle"
        shape="circle"
        @click="toServe('invite')">
        邀请好友
      </u-button>
      <u-button
        class="btn"
        type="primary"
        :custom-style="vipBtnStyle"
        shape="circle"
        @click="toServe('member')">
        购买会员
      </u-button>
    </view>
  </view>
</template>

<script setup lang="ts" name="user-points">
import useMemberStore from '@/store/modules/member'
import { getPointsRecord } from '@/api/project/member'
import type { IPointsRecordData } from '@/api/project/member/type'
import useDateFormatter from '@/hooks/useDateFormatter'

// 积分列表
const pointsList = ref<IPointsRecordData[]>([])

// 日期格式化
const { formatDate } = useDateFormatter()

// 会员信息
const memberStore = useMemberStore()

const toServe = (url: string) => {
  uni.navigateTo({
    url: `/subPages/user/${url}`,
  })
}

// 添加按钮自定义样式
const inviteBtnStyle = {
  backgroundColor: '#e2ecf8',
  color: '#1890ff',
  height: '88rpx',
  fontSize: '34rpx',
}

const vipBtnStyle = {
  backgroundColor: '#1890ff',
  height: '88rpx',
  fontSize: '34rpx',
}

onMounted(async () => {
  memberStore.getMemberInfo()
  const { data } = await getPointsRecord()
  pointsList.value = data.list
})
</script>

<style>
page {
  background-color: #f4f5f7;
}
</style>

<style lang="scss" scoped>
.points-page {
  .points-header {
    width: 750rpx;
    height: 633rpx;
    background: linear-gradient(to bottom, #2b8efb, #4edff6);
    border-bottom-left-radius: 40rpx;
    border-bottom-right-radius: 40rpx;

    .points-title {
      position: absolute;
      left: 72rpx;
      top: 198rpx;
      font-size: 30rpx;
      color: #ffffff;
    }
    .points-value {
      position: absolute;
      left: 72rpx;
      top: 254rpx;
      font-size: 90rpx;
      color: #ffffff;
      font-weight: bold;
    }
  }
  .points-list {
    width: 690rpx;
    height: 1060rpx;
    position: absolute;
    left: 30rpx;
    top: 394rpx;
    box-sizing: border-box;
    background: #fff;
    border-radius: 40rpx;
    padding: 40rpx;
    overflow: auto;

    .list-header {
      font-size: 32rpx;
      font-weight: bold;
      margin-bottom: 30rpx;
      position: relative;

      &::after {
        content: '';
        position: absolute;
        left: 0;
        bottom: -4rpx;
        width: 135rpx;
        height: 11rpx;
        background: linear-gradient(to right, rgba(255, 255, 255, 0) 40%, #459af7 100%);
        pointer-events: none;
      }
    }

    .list-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 24rpx 0;
      border-bottom: 2rpx dashed #f5f5f5;

      &:last-child {
        border-bottom: none;
      }

      .item-left {
        display: flex;
        flex-direction: column;

        .item-title {
          font-size: 32rpx;
          color: #111111;
          margin-bottom: 10rpx;
        }

        .item-time {
          font-size: 24rpx;
          color: #999;
        }
      }

      .item-points {
        font-size: 32rpx;
        color: #ff4d4f;

        &.points-plus {
          color: #52c41a;
        }
      }
    }
  }

  .bottom-buttons {
    position: fixed;
    bottom: 60rpx;
    left: 40rpx;
    right: 40rpx;
    display: flex;
    gap: 30rpx;
  }
}
</style>
