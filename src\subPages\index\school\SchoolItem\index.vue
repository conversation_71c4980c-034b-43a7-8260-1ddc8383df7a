<template>
  <view
    class="rounded-10rpx bg-white flex justify-between items-center p30rpx box-border ma m-y-20rpx relative items-stretch">
    <view
      v-if="flag"
      class="flex absolute top-20rpx right-30rpx text-26rpx text-#969DAB items-center"
      @click="isCollect(collect as boolean)">
      <u-icon
        :name="collect ? 'star-fill' : 'star'"
        size="20"
        :color="collect ? 'orange' : ''"></u-icon>
      收藏
    </view>
    <u-image
      :src="assembleImgData(itemData.school_badge?.[0])"
      :width="imgSize?.width || '140rpx'"
      :height="imgSize?.height || '140rpx'"
      :class="{ 'm-10rpx': !imgSize }"
      mode="aspectFit"></u-image>
    <view class="flex-1 ml20rpx flex flex-col justify-between">
      <view>
        <view class="text-30rpx text-#333333 fw-bold">{{
          itemData.school_name || '湖南大学'
        }}</view>
        <view class="flex w-full m-t-2% gap16rpx">
          <view class="p10rpx bg-#feefd7 rounded-6rpx">
            <u-icon
              name="home"
              size="25rpx"
              :label="itemData.school_type"
              space="5rpx"
              color="#FCB138"
              label-color="#FCB138"
              label-size="22rpx"></u-icon>
          </view>
          <view class="bg-#feefd7 p10rpx rounded-6rpx">
            <u-icon
              name="home"
              size="25rpx"
              :label="itemData.academy_type"
              space="5rpx"
              color="#FCB138"
              label-color="#FCB138"
              label-size="22rpx"></u-icon>
          </view>
          <view class="bg-#feefd7 p10rpx rounded-6rpx">
            <u-icon
              name="home"
              size="25rpx"
              :label="itemData.rank_type"
              space="5rpx"
              color="#FCB138"
              label-color="#FCB138"
              label-size="22rpx"></u-icon>
          </view>
        </view>
      </view>
      <view class="flex address mb-10rpx">
        <u-icon
          :style="{ width: flag ? '40%' : '80%' }"
          name="map-fill"
          size="24rpx"
          space="10rpx"
          color="#969dab"
          :label="itemData.school_address || '长沙市芙蓉区农大路一号'"
          label-color="#969dab"
          label-size="22rpx"></u-icon>
        <u-icon
          v-if="flag"
          class="w-40% ml-2rpx"
          name="phone-fill"
          size="24rpx"
          space="10rpx"
          color="#969dab"
          :label="itemData.phone || '暂无电话'"
          label-color="#969dab"
          label-size="22rpx"></u-icon>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts" name="school-item">
import type { IBKSchoolData } from '@/api/project/index/type'
import { assembleImgData, getSystemImg } from '@/utils'

defineProps<{
  itemData: IBKSchoolData
  imgSize?: {
    width: string
    height: string
  }
  collect?: boolean
  flag: boolean
}>()

const emits = defineEmits(['clickCollect'])

const isCollect = (flag: boolean) => {
  emits('clickCollect', flag)
}
</script>

<style lang="scss" scoped>
.address :deep(.u-icon__label) {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
</style>
