<template>
  <view class="ma w710rpx h300rpx rounded-15rpx bg-white rounded-md mt-32rpx p-30rpx box-border">
    <!-- 订单标题和金额 -->
    <view class="flex justify-between items-center mb-24rpx">
      <text class="text-33rpx font-bold text-#333333">{{ item.description }}</text>
      <text class="text-#FF776A fw-bold">¥{{ item.pay_price / 100 }}</text>
    </view>

    <!-- 订单信息 -->
    <view class="text-#999999 text-24rpx mb-16rpx">
      <view>订单编号：{{ item.out_order_no }}</view>
      <view class="m-y-16rpx">订单时间：{{ formatDate(item.creatorTime) }}</view>
    </view>

    <u-divider />

    <!-- 订单状态和操作 -->
    <view class="flex justify-between items-center">
      <text class="text-#333333 text-28rpx">{{ item.pay_status }}</text>
      <view
        v-if="item.pay_status === '下单成功'"
        class="b-1rpx b-#459AF7 b-solid text-#459AF7 rounded-full px-24rpx py-8rpx text-26rpx"
        @click="twoPay(item)"
        >立即支付</view
      >
    </view>
  </view>
</template>

<script setup lang="ts" name="OrderItem">
import useDateFormatter from '@/hooks/useDateFormatter'
import type { IOrderData } from '@/api/project/member/type'
import { getPayStatus, wxPay } from '@/api/project/member'
import { showToast, showToastBack } from '@/utils'
import type { WxPayParams } from '@/api/project/member/type.d'
defineProps<{
  item: IOrderData
}>()

const { formatDate } = useDateFormatter()

const placeData = ref<WxPayParams>({
  description: '',
  appId: 'wxe04186c0f2ce20d3',
  code: '',
  out_order_no: '',
  goods_module_id: '',
  goods_price_field: '',
  goods_id: '',
})

// 支付
const toPay = (val: any) => {
  uni.requestPayment({
    provider: 'wxpay',
    orderInfo: {} as any,
    ...val,
    success: (res: any) => {
      // showToast('支付成功')
      getPayStatus(placeData.value.out_order_no).then(res => {
        if (res.code === 200) {
          showToastBack('支付成功')
        }
      })
    },
    fail: (err: any) => {
      showToast('支付失败', err)
    },
  })
}

const twoPay = (item: IOrderData) => {
  toPay({
    timeStamp: new Date().getTime(),
    nonceStr: uni.$u.randomString(32),
    package: `prepay_id=${item.requestData.payer.openid}`,
    signType: 'MD5',
    paySign: '后端计算的签名',
  })
}

// 下单
const toPlace = (item: IOrderData) => {
  uni.login({
    success: res => {
      placeData.value.code = res.code
      placeData.value.out_order_no = item.out_order_no
      placeData.value.goods_module_id = item.goods_module_id
      placeData.value.goods_price_field = item.goods_price_field
      placeData.value.goods_id = item.goodsInfo._id
      placeData.value.description = item.description
      wxPay(placeData.value).then(res => {
        toPay(res.data)
      })
    },
  })
}
</script>

<style lang="scss" scoped></style>
