<template>
  <view>
    <!-- titlebar -->
    <up-navbar
      :title="title"
      :border="false"
      color="#fff"
      :title-style="titleStyle"
      :bg-color="bgColor"
      auto-back
      placeholder>
    </up-navbar>
    <view class="relative">
      <!-- 题列表 -->
      <TopicList
        :show-banner="showBanner"
        :list="subjectList"
        :type-name="title"
        @pick-title="pickChange">
        <!-- 具名插槽1 -->
        <template #search>
          <search
            class="margin-b-24 flex-shrink-0"
            width="auto"
            :custom-style="searchStyle"
            :value="searchKey"
            @input="handleInput"
            @search="handleSearch" />
        </template>
        <!-- 具名插槽2 -->
        <template #list>
          <div class="overflow-auto pb30rpx">
            <view
              v-if="filteredList?.length === 0"
              class="h50vh flex flex-col justify-center items-center">
              <u-empty></u-empty>
            </view>
            <view
              v-for="(item1, index1) in filteredList"
              :key="index1"
              class="section-item flex padding-24"
              @tap="gotoTest(item1)">
              <text class="text_ellipsis w-full font-bold">
                {{ item1.name }}
              </text>
            </view>
          </div>
        </template>
      </TopicList>
    </view>
  </view>
</template>

<script setup lang="ts" name="practice-test">
import TopicList from './TopicList.vue'
import search from './Search.vue'
import type { PaperItem, Subjects, TestingItemData } from '@/api/project/exercises/type'
import useExercisesStore from '@/store/modules/exercises'
const props = defineProps({
  title: {
    type: String,
    default: '暂无数据',
  },
  requestFn: {
    type: Function,
    default: () => {},
  },
  showBanner: {
    type: Boolean,
    default: true,
  },
})
//
const emit = defineEmits(['goTest'])
const { title, showBanner, requestFn } = props
const useExercises = useExercisesStore()
const titleStyle = computed(() => ({ fontSize: '36rpx', color: 'black' }))
const bgColor = ref('#e2eaff')
const ListActiveIndex = ref(0)
const examList = ref<TestingItemData[]>([])
const subjectList = computed(() => {
  return useExercises.subjects as Subjects[]
})

function gotoTest(exam: PaperItem) {
  emit('goTest', exam)
}
// 获取数据
const getData = () => {
  requestFn(subjectList.value[ListActiveIndex.value].subject).then((res: any) => {
    examList.value = res.data.list
  })
}
// 组件挂载实现数据加载
onMounted(() => {
  getData()
})
// 关键字
const searchKey = ref('')

// 搜索框样式
const searchStyle = ref({
  padding: '16rpx 24rpx',
  margin: '24rpx 16rpx 0',
})
// 计算属性
const filteredList = computed(() => {
  if (!examList?.value[0]?.tableField106) {
    return []
  }
  return examList?.value[0]?.tableField106.filter(item =>
    item.name.toLowerCase().includes(searchKey.value.toLowerCase())
  )
})

// 搜索框
function handleInput(value: string) {
  searchKey.value = value // 更新搜索文本
}

// 处理搜索
function handleSearch(value: string, type: string) {
  console.log('Search triggered:', value, type) // 搜索触发时的处理
}

// pickchange
function pickChange(e: number) {
  if (ListActiveIndex.value === e) {
    return
  }
  ListActiveIndex.value = e
  getData()
}
</script>

<style lang="scss" scoped>
@import '@/common/css/index.scss';
@import '@/common/css/project.scss';
.section-item {
  line-height: 1;
  border-bottom: 1px solid #dedede;
  font-size: 30rpx;
  color: #333;
  > text {
    display: inline-block;
  }
  &::after {
    width: 6em;
    font-family: uicon-iconfont;
    color: $u-theme-color;
    font-size: 26rpx;
    text-align: right;
    content: '去答题\e605';
  }
  &.continue::after {
    flex-shrink: 0;
    content: '继续答题\e605';
  }
}
:v-deep.u-empty {
  height: 100%;
}
</style>
