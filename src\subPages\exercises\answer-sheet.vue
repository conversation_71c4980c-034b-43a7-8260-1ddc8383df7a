<template>
  <scroll-view scroll-y scroll-with-animation :scroll-top="scroll" class="answer-sheet container">
    <up-navbar
      :title="tabbarTitle"
      :border="false"
      color="#fff"
      :title-style="titleStyle"
      placeholder
      @left-click="goBack">
    </up-navbar>
    <LoadingPage
      empty-text="暂无题目"
      loading-text="题目加载中..."
      :loading="loading"
      :empty="empty">
      <!-- 插槽内容 -->
      <template #default="{ show }">
        <view v-if="show">
          <!-- header -->
          <view class="relative">
            <InfoBar
              :accuracy-rate="accuracyRate"
              :time="formatTime(time)"
              :cur-index="curIndex"
              :is-count-down="isCountDown"
              :test-and-exam="testAndExam"
              :topic-list-length="topicList.length"
              :test-type="testType"></InfoBar>
            <!-- body -->
            <view class="pb120rpx">
              <SheetItem
                ref="sheetItem"
                :detail="curTopic"
                :index="curIndex + 1"
                @book-mark="bookMark"
                @check-option="getUserResult"></SheetItem>
              <!-- 答案 -->
              <view
                v-if="testType === 'report'"
                class="topic-result flex items-center flex-wrap mt20rpx">
                <mp-html
                  class="correct h-fit margin-r-24 p-l-10rpx"
                  :preview-img="false"
                  :content="`正确答案：${arrayToString(curTopic.answer)}`" />
                <view
                  class="flex h-fit text-state flex-wrap"
                  :class="{
                    correct: correct(),
                    wrong: !correct(),
                  }">
                  <view class="w-fit p-2rpx p-l-10rpx">我的答案是：</view>
                  <mp-html
                    class="margin-r-10"
                    :content="`${arrayToString([curTopic.user_answer])}`" />
                  <u-icon v-if="correct()" name="checkmark" color="#61c5a1" size="28rpx" />
                  <u-icon v-else name="close" size="28rpx" color="#ff776a" />
                </view>
              </view>
              <!-- 通过计算属性isInputVisible来判断是否需要当前题目需要输入框组件 -->
              <view v-show="isInputVisible" class="mt20rpx talk-input-container">
                <view class="input-label">答题区域</view>
                <TextArea
                  ref="textArea"
                  :is-analysis="isAnalysis"
                  :is-input-visible="isInputVisible"
                  :cur-index="curIndex"
                  :short-answers="shortAnswers"
                  class="input-box"
                  @keyboard-height-change="handleKeyboardHeightChange"
                  @update-answer="handleUpdateAnswer"></TextArea>
              </view>
              <!-- 解析 -->
              <view v-if="isAnalysis">
                <view class="analysis-wrap mt20rpx">
                  <view class="analysis-head">
                    试题解析
                    <text class="analysis-correction color-theme"> 纠错 </text>
                  </view>
                  <view class="analysis-main margin-t-20">
                    <view
                      v-if="curTopic.answer && curTopic.answer.length > 0"
                      class="analysis-answer">
                      正确答案：{{ removeHtmlTags(arrayToString(curTopic.answer)) }}
                    </view>
                    <view class="analysis-cont margin-t-20">
                      解析:
                      <view class="hide-warp after">
                        <mpHtml
                          class="context u-main-color"
                          :content="replaceSpanWithLatex(curTopic.analysis)">
                        </mpHtml>
                      </view>
                    </view>
                  </view>
                </view>
                <!-- 考点 -->
                <view class="analysis-wrap mt20rpx">
                  <view class="analysis-head"> 考点 </view>
                  <view class="analysis-main margin-t-20">
                    <mpHtml
                      class="context u-main-color"
                      :content="arrayToString(curTopic.knowledgePoints)"></mpHtml>
                  </view>
                </view>
              </view>
            </view>
          </view>
          <!-- tabbar -->
          <TabbarItem
            :cur-index="curIndex"
            :topic-list-length="topicList.length"
            :test-type="testType"
            :test-and-exam="testAndExam"
            @hand-exam="handExam"
            @pick-test="pickTest"
            @show-analysis="showAnalysis"
            @show-popup="showPopped"></TabbarItem>
          <!-- 弹窗 -->
          <u-popup :show="showPopup" :round="10" mode="bottom" closeable @close="showPopup = false">
            <view class="answer-card">答题卡</view>
            <view class="half">
              <u-grid :border="false" col="5">
                <view
                  v-for="(topic, i) in topicList"
                  :key="i"
                  class="topic-item"
                  :class="topic?.state"
                  @tap="pickerByPopup(i)">
                  {{ i + 1 }}
                </view>
              </u-grid>
            </view>
          </u-popup>
          <!-- 确认框 -->
          <u-modal
            :show="showModal"
            show-cancel-button
            confirm-color="#606266"
            cancel-color="#2979ff"
            cancel-text="继续答题"
            confirm-text="确认交卷"
            :title="modalTitle"
            :content="modalContent"
            @cancel="showModal = false"
            @confirm="confirm" />
        </view>
      </template>
    </LoadingPage>
    <PointsModel
      :show="pointsModelShow"
      type="reduce"
      :flag="pointsModelobj.flag"
      :points="pointsModelobj.points"
      :content="pointsModelobj.content"
      @close="pointsModelShow = false" />
  </scroll-view>
</template>

<script setup lang="ts" name="answer-sheet">
// 导入mpHtml
import mpHtml from './components/mp-html/mp-html.vue'
// tabbar
import TabbarItem from './components/TabbarItem.vue'
// tabbar
import InfoBar from './components/InfoBar.vue'
// 题目选项
import SheetItem from './components/SheetItem.vue'
// 简答题的输入框
import TextArea from '@/components/TextArea.vue'
// 工具类
import { removeHtmlTags, replaceSpanWithLatex } from '@/utils/index'
// 加载页面
import LoadingPage from '@/components/LoadingPage.vue'
import { useExamStore } from '@/store/modules/useExamStore' // 导入保存当前的encode的 store
// 积分弹窗
import PointsModel from '@/components/PointsModel.vue'
// 接口
import {
  calculationExercise,
  createCollect,
  createResultCard,
  deleteCollect,
  getCardItemById,
  getRandomList,
  getRealExamDetail,
  getTopicListByIds,
  submitResultCard,
} from '@/api/project/exercises'
// 类型
import type {
  CardSubmitData,
  CreateResultCardType,
  ShortAnswer,
  TopicItem,
  TopicResultType,
} from '@/api/project/exercises/type'
// 工具类
import { arraysEqual, showToast } from '@/utils'
//
import useExercisesStore from '@/store/modules/exercises'
// 积分
import useMemberStore from '@/store/modules/member'
import useUserStore from '@/store/modules/user' // 用户的所有的信息的store
const useExercises = useExercisesStore()
const memberStore = useMemberStore()
const userStore = useUserStore() // 用户信息的store
const examStore = useExamStore() // 使用 Pinia store,这是从store中获取当前enCode
// 这里是传递过来的参数,通过navigateTo传递过来的,用来组装请求
const competitionEnCode = ref('')
const teamEnCode = ref('')
// 加载中
const loading = ref(true)
// 是否空
const empty = ref(false)
// 组卷ID
const paperId = ref('')
// 正确率
const accuracyRate = ref(0.0)
// 答题卡
const showPopup = ref(false)
// 提示框
const showModal = ref(false)
// 当前下标
const curIndex = ref(0)
// 题目list
const topicList = ref<TopicItem[]>([])
// 当前题目
const curTopic = ref({} as TopicItem)
// 计时器
const time = ref(0)
// 答题模式
const testType = ref()
// 编辑可否
const editable = ref(true)
// 是否倒计时
const isCountDown = ref(false)
// 是否倒计时
const tabbarTitle = ref('')
// 倒计时时间
const countDown = ref(600)
// modal
const modalTitle = ref('')
const modalContent = ref('')
// 解析tabbar
const isAnalysis = ref(false)
// testTitle
const testTitle = ref('')
// title样式
const titleStyle = computed(() => ({ fontSize: '36rpx', color: 'black' }))
// 拿到子组件的ref对象
const textArea = ref<InstanceType<typeof TextArea> | null>(null) // 引用子组件
// 存储答案的数组
const shortAnswers = ref<ShortAnswer[]>([])
// 当前简答题的答案
const answer = ref<ShortAnswer | null>(null) // 初始化为 null

const scroll = ref(0)
// 题目类型
const QuestionTypes = {
  SINGLE_CHOICE: '单选题',
  MULTIPLE_CHOICE: '多选题',
  READING_COMPREHENSION: '阅读理解',
  TRUE_FALSE: '判断题',
  SHORT_ANSWER: '简答题',
  FILL_IN_THE_BLANK: '填空题', // 填空题
  TRAN__SLATION: '翻译题',
  WRITING: '写作题',
}
// 积分弹窗
const pointsModelShow = ref(false)
const pointsModelobj = ref({
  flag: false,
  points: 0,
  content: '',
})

const visibleTypes = [
  // 输入框可见的题型
  QuestionTypes.SHORT_ANSWER,
  QuestionTypes.FILL_IN_THE_BLANK,
  QuestionTypes.TRAN__SLATION,
  QuestionTypes.WRITING,
]
// 计算属性，用于判断输入区域是否可见
const isInputVisible = computed(() => {
  const isVisibleType = visibleTypes.includes(curTopic.value.type)

  return (
    (isVisibleType && testType.value !== 'analysis') ||
    (isVisibleType && testType.value === 'report')
  )
})

// 键盘高度
function handleKeyboardHeightChange(height: number) {
  console.log('父组件监听到键盘高度变化:', height)
  // 确保 scroll.value 在 0 到 height 之间
  scroll.value = Math.max(0, Math.min(scroll.value + height, height))
  console.log('更新后的 scroll 值:', scroll.value)
}

// 处理简答题更新答案的函数  data: { html: string; text: string }这些是子组件中的uniapp中的事件传递过来的参数
const handleUpdateAnswer = (data: { html: string; text: string }) => {
  const isTextEmpty = !data.text || data.text.trim() === '' || data.text === '↵'
  const isHtmlEmpty = !data.html || data.html.trim() === '' || data.html === '<p><br></p>'

  // 检查是否初次添加答案
  const existingAnswerIndex = shortAnswers.value.findIndex(
    (ans: any) => ans.questionId === curIndex.value
  )

  // 初次添加答案且答案为空时的处理
  if (existingAnswerIndex === -1 && isTextEmpty && isHtmlEmpty) {
    console.warn('首次添加答案时，答案文本和HTML均为空，未更新答案')
    return // 如果为空，返回，不进行后续操作
  }

  // 创建答案对象
  if (userStore.userInfo?.id) {
    const newAnswer: ShortAnswer = {
      topicId: curTopic.value._id,
      questionId: curIndex.value,
      questionType: curTopic.value.type,
      answerText: data.text, // 用户输入的答案文本
      answerHtml: data.html, // 用户输入的答案HTML格式
      creatorUserId: userStore.userInfo.id,
      grade: curTopic.value.grade,
      subject: curTopic.value.subject,
    }

    if (existingAnswerIndex !== -1) {
      // 如果答案已存在，更新答案
      if (isTextEmpty && isHtmlEmpty) {
        // 如果用户清空了答案，移除该答案
        // shortAnswers.value.splice(existingAnswerIndex, 1);
        console.warn('答案不能被清空')
      } else {
        // 更新已有答案
        shortAnswers.value[existingAnswerIndex] = newAnswer // 使用 newAnswer 替换
        console.log('当前答案已更新', shortAnswers.value)
      }
    } else {
      // 如果答案题号不存在，添加新答案
      curTopic.value.state = 'select' // 设置当前题目的状态为已选择
      shortAnswers.value.push(newAnswer) // 添加 newAnswer
      console.log('答案已添加', shortAnswers.value)
    }

    // 更新当前答案的引用
    answer.value = newAnswer // 更新当前答案
  } else {
    console.log('用户未登录')
  }
}
// 判断用户是否登录
onMounted(() => {
  // 在适当的地方调用 checkLogin 方法 ,这是store中判断是否登录的方法
  userStore.checkLogin()
})
// 在 Vue 实例中定义计算属性
const testAndExam = computed(() => {
  return testType.value === 'exam' || testType.value === 'test'
})
// 收藏
const throttledBookMark = throttle(() => {
  if (curTopic.value.collect) {
    deleteCollect(curTopic.value.collect)
      .then(({ code, data }) => {
        if (code === 200) {
          curTopic.value.collect = ''
          showToast('取消收藏成功')
        } else {
          showToast('取消收藏失败，请重试', 'error')
        }
      })
      .catch(error => {
        showToast('取消收藏失败，请重试', 'error')
        console.error('Error deleting collect:', error)
      })
  } else {
    const data = { topicId: curTopic.value._id }
    createCollect(data)
      .then(({ code, data }) => {
        if (code === 200) {
          curTopic.value.collect = data
          showToast('收藏成功')
        } else {
          showToast('收藏失败，请重试', 'error')
        }
      })
      .catch(error => {
        showToast('收藏失败，请重试', 'error')
        console.error('Error creating collect:', error)
      })
  }
}, 1000) // 设置节流时间间隔为 1000ms
// 显示答题卡
function showPopped() {
  showPopup.value = true
}
function correct() {
  return arraysEqual(curTopic.value.type, [curTopic.value.user_answer], curTopic.value.answer)
}
// 答案解析
function arrayToString(arr: any[], separator = ''): string {
  if (!Array.isArray(arr)) {
    return '暂无'
  }
  return arr.join(separator)
}
// showAnalysis
function showAnalysis() {
  isAnalysis.value = !isAnalysis.value
}
// 节流
function throttle(func: Function, limit: number) {
  let inThrottle = false
  return function () {
    if (!inThrottle) {
      inThrottle = true
      func()
      setTimeout(() => (inThrottle = false), limit)
    }
  }
}
// 收藏点击
function bookMark() {
  throttledBookMark()
}
// 交卷
function handExam() {
  const unansweredCount = countUnansweredQuestions('all')
  const unansweredCountCur = countUnansweredQuestions('cur')
  if (curTopic.value.type === QuestionTypes.READING_COMPREHENSION && unansweredCountCur > 0) {
    showToast('请先完成作答', 'error')
    return
  }
  if (topicList.value.filter(item => !item.state).length === topicList.value.length) {
    showToast('请先作答', 'error')
    return
  }
  if (unansweredCount === topicList.value.length) {
    // 当没有未答的题目数量等于题目总数时，提示用户至少回答一道题目
    showToast('至少回答一道题目', 'error')
    return
  }
  showModal.value = true
  if (unansweredCount > 0) {
    modalTitle.value = '你还有题目没有做完，确认交卷吗'
    modalContent.value = `还有${unansweredCount}道题目没有完成，是否坚持一会儿`
  } else {
    modalTitle.value = '题目作答完毕，确认交卷吗'
    modalContent.value = ''
  }
}
// 用户剩余题数
function countUnansweredQuestions(type: string): number {
  let unansweredCount = 0
  // 定义一个辅助函数，用于判断题目是否已回答,只能判断单选多选判断题
  const isAnswered = (question: TopicItem) => {
    return question.user_answer && question.user_answer.length !== 0
  }

  // 判断简答题是否已回答
  const isAnsweredShortAnswer = (question: TopicItem) => {
    return shortAnswers.value.find((ans: any) => ans.topicId === question._id)
  }

  // 根据传入的类型获取相关话题
  const topics = type === 'all' ? topicList.value : [curTopic.value]

  // 判断当前题目是否已回答
  const curType = curTopic.value.type
  switch (type) {
    case 'all':
      for (const topic of topics) {
        const topicType = topic.type

        // 处理单选多选判断题
        if (
          topicType === QuestionTypes.SINGLE_CHOICE ||
          topicType === QuestionTypes.MULTIPLE_CHOICE ||
          topicType === QuestionTypes.TRUE_FALSE
        ) {
          if (!isAnswered(topic)) {
            unansweredCount++
          }
        } else {
          // 针对简答题
          if (!isAnsweredShortAnswer(topic)) {
            unansweredCount++
          }
        }
      }
      break
    case 'cur':
      if (
        curType === QuestionTypes.SINGLE_CHOICE ||
        curType === QuestionTypes.MULTIPLE_CHOICE ||
        curType === QuestionTypes.TRUE_FALSE
      ) {
        unansweredCount = isAnswered(curTopic.value) ? 0 : 1
      } else {
        // 针对当前题目的其他类型的逻辑处理
        // 针对简答题
        console.log('其他类型的题目', curTopic.value)
        if (isAnsweredShortAnswer(curTopic.value)) {
          unansweredCount++
        }
      }
      break
  }

  return unansweredCount
}
let timer: string | number | NodeJS.Timeout | null | undefined = null
// 代码逻辑重构

// 时间格式化
const formatTime = (time: number) => {
  function padStart(num: number) {
    return (num | 0).toString().padStart(2, '0') || '00'
  }
  return `${padStart(time / 60 / 60)}:${padStart((time / 60) % 60)}:${padStart(time % 60)}`
}
// // 计时结束提交
// const submitResults = (result: string) => {
//   // 提交结果的逻辑
// }
// 清除定时器
const clearTimer = () => {
  if (timer) {
    clearInterval(timer)
    timer = null
  }
}
// 计时器定义
const createTimer = () => {
  if (!editable.value) return
  if (isCountDown.value) time.value = countDown.value
  let updateTime = null
  const submit = () => {
    clearTimer()
    confirm()
  }
  if (isCountDown.value) {
    updateTime = () => {
      if (time.value <= 0) submit()
      return --time.value
    }
  } else {
    // 最大8小时
    updateTime = () => {
      if (++time.value >= 28800) submit()
      return time.value
    }
  }
  timer = setInterval(() => updateTime(), 1000)
}
// 答题卡提交前退出提示
function goBack() {
  if (testType.value === 'exam' || testType.value === 'test') {
    uni.showModal({
      title: '提示',
      content: '当前题目未提交，是否退出？',
      success: (res: any) => {
        if (res.confirm) {
          clearTimer()
          uni.navigateBack()
        } else if (res.cancel) {
          return true
        }
      },
    })
  } else {
    uni.navigateBack()
  }
}
// 提交
function setLoadingState(length: any) {
  setTimeout(() => {
    loading.value = false
    if (length === 0) empty.value = true
  }, 300)
}
// 接收子组件答案
function getUserResult(index: number, answer: string) {
  // 解析模式不更新答题卡
  if (testType.value === 'analysis' || testType.value === 'report') {
    return
  }
  const currentTopic =
    curTopic.value.type === QuestionTypes.SINGLE_CHOICE ||
    curTopic.value.type === QuestionTypes.MULTIPLE_CHOICE ||
    curTopic.value.type === QuestionTypes.TRUE_FALSE
      ? curTopic.value
      : curTopic.value.tableField116[index]
  if (currentTopic.type === QuestionTypes.MULTIPLE_CHOICE) {
    // 切换答案选中状态
    if (!currentTopic.user_answer) {
      currentTopic.user_answer = [answer]
    } else if (currentTopic.user_answer.includes(answer)) {
      // 取消选中
      currentTopic.user_answer = currentTopic.user_answer.filter((item: string) => item !== answer)
    } else {
      currentTopic.user_answer.push(answer)
    }
  } else {
    // 直接赋值给 user_answer 数组
    currentTopic.user_answer = [answer]
  }
  // 更新答题卡状态
  if (countUnansweredQuestions('cur') === 0) {
    curTopic.value.state = 'select'
  }
}
watch(topicList, async () => {
  if (topicList.value.length > 0 && testType.value !== 'report') {
    if (testType.value === 'random') {
      // 随机刷题 刷一次扣一次
      const res = await memberStore.pointsAction('刷题1道', topicList.value.length)
      if (res?.flag) {
        pointsModelobj.value.flag = res!.flag
      } else {
        pointsModelobj.value = res as any
      }
      pointsModelShow.value = true
    } else {
      // 其他刷题 读配置看有没有刷过
      const res = await memberStore.pointsAction('刷题1道', topicList.value.length, paperId.value)
      if (!res.is) {
        if (res?.flag) {
          pointsModelobj.value.flag = res!.flag
        } else {
          pointsModelobj.value = res as any
        }
        pointsModelShow.value = true
      }
    }
  }
})
onLoad((e: any) => {
  // 获取路径参数
  const { type, duration, examId, title, answerType } = e
  // 设置组卷ID
  paperId.value = examId
  // 答题类型
  testTitle.value = answerType
  /// 设置标题
  tabbarTitle.value = title
  // 设置题目类型
  testType.value = type
  // 模拟考试或练习
  if (type === 'exam' || type === 'test') {
    handleExamOrTest(type, duration, examId)
  }
  // 随机练习
  if (type === 'random') {
    testTitle.value = title
    handleRandomPractice(e)
  }
  if (type === 'analysis') {
    const parsedData = JSON.parse(decodeURIComponent(e.examId))
    handleAnalysis(parsedData, type)
  }
  if (type === 'report') {
    isAnalysis.value = true
    setLoadingState(useExercises.listData.length)
    topicList.value = useExercises.listData
    time.value = useExercises.itemData?.answerTime
    curIndex.value = useExercises.defaultIndex
    curTopic.value = topicList.value[curIndex.value]
    curTopic.value.testType = type
  }
})
// 考试逻辑处理
function handleExamOrTest(type: string, duration: number, examId: string) {
  if (type === 'exam') {
    isCountDown.value = true
    countDown.value = duration * 60
  }
  getRealExamDetail(examId).then(({ code, data }) => {
    // 设置题目列表和当前题目
    if (code === 200) {
      setLoadingState(data.length)
      topicList.value = data
      curTopic.value = topicList.value[curIndex.value]
      curTopic.value.testType = type
    }
  })
  createTimer()
}
// 随机刷题逻辑处理
function handleRandomPractice(e: any) {
  // 解析传递过来的对象数据
  const parsedData = JSON.parse(decodeURIComponent(e.data))
  getRandomList(parsedData.grade, parsedData.subject, parsedData.num.toString()).then(
    ({ code, data }) => {
      if (code === 200) {
        setLoadingState(data.length)
        topicList.value = data
        curTopic.value = topicList.value[curIndex.value]
        curTopic.value.testType = 'random'
      }
    }
  )
  createTimer()
}
// 我的收藏和错题逻辑处理
function handleAnalysis(examId: any, type: string) {
  isAnalysis.value = true
  getTopicListByIds(examId).then(({ code, data }) => {
    if (code === 200) {
      setLoadingState(data.list.length)
      topicList.value = data.list
      curTopic.value = topicList.value[curIndex.value]
      curTopic.value.testType = type
    }
  })
}
// 正确率计算
function calculateAccuracy(count: number) {
  let correctCount = 0
  let answeredCount = 0
  // 定义一个辅助函数，用于计算题目的回答情况
  const countAnswers = (element: TopicItem) => {
    if (element.user_answer) {
      answeredCount++
      if (element.state === 'select') {
        correctCount++
      }
    }
  }
  // 遍历题目列表
  topicList.value.forEach(element => {
    if (element.type === '单选题' || element.type === '多选题') {
      countAnswers(element)
    } else {
      // 假设其他类型的题目是阅读理解题
      if (Array.isArray(element.tableField116)) {
        element.tableField116.forEach(countAnswers)
      }
    }
  })
  // 计算准确率，处理 answeredCount 为零的情况
  const accuracy =
    answeredCount > 0 ? ((correctCount / (answeredCount + count)) * 100).toFixed(2) : '0.00'
  accuracyRate.value = Number(accuracy)
}

// 更新答题卡状态
function updateCardState() {
  const currentTopic =
    curTopic.value.type === QuestionTypes.SINGLE_CHOICE ||
    curTopic.value.type === QuestionTypes.MULTIPLE_CHOICE
      ? curTopic.value
      : curTopic.value.tableField116
  let right = true
  if (
    curTopic.value.type === QuestionTypes.SINGLE_CHOICE ||
    curTopic.value.type === QuestionTypes.MULTIPLE_CHOICE
  ) {
    right = arraysEqual(curTopic.value.type, curTopic.value.answer, curTopic.value.user_answer)
  } else {
    for (const element of currentTopic as any) {
      if (!arraysEqual(element.type, element.answer, element.user_answer)) {
        element.state = 'wrong'
        // element.state = 'select'
        right = false
      } else {
        element.state = 'select'
      }
    }
  }
  curTopic.value.state = right ? 'select' : 'wrong'
  // curTopic.value.state = 'select'
  calculateAccuracy(0)
}

// 选题
function pickTest(e: number) {
  // 判断当前题目是否完成
  /* if (testAndExam.value && countUnansweredQuestions('cur')) {
    showToast('请先完成当前题目', 'error')
    return
  } */
  if (e === 1) {
    // 下一题
    if (curIndex.value >= topicList.value.length - 1) {
      showToast('已经是最后一题', 'error')
      return
    }
    // 更新答题卡状态
    if (testAndExam.value) {
      updateCardState()
    } else {
      calculateAccuracy(0)
    }
    curIndex.value++
    curTopic.value = topicList.value[curIndex.value]
    curTopic.value.testType = testType.value
    // 调用子组件的方法,处理简答题答题的回显和清除
    // console.warn("父组件的的事件下标: ",curIndex.value);
    // 判断是不是简答题,同时拿到textArea组件的对象,并调用子组件的方法,回显答案
    if (isInputVisible.value) {
      if (textArea.value) {
        console.log('父组件中调用子组件的方法,回显答案')
        textArea.value.someMethod(curIndex.value, isInputVisible.value) // 确保子组件的方法已经暴露
      }
    }
  } else {
    // 上一题
    if (curIndex.value <= 0) {
      showToast('已经是第一题', 'error')
      return
    }
    curIndex.value--
    curTopic.value = topicList.value[curIndex.value]
    // 添加testType属性
    curTopic.value.testType = testType.value
    // 调用子组件的方法,处理简答题答题的回显和清除
    console.log('父组件的的下标: ', curIndex.value)

    // 判断是不是简答题,同时拿到textArea组件的对象,并调用子组件的方法,回显答案
    if (isInputVisible.value) {
      nextTick(() => {
        if (textArea.value) {
          textArea.value.someMethod(curIndex.value, isInputVisible.value) // 确保子组件的方法已经暴露
        }
      })
    }
  }
}

// 点击答题卡跳转到指定题目
function pickerByPopup(e: number) {
  // 考试模式不允许跳转
  // if (testAndExam.value) return
  // 其他可以点击答题卡跳转题目
  showPopup.value = false
  curIndex.value = e
  curTopic.value = topicList.value[curIndex.value]
  curTopic.value.testType = testType.value
  // 调用子组件的方法,处理简答题答题的回显和清除
  if (isInputVisible.value) {
    nextTick(() => {
      if (textArea.value) {
        textArea.value.someMethod(curIndex.value, isInputVisible.value) // 确保子组件的方法已经暴露
      }
    })
  }
}
// 交卷弹窗确认按钮
function confirm() {
  // 更新答题卡
  if (testAndExam.value) {
    updateCardState()
  }
  calculateAccuracy(countUnansweredQuestions('all'))
  clearTimer()
  const userResult = getUserResultList()
  // 计算分数
  if (testType.value !== 'random') {
    calculationExercise(paperId.value, userResult).then((e: any) => {
      const result = totalScore(e.data)
      submitCard(result.totalScore, userResult, e.data)
    })
  } else {
    // 随机答题
    submitCard(0, userResult, null)
  }
}
// 用户答题处理
function userAnswerHandle(
  userResult: { [key: string]: any },
  scoreData: { [key: string]: any } | null // 允许 scoreData 为 null
): TopicResultType[] {
  const result = [] as any

  for (const topicId in userResult) {
    if (Object.prototype.hasOwnProperty.call(userResult, topicId)) {
      const answers = userResult[topicId]

      // 默认分数为0
      let score = 0

      // 检查 scoreData 是否为 null
      if (scoreData !== null && topicId in scoreData) {
        // 如果分数是一个对象，处理序号
        if (typeof scoreData[topicId] === 'object' && !Array.isArray(scoreData[topicId])) {
          let index = 0 // 初始化索引
          for (const key in scoreData[topicId]) {
            if (Object.prototype.hasOwnProperty.call(scoreData[topicId], key)) {
              // 对应的答案可能在 userResult 中
              const userAnswer = answers[index] !== undefined ? String(answers[index]) : ''
              result.push({ topicId, userAnswer, score: scoreData[topicId][key], index })
              index++ // 增加索引
            }
          }
          continue // 完成后跳过到下一个 topicId
        } else {
          // 直接获取分数
          score = scoreData[topicId]
        }
      }

      // 处理用户答案
      if (Array.isArray(answers)) {
        // 处理情况 1: 多个用户回答（多选题）
        const userAnswersArray = answers.map((answer: any) => String(answer)) // 将所有答案转换为字符串数组
        result.push({ topicId, userAnswer: userAnswersArray.toString(), score }) // 只推送一次
      } else if (typeof answers === 'string') {
        // 处理情况 2: 单个用户回答（单选题）
        result.push({ topicId, userAnswer: answers, score })
      } else if (typeof answers === 'object' && answers !== null) {
        // 处理情况 3: 使用序号作为键的用户答案对象
        const userAnswersArray = Object.values(answers).map((answer: any) => String(answer)) // 将对象的值转换为数组
        result.push({ topicId, userAnswer: userAnswersArray, score }) // 只推送一次
      }
    }
  }
  return result
}

// 计分函数
function submitCard(score: number, userResult: { [key: string]: any }, scoreData: any) {
  // 创建数据对象，符合 CardSubmitData 的结构
  const data = {} as CreateResultCardType

  let correctCount = 0 // 记录正确题数
  let errorCount = 0 // 记录错误题数
  const answerList = userAnswerHandle(userResult, scoreData) // 用户答案处理函数,返回用户的答案数组
  // 计算正确和错误项
  answerList.forEach(answer => {
    if (answer.score === 1) {
      correctCount++
    } else {
      errorCount++
    }
  })
  // 组装请求负载data,发送post请求
  data.tableField112 = answerList // 评分项数组
  data.answerTime = time.value.toString() // 答题时间
  data.fid = paperId.value
  data.state = '提交' // 状态
  data.totalScore = score // 从参数获取分数
  data.accuracy = accuracyRate.value // 计算的准确率
  data.type = testTitle.value // 获取考试类型
  createResultCard(data).then(({ code, data }) => {
    if (code === 200) {
      showToast('提交成功', 'success')
      uni.$emit('clockIn', time.value.toString())
      getCardItemById(data).then((e: any) => {
        setTimeout(() => {
          uni.redirectTo({
            url: `/subPages/exercises/test-report?reportId=${encodeURIComponent(
              JSON.stringify(e.data.list[0])
            )}`,
          })
        }, 500)
      })
    }
  })
}
// 总分统计
function totalScore(data: { [key: string]: any }): {
  totalScore: number
  scores: { [key: string]: number }
} {
  const scores: { [key: string]: number } = {}
  let totalScore = 0

  for (const key in data) {
    if (Object.prototype.hasOwnProperty.call(data, key)) {
      const item = data[key]

      // 如果 item 是数字，直接累加到 totalScore
      if (typeof item === 'number') {
        scores[key] = item // 将分数存储到 scores 对象中
        totalScore += item // 累加到总分
      }
      // 如果 item 是对象，遍历它以计算分数
      else if (typeof item === 'object') {
        let itemScore = 0
        for (const subKey in item) {
          if (Object.prototype.hasOwnProperty.call(item, subKey)) {
            itemScore += item[subKey] // 累加子项的分数
          }
        }
        scores[key] = itemScore // 将子项的总分存储到 scores 对象中
        totalScore += itemScore // 累加到总分
      }
    }
  }

  return { totalScore, scores }
}
// 获取用户答题结果列表
function getUserResultList() {
  const results: { [key: string]: any } = {} // 创建一个结果对象
  topicList.value.forEach((item: any) => {
    // 先检查 state 是否存在
    if (!item.state) {
      if (item.type === QuestionTypes.READING_COMPREHENSION) {
        const answers: { [key: string]: string } = {}
        const userAnswers = item.tableField116
        // 遍历用户答案并存储在 answers 对象中
        for (let i = 0; i < userAnswers.length; i++) {
          answers[i] = '' // 使用序号作为键，用户答案作为值
        }
        results[item._id] = answers // 将答案对象赋值给题目 id
      } else {
        results[item._id] = ''
      }
    } else {
      // 如果有答题，继续处理
      if (item.type === QuestionTypes.READING_COMPREHENSION) {
        // 对于阅读理解题，返回题目 id 和用户答案
        const answers: { [key: string]: string } = {}
        // 假设 tableField116 是一个数组，包含每个问题的用户答案
        const userAnswers = item.tableField116.map((field: any) => field.user_answer)

        // 遍历用户答案并存储在 answers 对象中
        for (let i = 0; i < userAnswers.length; i++) {
          const userAnswer = userAnswers[i]
          answers[i] = userAnswer || '' // 使用序号作为键，用户答案作为值
        }
        results[item._id] = answers // 将答案对象赋值给题目 id
      } else if (item.type === QuestionTypes.SINGLE_CHOICE) {
        // 对于单选题，返回 { "id": "用户答案" }
        results[item._id] = item.user_answer // user_answer 是字符串
      } else if (item.type === QuestionTypes.MULTIPLE_CHOICE) {
        // 对于多选题，返回 { "id": ["用户答案1", "用户答案2", ...] }
        results[item._id] = item.user_answer // user_answer 是数组
      } else if (item.type === QuestionTypes.TRUE_FALSE) {
        results[item._id] = item.user_answer
      } else if (visibleTypes.includes(item.type)) {
        // 处理简答题及其他相似结构的题型
        const answerown = shortAnswers.value.find((answer: any) => {
          return answer.topicId === item._id
        })

        if (answerown) {
          results[item._id] = answerown.answerHtml // 如果找到，保存答案文本到结果
          console.log('找到答案：', answerown.answerHtml)
        } else {
          console.log(`没有找到对应的答案，topicId: ${item._id}`) // 处理未找到的情况
        }
      }
    }
  })
  // 返回结果对象
  return results
}
</script>

<style lang="scss" scoped>
@import '@/common/css/index.scss';
@import '@/common/css/project.scss';
$pd_l_r: 30rpx;

.container {
  padding-bottom: 120rpx;
  min-height: 100vh !important;
}

.answer-sheet {
  background: #f4f5f7;
  height: 100%;

  :where(.topic-item) {
    width: 70rpx;
    height: 70rpx;
    background-color: #f6f7f8;
    border-radius: 36rpx;
    opacity: 1;
    text-align: center;
    line-height: 70rpx;
    margin: 20rpx 40rpx;

    &.select {
      background: $u-theme-color;
      color: #fff;
    }

    &.wrong {
      background: #ff776a;
      color: #fff;
    }
  }

  .answer-card {
    font-size: 32rpx;
    font-weight: bold;
    color: #333333;
    text-align: center;
    margin-top: 16rpx;
  }
}

.half {
  max-height: 50vh;
  overflow: auto;
}

.analysis-wrap {
  padding: 24rpx $pd_l_r;
  background: #ffffff;

  .analysis-head {
    position: relative;
    line-height: 1.4;

    &::before {
      $w: 8rpx;
      content: '';
      position: absolute;
      width: $w;
      height: 100%;
      background-color: $u-theme-color;
      left: $w - $pd_l_r;
    }

    .analysis-correction {
      float: right;
      font-size: 30rpx;
    }
  }

  .analysis-main {
    padding: 0 0.5em;

    .analysis-answer {
      margin: 12rpx 0;
    }

    .hide-warp {
      .context {
        text-indent: 2em;
        min-height: 4em;

        ._root > view {
          font-size: 24rpx;
        }
      }
    }
  }
}

//
.topic-result {
  background: #fff;
  text-align: center;
  line-height: 70rpx;
  font-size: 28rpx;
  padding: 0 30rpx;

  ::v-deep .u-icon {
    padding-left: 24rpx;
  }

  .correct {
    color: #61c5a1;
  }

  .wrong {
    color: #ff776a;
  }

  text {
    margin-right: 1em;
    line-height: 2.5;
  }

  & > text {
    margin-right: 4em;
  }
}

.talk-input-container {
  width: 100%;
  background-color: #f9f9f9;
  padding: 20rpx;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);

  .input-label {
    font-size: 28rpx;
    color: #333;
    font-weight: bold;
  }

  .input-box {
    width: calc(100% - 80rpx) !important;
    height: 200rpx;
    border: 1rpx solid #dcdcdc;
    border-radius: 8rpx;
    background-color: #fff;
    font-size: 26rpx;
    color: #555;
    line-height: 1.6;
    resize: none;

    &:focus {
      border-color: #2979ff;
      box-shadow: 0 0 6rpx rgba(41, 121, 255, 0.5);
    }

    &::placeholder {
      color: #aaa;
    }
  }
}
</style>
