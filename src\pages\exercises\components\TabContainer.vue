<template>
  <view>
    <view class="wrap">
      <view class="u-tabs-box">
        <up-tabs
          ref="tabs"
          :line-color="`url(${getSystemImg(
            '/6747e9ed0a34815816f11159/67590691f8730075fead9d71'
          )}) 100% 100%`"
          :line-height="4"
          :list="list"
          :current="currentTab"
          :active-style="{
            color: '#4d9ef7',
            fontWeight: 'bold',
            transform: 'scale(1.05)',
          }"
          :inactive-style="{
            color: '#333333',
            transform: 'scale(1)',
          }"
          @change="debouncedChange"></up-tabs>
      </view>
      <swiper class="swiper-box" :current="swiperCurrent" @animationfinish="onAnimationFinish">
        <swiper-item
          v-for="(item, index) in list"
          :key="item.name + item.subject + index"
          class="swiper-item">
          <scroll-view scroll-y style="height: 100%; width: 100%" @scrolltolower="onReachBottom">
            <view class="p-20rpx">
              <LoadMoreList
                :ref="el => (loadMoreRefs[index] = el)"
                :request-fn="getSelectedExercise"
                empty-text="暂无题目"
                :request-params="{ subject: item.subject, module: item.name }">
                <template #default="{ list: exerciseList }">
                  <view v-if="!arrange" class="flex flex-wrap justify-between">
                    <TopicItemFlex
                      v-for="exercise in (exerciseList as SelectedExerciseItem[])"
                      :key="exercise._id"
                      :cover="exercise.cover?.[0]"
                      :title="exercise.topicName"
                      :tags="exercise.tags"
                      :price="exercise.price"
                      @tap="gotoTest(exercise)" />
                  </view>
                  <view v-if="arrange" class="flex flex-wrap justify-between">
                    <TopicItem
                      v-for="exercise in (exerciseList as SelectedExerciseItem[])"
                      :key="exercise._id"
                      :cover="exercise.cover?.[0]"
                      :title="exercise.topicName"
                      :tags="exercise.tags"
                      :price="exercise.price"
                      @tap="gotoTest(exercise)" />
                  </view>
                </template>
              </LoadMoreList>
            </view>
          </scroll-view>
        </swiper-item>
      </swiper>
    </view>
  </view>
</template>

<script setup lang="ts">
import TopicItem from './TopicItem.vue'
import TopicItemFlex from './TopicItemFlex.vue'
import LoadMoreList from '@/components/LoadMoreList.vue'
import { getSelectedExercise } from '@/api/project/exercises/index'
import type { SelectedExerciseItem } from '@/api/project/exercises/type'
import { useDebounce } from '@/hooks/useDebounce'
import useUserStore from '@/store/modules/user'
import { getSystemImg, showToast } from '@/utils/index'
const props = defineProps({
  currentSubjectModule: {
    type: Object,
    required: true,
  },
  currentSubject: {
    type: String,
    required: true,
  },
  arrange: {
    type: Boolean,
    required: true,
  },
})
const userStore = useUserStore()
// 当前科目
const { currentSubjectModule, currentSubject } = toRefs(props)
// 科目模块
const list = computed(() => {
  return currentSubjectModule.value?.map((item: any) => ({
    name: item.module,
    subject: currentSubject.value,
  }))
})
// 当前模板
const currentTab = ref(0)
// 当前swiper
const swiperCurrent = ref(0)
// 加载更多组件
const loadMoreRefs = ref<any[]>([])

// 监听科目模块变化时重置标签页
watchEffect(() => {
  if (list.value?.length) {
    currentTab.value = 0
    swiperCurrent.value = 0
  }
})
// 模板切换
function change(e: { index: number }) {
  if (e.index === currentTab.value) return
  swiperCurrent.value = e.index
  // 切换标签页时刷新对应模块的列表
  nextTick(() => {
    loadMoreRefs.value[e.index]?.refresh()
  })
}

// 使用防抖函数包装 change
const debouncedChange = useDebounce(change, 100)
// 动画完成
const onAnimationFinish = ({ detail }: { detail: { current: number } }) => {
  swiperCurrent.value = detail.current
  currentTab.value = detail.current
}
// 触底
const onReachBottom = () => {
  loadMoreRefs.value[currentTab.value]?.onReachBottom()
}
function gotoTest(exam: SelectedExerciseItem) {
  if (!userStore.userInfo) {
    showToast('请先登录')
  } else {
    uni.navigateTo({
      url: `/subPages/exercises/answer-sheet?type=test&examId=${exam.topicId}&title=${exam.topicName}&answerType=章节练习`,
    })
  }
}
</script>

<style lang="scss" scoped>
.wrap {
  background-color: #ffffff;
  margin-top: 31rpx;
  border-radius: 20rpx;
  box-shadow: 0rpx 4rpx 16rpx 1rpx rgba(143, 143, 143, 0.13);
  display: flex;
  flex-direction: column;
  height: calc(100vh - (var(--window-top) + env(safe-area-inset-bottom) + 50px));
  width: 100%;
}

.swiper-box {
  flex: 1;
}

.swiper-item {
  height: 100%;
}

:deep .u-tabs__wrapper__nav__line {
  left: 24rpx;
}
</style>
