<template>
  <view>
    <up-overlay :show="showModal">
      <view class="warp">
        <view class="w670rpx hauto rounded-20rpx bg-#ffffff flex flex-col justify-between">
          <view>
            <view class="mt36rpx text-30rpx text-center w-full"> 创建团队 </view>
            <view class="p-40rpx">
              <up-form ref="form1" label-position="left" :model="model1" :rules="rules">
                <up-form-item
                  ref="item1"
                  label-width="150rpx"
                  label=" 团队名称"
                  prop="model1.userInfo.name"
                  required>
                  <up-input v-model="model1.TeamInfo.name"></up-input>
                </up-form-item>
                <!-- <up-form-item ref="item1" label-width="150rpx" label=" 团队头像" label-position="left" prop="userInfo.name">
                  <up-upload name="1" multiple :max-count="10"></up-upload> -->
                <!-- <template>
                    <div class="flex et">
                      <view class="w-150rpx"><span class="text-#FF0000">* </span>团队头像</view>
                      <up-upload name="1" multiple :max-count="10"></up-upload>
                    </div>
                  </template> -->
                <!-- </up-form-item> -->
                <up-form-item
                  ref="item1"
                  label-width="150rpx"
                  label=" 团队描述"
                  prop="model1.TeamInfo.introduce"
                  required>
                  <up-input v-model="model1.TeamInfo.introduce"></up-input>
                </up-form-item>
              </up-form>
            </view>
          </view>
          <view>
            <view class="flex pb-28rpx flex-center">
              <view class="mr-30rpx">
                <up-button
                  text="取消"
                  :custom-style="{ width: '204rpx', height: '74rpx' }"
                  :plain="true"
                  type="primary"
                  :hairline="true"
                  @click="close"></up-button>
              </view>
              <view>
                <up-button
                  text="确定"
                  type="primary"
                  :custom-style="{ width: '204rpx', height: '74rpx' }"
                  @click="open"></up-button>
              </view>
            </view>
          </view>
        </view>
      </view>
    </up-overlay>
  </view>
</template>

<script setup lang="ts" name="">
import { onMounted, ref } from 'vue'
import { useExamStore } from '@/store/modules/useExamStore' // 导入保存当前的encode的 store
import useUserStore from '@/store/modules/user' // 导入当前用户信息的 store
import { addTeamData } from '@/api/project/contest/index'
defineProps({
  showModal: {
    type: Boolean,
    required: true,
  },
})
const emit = defineEmits(['successRegistered', 'close'])

const examStore = useExamStore() // 使用 Pinia store,这是从store中获取当前enCode
// 定义接口来描述 TeamInfo 对象的结构
interface TeamInfo {
  name: string
  introduce: string
}

// 定义接口来描述 model1 对象的结构
interface Model1 {
  TeamInfo: TeamInfo
  captain: string[] // 队长的 ID, 是当前用户的 ID, 在 store 中获取
  enCode: string // 编码
}
const model1 = ref<Model1>({
  TeamInfo: {
    name: '', // 团队名称
    introduce: '', // 团队简介
  },
  captain: [], // 队长的 ID,就是当前用户的ID,在store中获取
  enCode: '', // 编码
})
// Access the user store
const userStore = useUserStore()

// Use onMounted to set the captain ID
onMounted(() => {
  nextTick(() => {
    // Check if userInfo is available and set the captain ID
    if (userStore.userInfo && userStore.userInfo.id) {
      model1.value.captain = [userStore.userInfo.id] // Assign the user ID to captain
    }
    // Optionally, you can also set the enCode if needed
    // console.log('创建团队这个组件中的enCode是', examStore.enCode)
    model1.value.enCode = examStore.enCode
  })
})

const rules = ref({
  'TeamInfo.name': [{ required: true, message: '团队名称不能为空', trigger: 'blur' }],
  'TeamInfo.introduce': [{ required: true, message: '团队描述不能为空', trigger: 'blur' }],
})
function close() {
  emit('close')
}
async function open() {
  // 检查 name 和 introduce 是否为空
  if (!model1.value.TeamInfo.name || !model1.value.TeamInfo.introduce) {
    // 如果为空，显示提示信息
    uni.showToast({
      title: '团队名称和简介不能为空', // 提示信息
      icon: 'none', // 图标类型
    })
    return // 阻止提交
  }
  // 组装请求负载
  const payload = {
    name: model1.value.TeamInfo.name, // 使用 userInfo 中的 name
    captain: model1.value.captain, // 使用队长的 ID
    enCode: model1.value.enCode, // 使用编码
    introduce: model1.value.TeamInfo.introduce, // 使用简介
  }

  try {
    const res = await addTeamData(payload)
    console.log('报名成功', res)
    // 报名成功后，触发 successRegistered 事件
    if (res.code === 200) {
      emit('successRegistered') // 触发父组件的 successRegistered 事件
    }
  } catch (error) {
    uni.showToast({
      title: '报名失败', // 提示信息
      icon: 'none', // 图标类型
    })
    console.error('报名失败:', error)
  }
}
</script>

<style lang="scss" scoped>
.warp {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
}
</style>
