<template>
  <view>
    <!-- 订单列表 -->
    <LoadMoreList
      v-if="status"
      ref="loadMoreList"
      :request-fn="getOrderList"
      :request-params="{ status }">
      <template #default="{ list }">
        <OrderItem v-for="item in orderList" :key="item._id" :list="list" :item="item" />
      </template>
    </LoadMoreList>
  </view>
</template>

<script setup lang="ts" name="order">
import OrderItem from './OrderItem/index.vue'
import { getOrderList } from '@/api/project/member'
import LoadMoreList from '@/components/LoadMoreList.vue'
import type { IOrderData } from '@/api/project/member/type'

const loadMoreList = ref<InstanceType<typeof LoadMoreList>>()

const status = ref('')

const orderList = computed(() => {
  return ((loadMoreList.value?.list as IOrderData[]) || []).filter(item => {
    if (item.pay_status === '下单成功') {
      const twoHoursAgo = Date.now() - 2 * 60 * 60 * 1000
      return item.creatorTime > twoHoursAgo
    }
    return true
  })
})

onReachBottom(() => {
  loadMoreList.value?.onReachBottom()
})

onLoad(val => {
  status.value = val?.status
})
</script>

<style>
page {
  background-color: #f4f5f7;
}
</style>

<style lang="scss" scoped></style>
