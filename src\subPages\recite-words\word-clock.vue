<template>
  <view class="word-clock">
    <!-- 顶部天数 -->
    <u-navbar bg-color="#F8F8F8">
      <template v-if="!query.wordId" #center>
        <view class="navbar-title-container">
          <u-icon
            name="arrow-left"
            size="20"
            :color="canGoPrevDay ? '#666' : '#ccc'"
            @click="goPrevDay">
          </u-icon>
          <view class="navbar-title text-center" @click="showDayPopup"> Day {{ currentDay }} </view>
          <u-icon
            name="arrow-right"
            size="20"
            :color="canGoNextDay ? '#666' : '#ccc'"
            @click="goNextDay">
          </u-icon>
        </view>
      </template>
      <template #left>
        <view class="flex items-center">
          <u-icon name="arrow-left" size="19" @click="back"></u-icon>
          <u-line direction="column" :hairline="false" length="16" margin="0 8px"></u-line>
          <image
            :src="getSystemImg('6747e9ed0a34815816f11159/6691ea130bb07d7cd6ed36e7')"
            class="icon-list"
            @click="goMyWord"></image>
        </view>
      </template>
    </u-navbar>
    <!-- 空容器 -->
    <view class="empty-container" :style="{ height: `calc(${emptyContainerHeight} + 20rpx)` }">
    </view>
    <!-- 导航 -->
    <view
      class="flex items-center word-clock-header"
      :style="{ top: `calc(${emptyContainerHeight} + 20rpx)` }">
      <view class="flex-1 text-center triangle" @click="showTypeSheet = true">
        {{ typeInnerText }}
      </view>
      <view class="flex-1 text-center triangle" @click="showSortSheet = true">
        {{ sortInnerText }}
      </view>
      <view class="flex-1 text-center train" @click="popObj.show = true"> 单词训练 </view>
    </view>
    <!-- 单词列表 -->
    <view
      class="word-content"
      :style="{
        paddingBottom: currentDay === mostNewDay && currentSign === 0 ? '120rpx' : '190rpx',
      }">
      <view v-for="(item, index) in wordList" :key="item._id" class="word-item">
        <SwipeAction
          ref="swipeActionItem"
          :threshold="60"
          :left-actions="getLeftActionOptions(item)"
          :right-actions="getRightActionOptions(item)"
          auto-close
          @click="
            (direction, actionIndex, action) =>
              onActionClick(direction, actionIndex, action, item, index)
          ">
          <WordEntry
            ref="wordEntry"
            :no="index + 1"
            :word-info="item"
            :color="getWordColor(item)"
            :show-enter-left="showEnterLeft"
            :show-enter-right="showEnterRight"
            @hidden-ele-click="hiddenEleClick"
            @word-learned="onWordLearned" />
        </SwipeAction>
      </view>
      <u-skeleton v-if="!wordList.length" rows="30" title loading></u-skeleton>
      <view v-if="wordList.length" class="fixed-bottom">
        <u-button
          v-if="currentDay === mostNewDay && currentSign === 0"
          type="primary"
          shape="circle"
          :text="query.wordId ? '完成练习' : '打卡'"
          :loading="loading"
          :disabled="loading || (query.wordId && finish)"
          @click="onClockIn"></u-button>

        <!-- 打卡完成提示 -->
        <view v-else class="c-[#459AF7] text-34rpx text-center py-40rpx">
          <view>打卡完成</view>
          <view>{{ formatDate(currentReciteWordDetail?.creatorTime, 'YYYY-MM-DD HH:mm') }}</view>
        </view>
      </view>
    </view>
    <!-- 单词训练弹窗 -->
    <u-popup :show="popObj.show" closeable :round="15" mode="center" @close="popObj.show = false">
      <view class="pop-container">
        <view class="pop-title">单词训练</view>
        <view v-for="item in popList" :key="item.name" class="pop-item" @click="onTestClick(item)">
          <image
            :class="item.name === '拍照打卡' ? 'w-100rpx h82rpx' : 'pop-image'"
            :src="item.image"></image>
          <view class="flex items-center">
            <text>{{ item.name }}</text>
            <u-icon name="arrow-right" size="24rpx" color="#B7B7B7"></u-icon>
          </view>
        </view>
      </view>
    </u-popup>
    <!-- 打卡天数弹窗 -->
    <u-popup :show="dayPopup" @close="dayPopup = false">
      <view class="day-pop">
        <view class="day-pop-header">
          <text class="title">{{ query.bookName }}</text>
          <view class="common-text" @click="changePlan">修改 ></view>
        </view>
        <u-line-progress :percentage="percentage" active-color="#459af7"></u-line-progress>
        <view class="line-info">
          <text class="common-text"> {{ popObj.learned }} / {{ popObj.totalCount }} </text>
          <text class="common-text"> 剩余 {{ query.totalDay - mostNewDay }} 天 </text>
        </view>
        <scroll-view scroll-y class="day-content-box">
          <view class="day-content">
            <view
              v-for="(_, index) in Number(query.totalDay || 1)"
              :key="index"
              class="day-item"
              :style="[currentDayStyle(index)]"
              @click="reviewWord(index)">
              <text class="day">Day{{ index + 1 }}</text>
              <text class="status">{{ currentDayText(index) }}</text>
            </view>
          </view>
        </scroll-view>
      </view>
    </u-popup>
    <u-action-sheet
      :actions="typeList"
      safe-area-inset-bottom
      cancel-text="取消"
      :show="showTypeSheet"
      @close="showTypeSheet = false"
      @select="onTypeSelect"></u-action-sheet>
    <u-action-sheet
      :actions="sortList"
      safe-area-inset-bottom
      cancel-text="取消"
      :show="showSortSheet"
      @close="showSortSheet = false"
      @select="onSortChange"></u-action-sheet>
    <u-modal
      :show="showModal"
      title="打卡成功"
      :content="finish ? '恭喜你已经完成了所有的单词！' : '打卡成功，是否继续背单词？'"
      :confirm-text="finish ? '完成' : '继续'"
      cancel-text="返回"
      content-text-align="center"
      show-cancel-button
      @confirm="onModalConfirm"
      @cancel="onModalCancel"></u-modal>
    <PointsModel
      :show="pointsModelShow"
      type="reduce"
      :flag="pointsModelobj.flag"
      :points="pointsModelobj.points"
      :content="pointsModelobj.content"
      @close="pointsModelShow = false" />
  </view>
</template>

<script setup lang="ts" name="word-clock">
/* ------------------------ 导入依赖 ------------------------ */
import WordEntry from './components/WordEntry.vue'
import SwipeAction from '@/components/SwipeAction.vue'
import type { IReciteWordDetail, IReciteWordInfo } from '@/api/project/recite-words/type'
import { formatHours, getSystemImg, showToast, toURLSearchParams } from '@/utils'
import {
  deleteReciteWordStatus,
  getDistributeWordList,
  getLearnedReciteWordCount,
  getReciteWord,
  getReciteWordCount,
  getReciteWordCountByBook,
  getReciteWordCountByDay,
  getReciteWordList,
  getReciteWordStatus,
  insertReciteWord,
  insertReciteWordStatus,
  updateReciteWordDetail,
  updateReciteWordDetailById,
  updateReciteWordStatus,
  updateUserReciteBook,
} from '@/api/project/recite-words'
import useMemberStore from '@/store/modules/member'
import PointsModel from '@/components/PointsModel.vue'
import {
  getDistributeTaskSituationData,
  updateDistributeTaskSituationData,
} from '@/api/project/clock-in'
import type { IDistributeTask } from '@/api/project/clock-in/type'
import useUserStore from '@/store/modules/user'
import useDateFormatter from '@/hooks/useDateFormatter'

/* ------------------------ 基础状态和配置 ------------------------ */
const userStore = useUserStore()
const { formatDate } = useDateFormatter()

// 积分弹窗相关
const pointsModelShow = ref(false)
const pointsModelobj = ref({
  flag: false,
  points: 0,
  content: '',
})

// 页面基础状态
const loading = ref(false)
const finish = ref(false)
const showModal = ref(false)

// 单词列表相关
type IReciteWordInfoWithLearned = IReciteWordInfo & {
  learned: 0 | 1
}
const primitiveList = ref<IReciteWordInfo[]>([])
const wordList = ref<IReciteWordInfoWithLearned[]>([])

// 生词熟词记录状态
const newWordsSet = ref<Set<string>>(new Set()) // 生词ID集合
const familiarWordsSet = ref<Set<string>>(new Set()) // 熟词ID集合

// 用户学习相关
const userDetailId = ref('')
const currentSign = ref(0)

/* ------------------------ 顶部天数相关 ------------------------ */
// 天数状态
const mostNewDay = ref(0) // 最新天
const currentDay = ref(0) // 当前天

// 天数弹窗
const dayPopup = ref(false)

// 弹出层对象
const popObj = reactive({
  show: false,
  learned: 0,
  totalCount: 0,
})

// 进度百分比
const percentage = computed(() => {
  const result = (popObj.learned / popObj.totalCount) * 100
  return parseFloat(result.toFixed(1))
})

/* ------------------------ 单词显示模式相关 ------------------------ */
// 类型值和配置
const type = ref(1)
const typeList = [
  { name: '中英显示', value: -1 },
  { name: '遮罩中文', value: 1 },
  { name: '遮罩英文', value: 0 },
]
const showTypeSheet = ref(false)

// 计算属性
const typeInnerText = computed(() => {
  return typeList.find(item => item.value === type.value)!.name
})

// 显示控制
const showEnterLeft = computed(() => {
  return type.value !== 0
})
const showEnterRight = computed(() => {
  return type.value !== 1
})

// 类型选择处理
function onTypeSelect(e: { value: number }) {
  type.value = e.value
  closeAction()
}

/* ------------------------ 单词排序相关 ------------------------ */
// 排序相关
const sort = ref(-1)
const sortList = [
  { name: '原始排序', value: -1 },
  { name: '随机乱序', value: 0 },
  { name: '字母正序', value: 1 },
]
const showSortSheet = ref(false)

const sortInnerText = computed(() => {
  return sortList.find(item => item.value === sort.value)!.name
})

// 排序切换处理
function onSortChange(e: { value: number }) {
  closeAction()
  uni.pageScrollTo({
    scrollTop: 0,
    duration: 300,
  })
  sort.value = e.value
  resetHiddenEle()
  wordList.value = sortData(wordList.value)
}

// 数据排序逻辑
function sortData(value = primitiveList.value): IReciteWordInfoWithLearned[] {
  const list = JSON.parse(JSON.stringify(value))

  // 从currentReciteWordDetail的tableField102中获取learned状态
  list.forEach((word: IReciteWordInfoWithLearned) => {
    const wordRecord = currentReciteWordDetail.value?.tableField102?.find(
      item => item.word === word._id
    )
    word.learned = wordRecord?.learned || 0
  })

  if (sort.value === 0) {
    return list.sort(() => Math.random() - 0.5)
  } else if (sort.value === 1) {
    return list.sort((a: any, b: any) => a.word.localeCompare(b.word))
  } else {
    return list
  }
}

/* ------------------------ 页面布局相关 ------------------------ */
// 空容器高度
const emptyContainerHeight = computed(() => {
  let top = 0
  // #ifdef MP
  const systemInfo = uni.getSystemInfoSync()
  const menuButtonInfo = uni.getMenuButtonBoundingClientRect()
  top = systemInfo.statusBarHeight! + menuButtonInfo.height
  // #endif
  return `${top}px`
})

/* ------------------------ 单词训练弹窗相关 ------------------------ */
// 弹窗列表
const popList = [
  {
    name: '单词拼写',
    path: '/subPages/recite-words/word-spell',
    image: getSystemImg('6747e9ed0a34815816f11159/6691ecf20bb07d7cd6ed36eb'),
  },
  {
    name: '选择题测试',
    path: '/subPages/recite-words/word-select',
    image: getSystemImg('6747e9ed0a34815816f11159/6691ecf20bb07d7cd6ed36ec'),
  },
  // {
  //   name: '拍照打卡',
  //   path: '/subPages/recite-words/photos-clock-in',
  //   image: getSystemImg('6747e9ed0a34815816f11159/677cd4f0f8730075feadaf0d'),
  // },
]

/* ------------------------ 页面参数和数据 ------------------------ */
type LoadQuery = {
  id: string
  wordNum: number
  bookName: string
  wordId: string
  order: string
  totalDay: number
  userReciteId: string
  taskId: string
  title: string
  book: string
}
const query = ref<LoadQuery>({
  id: '',
  wordNum: 0,
  bookName: '',
  wordId: '',
  order: '',
  totalDay: 0,
  userReciteId: '',
  taskId: '',
  title: '',
  book: '',
})

// 打卡记录相关
const reciteWordCountByBookDatum = ref([] as IReciteWordDetail[])
const currentReciteWordDetailIndex = ref(0)
const currentReciteWordDetail = computed(() => {
  return reciteWordCountByBookDatum.value[currentReciteWordDetailIndex.value]
})

// 学习进度计算
const learnedProgress = computed(() => {
  if (!currentReciteWordDetail.value?.tableField102?.length) return 0
  const learnedCount = currentReciteWordDetail.value.tableField102.filter(
    item => item.learned === 1
  ).length
  return Math.round((learnedCount / currentReciteWordDetail.value.tableField102.length) * 100)
})

/* ------------------------ 天数切换相关 ------------------------ */
// 计算属性：是否可以切换到上一天
const canGoPrevDay = computed(() => {
  return currentReciteWordDetailIndex.value > 0
})

// 计算属性：是否可以切换到下一天
const canGoNextDay = computed(() => {
  return currentReciteWordDetailIndex.value < reciteWordCountByBookDatum.value.length - 1
})

// 切换到上一天
function goPrevDay() {
  if (!canGoPrevDay.value) return

  reviewWord(currentReciteWordDetailIndex.value - 1)
  currentReciteWordDetailIndex.value--
}

// 切换到下一天
function goNextDay() {
  if (!canGoNextDay.value) return
  reviewWord(currentReciteWordDetailIndex.value + 1)
  currentReciteWordDetailIndex.value++
}

/* ------------------------ 单词学习相关 ------------------------ */
// 处理单词学习事件
async function onWordLearned(wordIndex: number) {
  // 只有当前天（正在学习的天）才允许记录学习状态
  if (currentReciteWordDetailIndex.value !== reciteWordCountByBookDatum.value.length - 1) {
    return // 不是当前天，不处理
  }

  const word = wordList.value[wordIndex - 1] // wordIndex是1基索引，需要转换为0基索引
  if (!word || word.learned) return // 如果已经学习过，不重复处理

  // 标记为已学习
  word.learned = 1

  // 更新reciteWordCountByBookDatum的当前天数据
  if (currentReciteWordDetail.value) {
    // 在tableField102中找到对应的单词并标记为已学习
    const wordRecord = currentReciteWordDetail.value.tableField102.find(
      item => item.word === word._id
    )
    if (wordRecord) {
      wordRecord.learned = 1
    }

    // 提交当前天的数据到服务器
    await updateWordLearnedStatus()
  }
}

// 提交学习状态到服务器
async function updateWordLearnedStatus() {
  try {
    if (!currentReciteWordDetail.value) return

    // 调用updateReciteWordDetailById接口提交当前天的学习状态
    await updateReciteWordDetailById(currentReciteWordDetail.value)
    console.log('单词学习状态已提交:', currentReciteWordDetail.value._id)
  } catch (error) {
    console.error('提交学习状态失败:', error)
    showToast('提交学习状态失败')
  }
}

// 处理隐藏元素点击事件
function hiddenEleClick(type: string) {
  console.log('隐藏元素点击:', type)
}

/**
 * 获取单词数据
 * @param {boolean} isInsert 是否需要插入新的单词表
 * @param {string} userDetailId 单词学习详细表Id
 */
function getWord(isInsert: boolean, userDetailId: string) {
  if (isInsert) {
    // 随机抽取单词
    return getReciteWord(query.value.id, query.value.wordNum)
  } else {
    // 获取用户背诵的单词
    return getReciteWordList(userDetailId)
  }
}

// 插入学习详情
function insertDetail() {
  const tableField102 = wordList.value.map(o => ({
    word: o._id,
  }))
  // 插入背诵单词
  return insertReciteWord({
    book: query.value.id,
    tableField102,
    sign: 0,
    days: currentDay.value,
  })
}

// 更新用户背诵的书籍
function changeUserReciteBook() {
  return updateUserReciteBook({
    id: query.value.userReciteId,
    bookName: query.value.bookName,
    bookId: query.value.id,
  })
}

/* ------------------------ 生命周期钩子 ------------------------ */
onUnload(() => {
  // 取消自动跳转
  uni.$emit('autoJump', false)
})

onLoad(async (e: AnyObject | undefined) => {
  // 更新用户背诵的书籍
  query.value = {
    ...e,
    bookName: decodeURIComponent(e?.bookName || ''),
    order: decodeURIComponent(e?.order || ''),
  } as LoadQuery

  // 单词打卡任务分配
  if (query.value.wordId) {
    getClockWordList()
    return
  }
  changeUserReciteBook()
  getPopupInfo()
  sort.value = sortList.find(item => item.name === query.value.order)!.value

  // 加载生词熟词记录
  await loadWordStatus(query.value.id)

  // 获取用户所有打卡记录
  reciteWordCountByBookDatum.value = (await getReciteWordCountByBook(query.value.id)).data.list
  // 默认选中最后一个（最新天）
  currentReciteWordDetailIndex.value = reciteWordCountByBookDatum.value.length - 1
  const currentData = reciteWordCountByBookDatum.value[currentReciteWordDetailIndex.value] || {}
  currentSign.value = currentData?.sign || 0
  const day = currentData?.days || 0
  finish.value = day === Number(query.value.totalDay)

  const isInsert = currentData?.sign !== 0 && !finish.value

  currentDay.value = isInsert ? day + 1 : day
  mostNewDay.value = currentDay.value

  const resultWord = await getWord(isInsert, currentData._id)

  primitiveList.value = resultWord.data
  wordList.value = sortData()
  if (isInsert) {
    const result = await insertDetail()
    currentSign.value = 0
    userDetailId.value = result.data
    const res = await useMemberStore().pointsAction('单词1个', wordList.value.length, result.data)
    if (res?.flag) {
      pointsModelobj.value.flag = res!.flag
    } else {
      pointsModelobj.value = res as any
    }
    pointsModelShow.value = true
  } else {
    userDetailId.value = currentData._id
  }
})

/* ------------------------ 天数弹窗相关 ------------------------ */
// 显示天数弹窗
function showDayPopup() {
  dayPopup.value = true
  getPopupInfo()
}

// 修改学习计划
function changePlan() {
  const query1 = {
    id: query.value.id,
    title: query.value.bookName,
    learned: popObj.learned,
    learnedDay: currentDay.value,
  }
  uni.navigateTo({
    url: `/subPages/recite-words/plan?${toURLSearchParams(query1)}`,
  })
}

/* ------------------------ 打卡相关 ------------------------ */
async function getClockWordList() {
  const result = await getDistributeWordList(query.value.wordId)
  query.value.id = query.value.book
  userDetailId.value = query.value.wordId
  primitiveList.value = result.data
  wordList.value = sortData()
}

async function onClockIn() {
  if (query.value.wordId) {
    await submitDistribute()
  } else {
    await submitSign()
  }
}

// 完成派发单词打卡任务
async function submitDistribute() {
  getDistributeTaskSituationData(query.value.taskId).then(res => {
    if (res.data.list.length === 0) {
      onTestClick(popList[1])
    } else {
      showToast('已打卡')
    }
  })
}

// 完成学习计划打卡任务
async function submitSign() {
  loading.value = true
  const result = await updateReciteWordDetail({
    id: userDetailId.value,
    sign: 1,
  })
  currentSign.value = 1
  showModal.value = result.code === 200
}

/* ------------------------ 生词熟词操作相关 ------------------------ */
async function onActionClick(
  direction: 'left' | 'right',
  index: number,
  action: any,
  item: IReciteWordInfoWithLearned,
  actionIndex: number
) {
  const { type, wordbookId, _id, typeId } = item

  // 处理左侧滑动操作
  if (direction === 'left') {
    if (index === 0) {
      // 删除操作
      uni.showModal({
        title: '确认删除',
        content: `确定要删除单词"${item.word}"吗？`,
        success: res => {
          if (res.confirm) {
            // 这里可以添加删除逻辑
            uni.$u.toast('删除功能待实现')
          }
        },
      })
    }
    closeAction(actionIndex)
    return
  }

  // 处理右侧滑动操作（原有逻辑）
  const setType = index === 0 ? '熟词' : '生词'
  if (!typeId) {
    const result = await insertReciteWordStatus({
      book: wordbookId,
      wordId: _id,
      type: setType,
    })
    uni.$u.toast(`添加${setType}成功`)
    item.typeId = result.data
    item.type = setType
    // 更新状态集合
    if (setType === '生词') {
      newWordsSet.value.add(_id)
      familiarWordsSet.value.delete(_id)
    } else if (setType === '熟词') {
      familiarWordsSet.value.add(_id)
      newWordsSet.value.delete(_id)
    }
  } else if (type === setType) {
    // 取消
    await deleteReciteWordStatus(typeId)
    uni.$u.toast(`取消${setType}成功`)
    delete item.type
    delete item.typeId
    // 从状态集合中删除
    newWordsSet.value.delete(_id)
    familiarWordsSet.value.delete(_id)
  } else {
    // 更新
    await updateReciteWordStatus({
      id: typeId,
      type: setType,
    })
    item.type = setType
    uni.$u.toast(`添加${setType}成功`)
    // 更新状态集合
    if (setType === '生词') {
      newWordsSet.value.add(_id)
      familiarWordsSet.value.delete(_id)
    } else if (setType === '熟词') {
      familiarWordsSet.value.add(_id)
      newWordsSet.value.delete(_id)
    }
  }

  closeAction(actionIndex)
}

// 滑动操作相关
const swipeActionItem = ref()
function closeAction(index?: number) {
  if (index !== undefined && swipeActionItem.value?.[index]) {
    swipeActionItem.value[index].close()
    return
  }
  for (const item of swipeActionItem.value || []) {
    item.close()
  }
}

// 单词条目相关
const wordEntry = ref()
function resetHiddenEle() {
  for (const entry of wordEntry.value || []) {
    entry.resetHiddenEle()
  }
}

// 左侧滑动操作选项
function getLeftActionOptions({ type }: { type?: string }) {
  return [
    {
      text: '生词',
      icon: type === '生词' ? 'star-fill' : 'star',
      style: {
        backgroundColor: '#FCB138',
        fontSize: '28rpx',
        color: 'white',
      },
    },
  ]
}

// 右侧滑动操作选项
function getRightActionOptions({ type }: { type?: string }) {
  return [
    {
      text: '熟词',
      icon: type === '熟词' ? 'checkmark-circle-fill' : 'checkmark-circle',
      style: {
        backgroundColor: '#459AF7',
        fontSize: '28rpx',
        color: 'white',
      },
    },
  ]
}

// 保持原有函数以兼容其他可能的调用
function getActionOptions({ type }: { type?: string }) {
  return getRightActionOptions({ type })
}

/* ------------------------ 生词熟词状态管理 ------------------------ */
// 加载生词熟词记录
async function loadWordStatus(bookId: string) {
  try {
    const result = await getReciteWordStatus(bookId)
    const newWords = new Set<string>()
    const familiarWords = new Set<string>()

    result.data.list.forEach(item => {
      if (item.type === '生词') {
        newWords.add(item.wordId)
      } else if (item.type === '熟词') {
        familiarWords.add(item.wordId)
      }
    })

    newWordsSet.value = newWords
    familiarWordsSet.value = familiarWords
  } catch (error) {
    console.error('加载生词熟词记录失败:', error)
  }
}

// 根据单词状态获取颜色
function getWordColor(item: IReciteWordInfoWithLearned) {
  if (newWordsSet.value.has(item._id)) {
    return 'rgba(252, 177, 56, .4)' // 收藏/生词颜色
  } else if (familiarWordsSet.value.has(item._id)) {
    return 'rgba(69, 154, 247, .3)' // 熟知/熟词颜色
  }
  return undefined // 默认颜色（未定义状态）
}

/* ------------------------ 天数复习相关 ------------------------ */
async function reviewWord(index: number) {
  if (index >= mostNewDay.value) {
    return uni.$u.toast('还未开始学习')
  }

  closeAction()
  if (index !== currentDay.value - 1) {
    resetHiddenEle()
    currentDay.value = index + 1
    const result = await getReciteWordCountByDay(query.value.id, index + 1)
    const currentData = result.data.list?.[0] || {}
    userDetailId.value = currentData._id
    const resultWord = await getWord(false, currentData._id)
    primitiveList.value = resultWord.data
    wordList.value = sortData()
  }
  dayPopup.value = false
}

// 天数样式
function currentDayStyle(index: number) {
  const style = {
    color: '#333',
    backgroundColor: 'rgba(69, 154, 247, 0.10)',
  }
  if (index < mostNewDay.value - 1 || finish.value) {
    style.color = '#fff'
    style.backgroundColor = '#459AF7'
  } else if (index === mostNewDay.value - 1) {
    style.color = '#fff'
    style.backgroundColor = 'rgba(69, 154, 247, 0.50)'
  }
  return style
}

/* ------------------------ 页面跳转相关 ------------------------ */
function goMyWord() {
  uni.$u.route({
    url: '/subPages/recite-words/word-my',
    params: {
      id: query.value.id,
    },
  })
}

// 返回
function back() {
  uni.reLaunch({
    url: '/subPages/recite-words/recite-words',
  })
}

function onTestClick(item: (typeof popList)[0]) {
  if (!item.path) {
    showToast('敬请期待')
    return
  }
  uni.navigateTo({
    url:
      item.path +
      uni.$u.queryParams({
        ...query.value,
        userDetailId: userDetailId.value,
      }),
    success: res => {
      res.eventChannel.emit('emitWordList', wordList.value)
      res.eventChannel.emit('emitRecord', {
        bookId: query.value.id,
        type: item.name,
      })
    },
  })
}

/* ------------------------ 弹窗处理相关 ------------------------ */
function currentDayText(index: number) {
  if (index < mostNewDay.value - 1 || finish.value) {
    return '已完成'
  } else if (index === mostNewDay.value - 1) {
    return `${learnedProgress.value}%`
  } else {
    return '未开始'
  }
}

async function onModalConfirm() {
  closeAction()
  showModal.value = false
  loading.value = false
  if (finish.value) return // 如果已经完成，则不进行任何操作

  currentDay.value++
  mostNewDay.value++
  wordList.value = []
  const resultWord = await getWord(true, query.value.id)
  finish.value = mostNewDay.value === Number(query.value.totalDay) // 判断是否完成
  currentSign.value = 0

  primitiveList.value = resultWord.data
  wordList.value = sortData()
  const result = await insertDetail()
  const res = await useMemberStore().pointsAction('单词1个', wordList.value.length, result.data)
  if (res?.flag) {
    pointsModelobj.value.flag = res!.flag
  } else {
    pointsModelobj.value = res as any
  }
  pointsModelShow.value = true
  resetHiddenEle()
  uni.pageScrollTo({
    scrollTop: 0,
    duration: 300,
  })
  userDetailId.value = result.data
}

function onModalCancel() {
  showModal.value = false
  loading.value = false
  back()
}

/* ------------------------ 弹窗信息获取 ------------------------ */
// 获取弹窗信息
async function getPopupInfo() {
  try {
    const countResult = await getReciteWordCount(query.value.id)
    const totalCount = countResult.data?.[0]?.totalCount || 0
    popObj.totalCount = totalCount

    const learnedResult = await getLearnedReciteWordCount(query.value.id)
    const learnedCount = learnedResult.data?.[0]?.totalCount || 0
    popObj.learned = learnedCount
  } catch (error) {
    console.error('获取弹窗信息失败:', error)
  }
}
</script>

<style lang="scss" scoped>
.word-clock {
  .word-clock-header {
    position: sticky;
    background-color: $uni-bg-color;
    padding: 20rpx;
    z-index: 100;
    .train {
      color: #459af7;
    }
  }
  .triangle {
    position: relative;
    &::after {
      content: '';
      position: absolute;
      right: 25rpx;
      top: 45%;
      transform: translateX(-50%);
      width: 0;
      height: 0;
      border-left: 10rpx solid transparent;
      border-right: 10rpx solid transparent;
      border-top: 15rpx solid #b7b7b7;
      border-radius: 5rpx;
    }
  }
  .word-content {
    background-color: $uni-bg-color;
    padding: 10rpx 20rpx 0;
    .word-item {
      margin-bottom: 20rpx;
    }

    .clock-btn {
      margin: 0 auto;
    }
    .fixed-bottom {
      position: fixed;
      bottom: 0;
      left: 0;
      right: 0;
      padding: 10rpx 30rpx; // 可根据需要调整内边距
      background-color: white; // 可根据需要调整背景颜色
      z-index: 1000; // 确保按钮在其他元素之上
      padding-bottom: env(safe-area-inset-bottom); // 添加安全区域的底部填充
    }
  }
  .pop-container {
    width: 500rpx;
    padding: 30rpx;
    .pop-title {
      font-size: 32rpx;
      text-align: center;
      color: #333;
    }
    .pop-item {
      display: flex;
      align-items: center;
      gap: 48rpx;
      margin-top: 30rpx;
      border-radius: 14px 14px 14px 14px;
      padding: 27rpx 68rpx;
      border: 1px solid #d4d4d4;
      &:first-child {
        margin-bottom: 30rpx;
      }
      .pop-image {
        height: 100rpx;
        width: 100rpx;
        object-fit: cover;
      }
      text {
        font-size: 30rpx;
        color: #333;
        font-weight: bold;
        margin-right: 6rpx;
      }
    }
  }

  .navbar-title-container {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 16rpx;
  }

  .navbar-title {
    font-size: 36rpx;
    color: #333333;
    position: relative;
    min-width: 120rpx;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
    &::after {
      content: '';
      border-left: 10rpx solid transparent;
      border-right: 10rpx solid transparent;
      border-top: 15rpx solid #000;
      border-radius: 5rpx;
      margin: 5rpx;
    }
  }

  .icon-list {
    width: 35rpx;
    height: 35rpx;
  }
  .day-pop {
    background-color: $uni-bg-color;
    padding: 40rpx;
    .day-pop-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 15rpx;

      .title {
        font-size: 30rpx;
        color: #333;
      }
    }
    .line-info {
      margin-top: 15rpx;
      display: flex;
      justify-content: space-between;
    }
  }
  .common-text {
    color: #a7a7a7;
    font-size: 24rpx;
  }
  .day-content-box {
    max-height: 60vh;
  }
  .day-content {
    margin-top: 30rpx;
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    grid-gap: 20rpx;
    font-size: 28rpx;
    .day-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 15rpx 30rpx;
      border-radius: 50rpx;
    }
  }
}
</style>
