<template>
  <view class="w-full bg-white relative z-9999 pb10rpx overflow-hidden">
    <SearchInput placeholder="请输入招生院校" @on-search="onSearch" />
  </view>
  <FoldPage
    :tags="tags"
    :current-tag="currentTag"
    :center-tag="academy_type"
    :left-tag="school_region"
    :right-tag="school_type"
    @click-tags="clickTags"
    @click-tag="clickTag" />
  <LoadMoreList ref="loadMoreList" :request-fn="getBKSchoolDataList" :request-params="{ filter }">
    <template #default="{ list }">
      <SchoolItem
        v-for="item in (list as IBKSchoolData[])"
        :key="item._id"
        :item-data="item"
        :flag="false"
        @tap="toDetail(item._id)" />
    </template>
  </LoadMoreList>
  <FloatWeixin />
</template>

<script setup lang="ts" name="school-list">
import SchoolItem from './SchoolItem/index.vue'
import FoldPage from './FoldPage/index.vue'
import SearchInput from '@/subPages/index/components/SearchInput/index.vue'
import FloatWeixin from '@/components/FloatWeixin.vue'
import LoadMoreList from '@/components/LoadMoreList.vue'
import type {
  IBKSchoolAddressData,
  IBKSchoolData,
  IBKSchoolTypeData,
} from '@/api/project/index/type'
import {
  getBKSchoolAddressDataList,
  getBKSchoolDataList,
  getBKSchoolTypeDataList,
} from '@/api/project/index'
import type { FilterType } from '@/es/request'

const loadMoreList = ref<InstanceType<typeof LoadMoreList>>()

onReachBottom(() => {
  loadMoreList.value!.onReachBottom()
})

const getData = () => {
  loadMoreList.value?.refresh()
}

const school_region = ref('')
const academy_type = ref('')
const school_type = ref('')
const filter = ref<FilterType[]>([
  {
    enCode: 'school_region',
    method: 'eq',
    type: 'custom',
    value: [school_region.value],
  },
  {
    enCode: 'academy_type',
    method: 'eq',
    type: 'custom',
    value: [academy_type.value],
  },
  {
    enCode: 'school_type',
    method: 'eq',
    type: 'custom',
    value: [school_type.value],
  },
  {
    enCode: 'school_name',
    method: 'like',
    type: 'custom',
    value: [''],
  },
])

const schoolReginList = ref<IBKSchoolAddressData[]>([])
const schoolTypeList = ref<IBKSchoolTypeData[]>([])
const currentTag = ref('')
const showPopup = ref(false)
const tags = ref([
  {
    name: '位置',
    key: 'school_region',
    tags: [],
  },
  {
    name: '办学类型',
    key: 'school_type',
    tags: ['公办', '民办'],
  },
  {
    name: '院校类型',
    key: 'academy_type',
    tags: [],
  },
])
const clickTags = (key: string) => {
  if (currentTag.value === key) {
    currentTag.value = ''
    showPopup.value = false
    return 0
  }
  showPopup.value = true
  currentTag.value = key
}

const clickTag = (key: string, val: string) => {
  switch (key) {
    case 'school_region':
      if (val === school_region.value) {
        school_region.value = ''
        filter.value[0].value[0] = ''
        break
      }
      school_region.value = val
      filter.value[0].value[0] = val
      break
    case 'school_type':
      if (val === school_type.value) {
        school_type.value = ''
        filter.value[2].value[0] = ''
        break
      }
      school_type.value = val
      filter.value[2].value[0] = val
      break
    case 'academy_type':
      if (val === academy_type.value) {
        academy_type.value = ''
        filter.value[1].value[0] = ''
        break
      }
      academy_type.value = val
      filter.value[1].value[0] = val
      break
  }
  getData()
}

// 院校搜索
const searchValue = ref('')
const onSearch = (val: string) => {
  filter.value[3].value[0] = val
  getData()
}

const toDetail = (id: string) => {
  uni.navigateTo({
    url: `/subPages/index/school/detail?id=${id}`,
  })
}
onLoad(async () => {
  schoolReginList.value = (await getBKSchoolAddressDataList({})).data.list
  schoolTypeList.value = (await getBKSchoolTypeDataList({})).data.list
  tags.value[0].tags = schoolReginList.value.map(item => item.school_address)
  tags.value[2].tags = schoolTypeList.value.map(item => item.school_type)
})
</script>

<style>
page {
  background-color: #f3f4f6;
}
</style>

<style lang="scss" scoped></style>
