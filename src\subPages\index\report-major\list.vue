<template>
  <SearchInput
    :value="searchValue"
    placeholder="请输入学校或者专业"
    bg="#FFFFFF"
    @on-change="onChange" />
  <up-tabs
    :list="tabs"
    :item-style="{ width: '350rpx', height: '100rpx' }"
    :line-width="40"
    :line-height="6"
    :current="tabIndex"
    @change="tabChange"></up-tabs>
  <swiper :style="`height:${swiperHeight}px`" :current="tabIndex" @change="swiperChange">
    <swiper-item item-id="111" class="overflow-auto">
      <view v-for="(item, index) in majorlist" :key="`${index}1`">
        <Item
          :title="item.title"
          :is-rotate="clickIndex === index && majorShow"
          @tap="toOpen(index, item.title, 'major')" />
        <!-- 折叠内容区域 - 专业 -->
        <view
          :class="{ collapsed: clickIndex === index && majorShow }"
          :style="{
            maxHeight: clickIndex === index && majorShow ? '1000rpx' : '0',
            overflow: 'hidden',
            transition: 'max-height 0.3s ease-in-out',
          }">
          <view class="m-32rpx pt0 relative">
            <view v-if="!isLogin" class="blur flex items-center justify-center">
              <view class="text-#333 -rotate-45">登录后查看</view>
            </view>
            <MajorTable
              v-if="clickIndex === index && majorShow"
              type="major"
              :titles="[
                '学校名称',
                '考试科目',
                `${yearData?.plan}年<br/>招生计划`,
                `${yearData?.count}年<br/>报考人数`,
                `${yearData?.score}年<br/>分数线`,
              ]"
              :list="majorlist[clickIndex]?.list"
              @to-jump="toSchool" />
          </view>
        </view>
      </view>
    </swiper-item>
    <swiper-item item-id="222" class="overflow-auto">
      <view v-for="(item, index) in schoollist" :key="index">
        <Item
          :title="item.title"
          :is-rotate="clickIndex === index && schoolShow"
          @tap="toOpen(index, item.title, 'school')" />
        <!-- 折叠内容区域 - 学校 -->
        <view
          :class="{ collapsed: clickIndex === index && schoolShow }"
          :style="{
            maxHeight: clickIndex === index && schoolShow ? '1000rpx' : '0',
            overflow: 'hidden',
            transition: 'max-height 0.3s ease-in-out',
          }">
          <view class="m-32rpx pt0 relative">
            <view v-if="!isLogin" class="blur flex items-center justify-center">
              <view class="text-#333 -rotate-45">登录后查看</view>
            </view>
            <MajorTable
              v-if="clickIndex === index && schoolShow"
              type="major"
              :titles="[
                '专业名称',
                '考试科目',
                `${yearData?.plan}年<br/>招生计划`,
                `${yearData?.count}年<br/>报考人数`,
                `${yearData?.score}年<br/>分数线`,
              ]"
              :list="schoollist[clickIndex]?.list"
              @to-jump="toMajor" />
          </view>
        </view>
      </view>
    </swiper-item>
  </swiper>
  <view class="h30rpx w-full"> </view>
  <FloatWeixin />
</template>

<script setup lang="ts" name="report-major-list">
import MajorTable from './MajorTable/index.vue'
import { getBKSchoolDataList, getReportMajorDataList, getSbYearData } from '@/api/project/index'
import Item from '@/subPages/index/search/Item/index.vue'
import FloatWeixin from '@/components/FloatWeixin.vue'
import SearchInput from '@/subPages/index/components/SearchInput/index.vue'
import type { IBKSchoolMajorData, ISbYearData } from '@/api/project/index/type'
import useIndexStore from '@/store/modules/index'

const searchValue = ref('')
const onChange = (val: string) => {
  searchValue.value = val
}

const currentYear = ref(2025)

// 年份相关配置
const yearData = ref<ISbYearData>()
const yearDataCopy = ref<ISbYearData>()

// 总本科专业数据
const majorbkList = ref<IBKSchoolMajorData[]>([])

const now_title = ref('')
// 按专业选择
const majorTitles = ref<string[]>([])
const majorShow = ref(false)
const majorlist = computed<{ title: string; list: string[][] }[]>(() => {
  const list: { title: string; list: string[][] }[] = []
  majorTitles.value.forEach(item => {
    list.push({
      title: item,
      list: majorbkList.value
        .filter(
          ite =>
            ite.Undergraduate_name === item &&
            yearData.value?.title === ite.sum_year &&
            (searchValue.value === '' || ite.Undergraduate_name === searchValue.value)
        )
        .map(i => {
          return [
            i.school_name,
            i.other_info.split('\n')[0],
            `${i.enrollment_plan}`,
            i.application_nums,
            i.score,
          ]
        }),
    })
  })

  return searchValue.value ? list.filter(item => item.title.includes(searchValue.value)) : list
})
const toMajor = (val: string) => {
  const data = majorbkList.value.filter(item => item.Undergraduate_name === val)[0]
  uni.navigateTo({
    url: `/subPages/index/major/detail?name=${data.Undergraduate_name}&code=${data.Undergraduate_code}`,
  })
}

const toSchool = async (val: string) => {
  const data = (
    await getBKSchoolDataList({
      filter: [
        {
          enCode: 'school_name',
          type: 'custom',
          method: 'eq',
          value: [val],
        },
      ],
    })
  ).data.list.filter(item => item.school_name === val)[0]
  uni.navigateTo({
    url: `/subPages/index/school/detail?id=${data._id}`,
  })
}

// 按学校选择
const schoolTitles = ref<string[]>([])
const schoolShow = ref(false)
const schoollist = computed<
  {
    title: string
    list: string[][]
  }[]
>(() => {
  const list: {
    title: string
    list: string[][]
  }[] = []
  schoolTitles.value.forEach(item => {
    list.push({
      title: item,
      list: majorbkList.value
        .filter(ite => ite.school_name === item && yearData.value?.title === ite.sum_year)
        .map(i => {
          return [
            i.Undergraduate_name,
            i.other_info.split('\n')[0],
            `${i.enrollment_plan}`,
            i.application_nums,
            i.score,
          ]
        }),
    })
  })
  return list.filter(item => item.title.includes(searchValue.value))
})

const clickIndex = ref(-1)
const toOpen = (index: number, title: string, type: string) => {
  // 如果点击的是同一个项目，则收起
  if (index === clickIndex.value) {
    clickIndex.value = -1
    if (type === 'major') {
      majorShow.value = false
    } else {
      schoolShow.value = false
    }
    return
  }

  // 展开新的项目
  now_title.value = title
  clickIndex.value = index

  if (type === 'major') {
    majorShow.value = true
    schoolShow.value = false // 确保另一个tab的状态被重置
  } else {
    schoolShow.value = true
    majorShow.value = false // 确保另一个tab的状态被重置
  }
}

// tabs导航
const tabs = ref([
  {
    id: 1,
    name: '按专业选择',
  },
  {
    id: 2,
    name: '按学校选择',
  },
])
// 当前tab
const tabIndex = ref(0)
const tabChange = (val: any) => {
  tabIndex.value = val.index
  clickIndex.value = -1
  majorShow.value = false
  schoolShow.value = false
  searchValue.value = ''
}
const swiperChange = (val: any) => {
  tabIndex.value = val.detail.current
}
const swiperHeight = ref(800)

onMounted(async () => {
  const systemInfo = uni.getSystemInfoSync()
  const screenHeight = systemInfo.screenHeight
  swiperHeight.value = screenHeight - 200
})

onLoad(async (val: any) => {
  const { data } = await getReportMajorDataList(val.code)
  const list = (data[0] as any).school_list

  yearData.value = (await getSbYearData()).data.list[0]
  yearDataCopy.value = yearData.value

  // 处理专业数据
  const processedList: any[] = []
  const majorMap = new Map()

  // 首先处理2024年的数据
  list.forEach((item: any) => {
    if (item.sum_year === '2024') {
      const key = `${item.school_name}-${item.Undergraduate_name}`
      majorMap.set(key, item)
    }
  })

  // 处理所有年份的数据
  list.forEach((item: any) => {
    if (item.sum_year === '2025') {
      const key = `${item.school_name}-${item.Undergraduate_name}`
      processedList.push(item) // 保存2025年的数据

      if (!majorMap.has(key)) {
        // 2025年招生但2024年不招的，添加一条2024年的空数据
        processedList.push({
          ...item,
          sum_year: '2024',
          enrollment_plan: '-',
          application_nums: '-',
          score: '-',
          admission_nums: '-',
          acceptance_rate: '-',
        })
      } else {
        // 2025年招生且2024年也招生的，保留2024年的数据
        processedList.push(majorMap.get(key))
      }
    }
  })

  // 更新专业列表
  majorbkList.value = processedList

  // 更新学校和专业标题列表
  schoolTitles.value = [...new Set(majorbkList.value.map(item => item.school_name))]
  majorTitles.value = [...new Set(majorbkList.value.map(item => item.Undergraduate_name))]
  currentYear.value = await useIndexStore().getCurrentYear()
})
import useUserStore from '@/store/modules/user'

const isLogin = computed(() => {
  return useUserStore().userInfo
})
</script>

<style lang="scss">
page {
  background-color: #f4f5f7;
}

.blur {
  position: absolute;
  width: 100%;
  height: 100%;
  background: rgba($color: #fff, $alpha: 0.5);
  z-index: 10;
  backdrop-filter: blur(8rpx);
}
</style>
