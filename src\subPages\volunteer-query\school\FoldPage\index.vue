<template>
  <view class="w-full bg-white p1rpx relative z-99 overflow-hidden">
    <view class="mt30rpx flex justify-around">
      <view
        v-for="item in tags"
        :key="item.key"
        class="flex items-center text-30rpx text-#333333 fw-500"
        :style="currentTag === item.key ? 'color: #459af7' : ''"
        @click="clickTags(item.key)">
        {{ item.name }}
        <view
          class="ml10rpx"
          style="transition: transform 0.5s"
          :style="`transform: rotate(${currentTag === item.key ? 180 : 0}deg);`">
          <u-icon
            name="arrow-up-fill"
            size="15"
            :color="currentTag === item.key ? '#459af7' : ''"></u-icon>
        </view>
      </view>
    </view>
    <view class="w-full h20rpx"></view>
  </view>
  <view
    v-for="(item, index) in tags"
    :key="index"
    class="absolute w-full max-h-260rpx overflow-auto bg-white top-220rpx left-0rpx flex flex-wrap z-90"
    style="transition: transform 0.5s"
    :style="`transform: translateY(${currentTag !== item.key ? '-100%' : '1%'});`">
    <view
      v-for="it in item.tags"
      :key="it"
      class="w160rpx h63rpx ml20rpx m-y-12rpx rounded-6rpx bg-white b-2rpx b-solid b-#D9D9D9 text-#666666 text-28rpx text-center lh-63rpx"
      :class="{ wid: item.key === 'school_name' }"
      :style="
        it === leftTag || it === centerTag || it === rightTag
          ? 'background-color: #e1e9ff; border: 2rpx solid #459af7'
          : ''
      "
      @click="clickTag(item.key, it)">
      {{ it }}
    </view>
  </view>
  <up-overlay :show="currentTag !== ''" z-index="10" @click="clickTags('')"></up-overlay>
</template>

<script setup lang="ts" name="fold-page">
defineProps<{
  tags: {
    name: string
    key: string
    tags: string[]
  }[]
  currentTag: string
  leftTag: string
  centerTag: string
  rightTag: string
}>()

const emits = defineEmits(['clickTags', 'clickTag'])
const clickTags = (key: string) => {
  emits('clickTags', key)
}
const clickTag = (key: string, val: string) => {
  emits('clickTag', key, val)
}
</script>

<style lang="scss" scoped>
.wid {
  width: 340rpx;
}
</style>
