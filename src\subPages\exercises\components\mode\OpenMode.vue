<template>
  <view>
    <!-- header -->
    <view class="relative">
      <InfoBar :cur-index="curIndex" :topic-list-length="topicList.length"></InfoBar>
      <view class="pb120rpx">
        <SheetItem
          :detail="topicList[curIndex]"
          :collect="false"
          :index="curIndex + 1"
          @check-option="getUserResult"></SheetItem>
        <!-- 答案 -->
        <view v-if="showAnalysis" class="topic-result flex items-center flex-wrap mt20rpx">
          <mp-html
            class="correct h-fit margin-r-24"
            :preview-img="false"
            :content="`正确答案：${arrayToString(curTopic.answer)}`" />
        </view>
        <!-- 解析 -->
        <view v-if="showAnalysis">
          <view class="analysis-wrap mt20rpx">
            <view class="analysis-head"> 解析 </view>
            <view class="analysis-main margin-t-20">
              <view class="analysis-cont margin-t-20">
                <view class="hide-warp after">
                  <mpHtml
                    class="context u-main-color"
                    :content="replaceSpanWithLatex(curTopic.analysis)"></mpHtml>
                </view>
              </view>
            </view>
          </view>
        </view>
        <!-- 考点 -->
        <view v-if="showAnalysis" class="analysis-wrap mt20rpx">
          <view class="analysis-head"> 考点 </view>
          <view class="analysis-main margin-t-20">
            <mpHtml
              class="context u-main-color"
              :content="arrayToString(curTopic.knowledgePoints)"></mpHtml>
          </view>
        </view>
      </view>
    </view>
    <!-- tabbar -->
    <u-tabbar :placeholder="false" inactive-color="#459AF7">
      <u-tabbar-item text="上一题" @tap="pickTest(0)" icon="play-left"> </u-tabbar-item>
      <u-tabbar-item text="解析" @tap="showAnalysis = !showAnalysis">
        <template #inactive-icon>
          <image
            class="tabbar-icon"
            :src="getSystemImg('6747e9ed0a34815816f11159/669bbde90bb07d7cd6ed3bb2')">
          </image>
        </template>
      </u-tabbar-item>
      <u-tabbar-item text="题卡" @tap="showPopup = true">
        <template #inactive-icon>
          <image
            class="tabbar-icon"
            :src="getSystemImg('6747e9ed0a34815816f11159/669bb8940bb07d7cd6ed3bae')">
          </image>
        </template>
      </u-tabbar-item>
      <u-tabbar-item text="下一题" @tap="pickTest(1)" icon="play-right"> </u-tabbar-item>
    </u-tabbar>
    <!-- 弹窗 -->
    <u-popup :show="showPopup" :round="10" mode="bottom" closeable @close="showPopup = false">
      <view class="answer-card">答题卡</view>
      <view class="half">
        <u-grid :border="false" col="5">
          <view
            v-for="(topic, i) in topicList"
            :key="i"
            class="topic-item"
            :class="topic?.state"
            @tap="pickerByPopup(i)">
            {{ i + 1 }}
          </view>
        </u-grid>
      </view>
    </u-popup>
  </view>
</template>
<script setup lang="ts" name="report-mode">
// 题目选项
import SheetItem from '../SheetItem.vue'
import InfoBar from '../InfoBar.vue'
// 数学公式mphtml 开启扩展latex
import mpHtml from '../mp-html/mp-html.vue'
import { getSystemImg } from '@/utils'
// 工具类
import { replaceSpanWithLatex } from '@/utils/index'
// 加载页面
const props = defineProps({
  topicList: {
    type: Array,
    default: [],
  } as any,
})
// 类型
import type { TopicItem } from '@/api/project/exercises/type'
// 当前题目
const curTopic = ref({} as TopicItem)

// 当前下标
const curIndex = ref(0)
// 工具类
import { showToast } from '@/utils'
// 题目类型
const QuestionTypes = {
  SINGLE_CHOICE: '单选题',
  MULTIPLE_CHOICE: '多选题',
  READING_COMPREHENSION: '阅读理解',
  TRUE_FALSE: '判断题',
}
// 答题卡
const showPopup = ref(false)

const showAnalysis = ref(false)
onMounted(() => {
  curTopic.value = props.topicList[curIndex.value]
})
// 接收子组件答案
function getUserResult(index: number, answer: string) {
  const currentTopic =
    curTopic.value.type === QuestionTypes.SINGLE_CHOICE ||
    curTopic.value.type === QuestionTypes.MULTIPLE_CHOICE
      ? curTopic.value
      : curTopic.value.tableField116[index]
  if (currentTopic.type === QuestionTypes.MULTIPLE_CHOICE) {
    // 切换答案选中状态
    if (!currentTopic.user_answer) {
      currentTopic.user_answer = [answer]
    } else if (currentTopic.user_answer.includes(answer)) {
      // 取消选中
      currentTopic.user_answer = currentTopic.user_answer.filter((item: string) => item !== answer)
    } else {
      currentTopic.user_answer.push(answer)
    }
  } else {
    // 直接赋值给 user_answer 数组
    currentTopic.user_answer = [answer]
  }
  // 更新答题卡状态
  // if (countUnansweredQuestions('cur') === 0) {
  //   curTopic.value.state = 'select'
  // }
}
// 答案解析
function arrayToString(arr: any[], separator = ', '): string {
  if (!Array.isArray(arr)) {
    return '暂无'
  }
  return arr.join(separator)
}
// 选题
function pickTest(e: number) {
  if (e === 1) {
    // 下一题
    if (curIndex.value >= props.topicList.length - 1) {
      showToast('已经是最后一题', 'error')
      return
    }
    curIndex.value++
  } else {
    // 上一题
    if (curIndex.value <= 0) {
      showToast('已经是第一题', 'error')
      return
    }
    curIndex.value--
  }
  curTopic.value = props.topicList[curIndex.value]
}
// 点击答题卡跳转到指定题目
function pickerByPopup(e: number) {
  // 其他可以点击答题卡跳转题目
  showPopup.value = false
  curIndex.value = e
  curTopic.value = props.topicList[curIndex.value]
}
</script>

<style lang="scss" scoped>
@import '@/common/css/index.scss';
@import '@/common/css/project.scss';
.tabbar-icon {
  width: 20px;
  height: 20px;
}
.u-tabbar__icon {
  width: 17px;
  height: 17px;
}
.answer-card {
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
  text-align: center;
  margin-top: 16rpx;
}
.half {
  max-height: 50vh;
  overflow: auto;
}
.topic-item {
  width: 70rpx;
  height: 70rpx;
  background-color: #f6f7f8;
  border-radius: 36rpx;
  opacity: 1;
  text-align: center;
  line-height: 70rpx;
  margin: 20rpx 40rpx;
  &.select {
    background: $u-theme-color;
    color: #fff;
  }
  &.wrong {
    background: #ff776a;
    color: #fff;
  }
}
$pd_l_r: 30rpx;
.analysis-wrap {
  padding: 24rpx $pd_l_r;
  background: #ffffff;
  .analysis-head {
    position: relative;
    line-height: 1.4;
    &::before {
      $w: 8rpx;
      content: '';
      position: absolute;
      width: $w;
      height: 100%;
      background-color: $u-theme-color;
      left: $w - $pd_l_r;
    }
    .analysis-correction {
      float: right;
      font-size: 30rpx;
    }
  }
  .analysis-main {
    padding: 0 0.5em;
    .analysis-answer {
      margin: 12rpx 0;
    }
    .hide-warp {
      .context {
        text-indent: 2em;
        min-height: 4em;
        ._root > view {
          font-size: 24rpx;
        }
      }
    }
  }
}
.topic-result {
  background: #fff;
  text-align: center;
  line-height: 70rpx;
  font-size: 28rpx;
  padding: 0 30rpx;
  .correct {
    color: #61c5a1;
  }
  .wrong {
    color: #ff776a;
  }
  text {
    margin-right: 1em;
    line-height: 2.5;
  }
  & > text {
    margin-right: 4em;
  }
}
</style>
