<template>
  <view class="progress_box">
    <canvas class="progress_bg" canvas-id="cpbg"></canvas>
    <canvas class="progress_bar" canvas-id="cpbar"></canvas>
    <view class="progress_txt">
      <view style="line-height: 0.8">答对</view>
      <view class="progress_info">{{ rightNum }}</view>
      <text>/{{ totalNum }}题</text>
    </view>
  </view>
</template>

<script>
export default {
  props: {
    rightNum: {
      type: Number,
    },
    totalNum: {
      type: Number,
    },
    radius: {
      type: Number,
      default: 110,
    },
  },
  data() {
    return {
      x: 150,
      y: 120,
      barWidth: 12,
    }
  },
  watch: {
    rightNum(newVal) {
      this.drawCircle(parseFloat(newVal / this.totalNum).toFixed(2))
    },
    totalNum(newVal) {
      this.drawCircle(parseFloat(this.rightNum / newVal).toFixed(2))
    },
  },
  mounted() {
    this.drawProgressbg()
    this.drawCircle(parseFloat(this.rightNum / this.totalNum).toFixed(2)) // 参数为1-100
  },
  methods: {
    drawProgressbg() {
      // 自定义组件实例 this ，表示在这个自定义组件下查找拥有 canvas-id 的 <canvas/>
      const ctx = uni.createCanvasContext('cpbg', this)
      ctx.setLineWidth(this.barWidth - 4) // 设置圆环的宽度
      ctx.setStrokeStyle('#E5EAFD') // 设置圆环的颜色
      ctx.setLineCap('round') // 设置圆环端点的形状
      ctx.beginPath() // 开始一个新的路径
      ctx.arc(this.x, this.y, this.radius, 1 * Math.PI, 0, false)
      // 设置一个原点(this.x,this.y)，半径为this.radius的圆的路径到当前路径
      ctx.stroke() // 对当前路径进行描边
      ctx.draw()
    },
    drawCircle(step) {
      const ctx = uni.createCanvasContext('cpbar', this)
      ctx.setLineWidth(this.barWidth)
      ctx.setStrokeStyle('#8BA8F6')
      ctx.setLineCap('round')
      ctx.beginPath()
      ctx.arc(this.x, this.y, this.radius, 1 * Math.PI, Math.PI + Math.PI * step, false)
      ctx.stroke()
      ctx.draw()
    },
  },
}
</script>

<style>
.progress_box {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
}
.progress_bg {
  position: absolute;
  width: 300px;
  height: 140px;
}
.progress_bar {
  width: 300px;
  height: 140px;
}
.progress_txt {
  display: flex;
  flex-direction: column;
  top: 90rpx;
  position: absolute;
  font-size: 28upx;
  color: #999;
}
.progress_info {
  letter-spacing: 2upx;
  font-size: 70rpx;
  color: #333;
}
.progress_dot {
  width: 16upx;
  height: 16upx;
  border-radius: 50%;
  background-color: #fb9126;
}
</style>
