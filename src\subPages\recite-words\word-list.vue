<template>
  <view>
    <view
      v-if="type === '熟知词' || type === '生词' || type === '连词造句'"
      class="flex justify-between px-40rpx py-26rpx text-28rpx c-[#999999]">
      <view>全部 {{ wordNum }} 词</view>
      <view class="flex items-center gap-30rpx">
        <view
          v-if="isBatchMode && (type === '生词' || type === '熟知词') && selectedWords.length > 0"
          class="c-[#F82F2F]"
          @click="batchDelete">
          批量删除
        </view>
        <view class="c-[#459AF7]" @click="toggleBatchMode">
          {{ isBatchMode ? '取消选择' : '批量选择' }}
        </view>
      </view>
    </view>
    <u-checkbox-group :value="selectedWords" @change="onCheckboxGroupChange">
      <LoadMoreList
        ref="loadMoreList"
        :request-fn="getRequestFunction"
        :request-params="getRequestParams"
        empty-text="暂无单词">
        <template #default="{ list }">
          <view :class="{ 'pb-120rpx': type === '连词造句' }">
            <view
              v-for="(item, index) in (list as IReciteWordInfo[])"
              :key="item._id"
              class="word-item">
              <!-- 有滑动功能的情况 -->
              <u-swipe-action
                v-if="
                  type === '熟知词' || type === '生词' || type === '本书待学' || type === '单词拼写'
                "
                auto-close>
                <u-swipe-action-item
                  :threshold="20"
                  :options="swipeOptions"
                  @click="
                    type === '熟知词'
                      ? handleSwipeAction($event, item as INewWordAndFamiliarWord, index)
                      : handleFamiliarAction($event, item as INewWordAndFamiliarWord, index)
                  ">
                  <WordListItem
                    :no="showCheckbox ? undefined : index + 1"
                    :word-info="item"
                    :color="getWordColor(index, item)"
                    :show-checkbox="showCheckbox"
                    :is-selected="selectedWords.includes(item._id)" />
                </u-swipe-action-item>
              </u-swipe-action>
              <!-- 无滑动功能的情况 -->
              <WordListItem
                v-else
                :no="showCheckbox ? undefined : index + 1"
                :word-info="item"
                :color="getWordColor(index, item)"
                :show-checkbox="showCheckbox"
                :is-selected="selectedWords.includes(item._id)" />
            </view>
          </view>
        </template>
      </LoadMoreList>
    </u-checkbox-group>
    <!-- 连词造句按钮 -->
    <view v-if="type === '连词造句'" class="w-full fixed bottom-30rpx p30rpx box-border">
      <u-button text="连词造句" shape="circle" type="primary" @click="handleSentenceFormation">
      </u-button>
    </view>

    <!-- 连词造句弹窗 -->
    <CommonModal
      :show="showSentenceModal"
      title="连词造句"
      button-text="重新生成"
      :button-loading="isGenerating"
      @button-click="regenerateSentence"
      @close="closeSentenceModal">
      <view class="sentence-modal">
        <!-- 选中的单词 -->
        <view class="selected-words">
          <text class="words-text">{{ selectedWordsText }}</text>
        </view>

        <!-- 生成的句子 -->
        <view class="generated-sentence">
          <rich-text :nodes="richTextNodes" class="sentence-text"></rich-text>
        </view>
      </view>
    </CommonModal>
  </view>
</template>

<script setup lang="ts">
import WordListItem from './components/WordListItem.vue'
import CommonModal from '@/components/CommonModal.vue'
import LoadMoreList from '@/components/LoadMoreList.vue'
import type { IReciteWordInfo } from '@/api/project/recite-words/type'
import {
  deleteReciteWordStatus,
  getLearnedReciteWordList,
  getNewWordCountAndFamiliarWordCount,
  getNewWordCountAndFamiliarWordList,
  getNewWordList,
  getReciteWordStatus,
  insertReciteWordStatus,
} from '@/api/project/recite-words'

const wordNum = ref(0)
const wordList = ref<IReciteWordInfo[]>([])
const bookId = ref('')
const type = ref('')
const total = ref(0)
const wrongWordIds = ref<string[]>([])

// 生词熟词记录状态
const newWordsSet = ref<Set<string>>(new Set()) // 生词ID集合
const familiarWordsSet = ref<Set<string>>(new Set()) // 熟词ID集合

// LoadMoreList相关
const loadMoreList = ref<InstanceType<typeof LoadMoreList>>()

// 熟知词缓存
const familiarWordsCache = ref<Set<string>>(new Set())
const familiarWordsCacheLoaded = ref(false)

// LoadMoreList需要的请求函数
const getRequestFunction = computed(() => {
  switch (type.value) {
    case '生词':
    case '连词造句':
      return (params: any) =>
        getNewWordCountAndFamiliarWordList(bookId.value, '生词', params.currentPage)
    case '熟知词':
      return (params: any) =>
        getNewWordCountAndFamiliarWordList(bookId.value, '熟词', params.currentPage)
    case '本书待学':
      return (params: any) => getNewWordList(bookId.value, params.currentPage)
    case '本书已学':
      return (params: any) => getLearnedReciteWordList(bookId.value, params.currentPage)
    default:
      return (params: any) => getNewWordList(bookId.value, params.currentPage)
  }
})

// LoadMoreList需要的请求参数
const getRequestParams = computed(() => {
  // LoadMoreList会自动添加pageSize和currentPage参数
  // 这里只需要返回额外的参数
  return {}
})

// 批量选择相关
const selectedWords = ref<string[]>([]) // 选中的单词ID数组
const isBatchMode = ref(false) // 是否处于批量选择模式
const showCheckbox = computed(
  () =>
    (type.value === '连词造句' || type.value === '生词' || type.value === '熟知词') &&
    isBatchMode.value
)

// 滑动选项计算属性
const swipeOptions = computed(() => {
  if (type.value === '熟知词') {
    return deleteOptions.value
  } else if (type.value === '生词') {
    // 生词只有删除选项
    return [
      {
        text: '删除',
        icon: 'trash',
        style: {
          backgroundColor: '#F82F2F',
        },
      },
    ]
  } else if (type.value === '本书待学' || type.value === '单词拼写') {
    return familiarOptions.value
  }
  return []
})

// 加载生词熟词记录
async function loadWordStatus(bookId: string) {
  try {
    const result = await getReciteWordStatus(bookId)
    const newWords = new Set<string>()
    const familiarWords = new Set<string>()

    result.data.list.forEach(item => {
      if (item.type === '生词') {
        newWords.add(item.wordId)
      } else if (item.type === '熟词') {
        familiarWords.add(item.wordId)
      }
    })

    newWordsSet.value = newWords
    familiarWordsSet.value = familiarWords
  } catch (error) {
    console.error('加载生词熟词记录失败:', error)
  }
}

// 根据页面类型和索引获取颜色
function getWordColor(index: number, item?: IReciteWordInfo) {
  // 如果是本书已学或本书待学，根据单词状态获取颜色
  if ((type.value === '本书已学' || type.value === '本书待学') && item) {
    if (newWordsSet.value.has(item._id)) {
      return 'rgba(252, 177, 56, .4)' // 收藏/生词颜色
    } else if (familiarWordsSet.value.has(item._id)) {
      return 'rgba(69, 154, 247, .3)' // 熟知/熟词颜色
    }
    return undefined // 默认颜色（未定义状态）
  }

  // 其他情况使用交替颜色
  // 偶数索引返回默认颜色（undefined），奇数索引返回红色
  return index % 2 === 1 ? 'rgba(255, 0, 0, 0.10)' : undefined
}

// 选中单词的文本显示
const selectedWordsText = computed(() => {
  const selectedWordList = wordList.value.filter(item => selectedWords.value.includes(item._id))
  return selectedWordList.map(item => item.word).join(', ')
})

// 富文本内容
const richTextNodes = ref('')

// 生成富文本内容的函数
function generateRichText() {
  const sentence = generatedSentence.value
  const selectedIds = selectedWords.value
  const words = wordList.value
  if (!sentence) {
    richTextNodes.value = ''
    return
  }
  // 获取选中的单词
  const selectedWordList = words.filter(item => selectedIds.includes(item._id))
  const selectedWordsArray = selectedWordList.map(item => item.word.toLowerCase())
  if (selectedWordsArray.length === 0) {
    richTextNodes.value = sentence
    return
  }
  let result = sentence
  // 对每个选中的单词进行正则替换
  selectedWordsArray.forEach(word => {
    // 转义特殊字符，防止正则表达式注入
    const escapedWord = word.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')
    // 创建正则表达式，匹配单词（忽略大小写，考虑单词边界）
    const regex = new RegExp(`\\b(${escapedWord})\\b`, 'gi')
    result = result.replace(regex, '<span style="color: #459af7;">$1</span>')
  })
  richTextNodes.value = result
}

// 删除选项配置
const deleteOptions = ref([
  {
    text: '删除',
    icon: 'trash',
    style: {
      backgroundColor: '#F82F2F',
    },
  },
])

// 熟知选项配置
const familiarOptions = ref([
  {
    text: '熟知',
    icon: 'checkmark-circle',
    style: {
      backgroundColor: '#459AF7',
      fontSize: '28rpx',
    },
  },
])

// 获取单词数量
async function getWordCount() {
  if (type.value === '熟知词' || type.value === '生词' || type.value === '连词造句') {
    try {
      const res = await getNewWordCountAndFamiliarWordCount(bookId.value)
      const data = res.data || []

      // 根据类型获取对应的数量
      let targetType = type.value
      if (type.value === '连词造句') {
        targetType = '生词' // 连词造句就是生词
      } else if (type.value === '熟知词') {
        targetType = '熟词' // API中熟知词对应的是熟词
      }

      const found = data.find((item: any) => item._id === targetType)
      wordNum.value = found ? found.totalCount : 0
    } catch (error) {
      console.error('获取单词数量失败:', error)
      wordNum.value = 0
    }
  }
}

// 切换批量选择模式
function toggleBatchMode() {
  isBatchMode.value = !isBatchMode.value

  // 退出批量模式时清空选中状态
  if (!isBatchMode.value) {
    selectedWords.value = []
  }

  console.log('批量选择模式:', isBatchMode.value)
}

// 批量删除
function batchDelete() {
  if (selectedWords.value.length === 0) {
    uni.showToast({
      title: '请先选择要删除的单词',
      icon: 'none',
    })
    return
  }

  const actionText = type.value === '熟知词' ? '从熟知词中移除' : '删除'
  uni.showModal({
    title: '确认批量删除',
    content: `确定要${actionText}选中的 ${selectedWords.value.length} 个单词吗？`,
    success: res => {
      if (res.confirm) {
        performBatchDelete()
      }
    },
  })
}

// 执行批量删除
async function performBatchDelete() {
  const selectedIds = [...selectedWords.value]
  let successCount = 0
  let failCount = 0

  uni.showLoading({
    title: '删除中...',
  })

  try {
    // 逐个删除选中的单词
    for (const wordId of selectedIds) {
      try {
        await deleteReciteWordStatus(wordId)

        // 从缓存中移除（如果是熟知词）
        if (type.value === '熟知词') {
          familiarWordsCache.value.delete(wordId)
        }

        successCount++
      } catch (error) {
        console.error('删除单词失败:', wordId, error)
        failCount++
      }
    }

    // 从列表中移除已删除的单词
    wordList.value = wordList.value.filter(item => !selectedIds.includes(item._id))

    // 更新总数
    total.value -= successCount
    wordNum.value = Math.max(0, wordNum.value - successCount)

    // 清空选择
    selectedWords.value = []

    uni.hideLoading()

    if (failCount === 0) {
      uni.showToast({
        title: `成功删除 ${successCount} 个单词`,
        icon: 'success',
      })
    } else {
      uni.showToast({
        title: `成功删除 ${successCount} 个，失败 ${failCount} 个`,
        icon: 'none',
      })
    }
  } catch (error) {
    uni.hideLoading()
    console.error('批量删除失败:', error)
    uni.showToast({
      title: '批量删除失败',
      icon: 'error',
    })
  }
}

// 连词造句弹窗相关
const showSentenceModal = ref(false)
const isGenerating = ref(false)
const generatedSentence = ref('')

// 监听变化，更新富文本内容
watch([generatedSentence], generateRichText)

// 处理连词造句按钮点击
function handleSentenceFormation() {
  // 检查是否有选中的单词
  if (selectedWords.value.length === 0) {
    uni.showToast({
      title: '请先选择单词',
      icon: 'none',
    })
    return
  }

  // 显示连词造句弹窗
  showSentenceModal.value = true
  generateSentence()
}

// 生成句子
async function generateSentence() {
  isGenerating.value = true

  try {
    // 获取选中的单词
    const selectedWordList = wordList.value.filter(item => selectedWords.value.includes(item._id))
    const words = selectedWordList.map(item => item.word)

    console.log('生成句子，使用单词:', words)

    // 这里调用AI接口生成句子
    // const response = await generateSentenceAPI(words)
    // generatedSentence.value = response.data.sentence

    // 模拟生成句子
    await new Promise(resolve => setTimeout(resolve, 1000))
    console.log(words)

    generatedSentence.value = `${words.join(' ')}.`
  } catch (error) {
    console.error('生成句子失败:', error)
    uni.showToast({
      title: '生成失败，请重试',
      icon: 'error',
    })
  } finally {
    isGenerating.value = false
  }
}

// 重新生成句子
function regenerateSentence() {
  generateSentence()
}

// 关闭连词造句弹窗
function closeSentenceModal() {
  showSentenceModal.value = false
  generatedSentence.value = ''
}

// 处理checkbox-group变化
function onCheckboxGroupChange(selectedList: string[]) {
  selectedWords.value = selectedList
  console.log('选中的单词:', selectedWords.value)
}
// wordLearned事件已移除，单词点击只播放发音

// 处理滑动操作 - 删除熟知词
function handleSwipeAction(event: any, item: INewWordAndFamiliarWord, index: number) {
  if (event.index === 0) {
    // 点击删除熟知词
    uni.showModal({
      title: '确认删除',
      content: `确定要将单词"${item.word}"从熟知词中移除吗？`,
      success: res => {
        if (res.confirm) {
          deleteFamiliarWord(item, index)
        }
      },
    })
  }
}

// 处理熟知操作
function handleFamiliarAction(event: any, item: INewWordAndFamiliarWord, index: number) {
  if (type.value === '生词') {
    // 生词只有删除操作
    if (event.index === 0) {
      // 点击删除按钮
      uni.showModal({
        title: '确认删除',
        content: `确定要删除单词"${item.word}"吗？`,
        success: res => {
          if (res.confirm) {
            deleteNewWord(item, index)
          }
        },
      })
    }
  } else {
    // 其他类型只有熟知操作
    if (event.index === 0) {
      // 点击熟知按钮
      uni.showModal({
        title: '确认操作',
        content: `确定要将单词"${item.word}"标记为熟知吗？`,
        success: res => {
          if (res.confirm) {
            markAsFamiliar(item)
          }
        },
      })
    }
  }
}

// 加载熟知词缓存
async function loadFamiliarWordsCache() {
  if (familiarWordsCacheLoaded.value) {
    return // 已经加载过了
  }

  try {
    const res = await getNewWordCountAndFamiliarWordList(bookId.value, '熟词', 1, -1)
    const familiarWords = res.data
    // 将熟知词ID存入Set缓存
    familiarWordsCache.value.clear()
    familiarWords.forEach(word => {
      familiarWordsCache.value.add(word._id)
    })

    familiarWordsCacheLoaded.value = true
  } catch (error) {
    console.error('加载熟知词缓存失败:', error)
  }
}

// 检查单词是否已经在熟知词列表中
async function checkIfWordIsFamiliar(wordId: string): Promise<boolean> {
  try {
    // 确保缓存已加载
    await loadFamiliarWordsCache()

    // 从缓存中检查
    return familiarWordsCache.value.has(wordId)
  } catch (error) {
    console.error('检查熟知词状态失败:', error)
    return false
  }
}

// 标记为熟知
async function markAsFamiliar(item: IReciteWordInfo) {
  try {
    // 检查单词是否已经标记为熟词
    const isAlreadyFamiliar = await checkIfWordIsFamiliar(item._id)

    if (isAlreadyFamiliar) {
      uni.showToast({
        title: '该单词已在熟知词中',
        icon: 'none',
      })
      return
    }

    // 调用熟词生词入库API，标记为熟词
    await insertReciteWordStatus({
      book: bookId.value,
      wordId: item._id,
      type: '熟词',
    })

    console.log('标记为熟知:', item.word, item._id)

    // 更新缓存：将单词ID添加到熟知词缓存中
    familiarWordsCache.value.add(item._id)

    // 不移除列表中的单词，仅增加熟词记录
    uni.showToast({
      title: '已标记为熟知',
      icon: 'success',
    })
  } catch (error) {
    console.error('标记熟知失败:', error)
    uni.showToast({
      title: '操作失败',
      icon: 'error',
    })
  }
}

// 删除生词
async function deleteNewWord(item: IReciteWordInfo, index: number) {
  try {
    // 调用删除生词的API
    await deleteReciteWordStatus(item._id)

    console.log('删除生词:', item.word, item._id)

    // 从列表中移除
    wordList.value.splice(index, 1)
    total.value--

    // 更新单词数量
    if (wordNum.value > 0) {
      wordNum.value--
    }

    uni.showToast({
      title: '已删除生词',
      icon: 'success',
    })
  } catch (error) {
    console.error('删除生词失败:', error)
    uni.showToast({
      title: '删除失败',
      icon: 'error',
    })
  }
}
import type { INewWordAndFamiliarWord } from '@/api/project/recite-words/type'
// 删除熟知词
async function deleteFamiliarWord(item: INewWordAndFamiliarWord, index: number) {
  try {
    // 检查是否有typeId（熟知词记录ID）
    if (!item._id) {
      uni.showToast({
        title: '无法删除，记录ID不存在',
        icon: 'none',
      })
      return
    }

    // 调用删除熟知词的API
    await deleteReciteWordStatus(item.recordId)

    console.log('删除熟知词:', item.word, item.recordId)

    // 更新缓存：从熟知词缓存中移除单词ID
    familiarWordsCache.value.delete(item.recordId)

    // 从列表中移除
    wordList.value.splice(index, 1)
    total.value--

    // 更新单词数量
    if (wordNum.value > 0) {
      wordNum.value--
    }

    uni.showToast({
      title: '已从熟知词中移除',
      icon: 'success',
    })
  } catch (error) {
    console.error('删除熟知词失败:', error)
    uni.showToast({
      title: '删除失败',
      icon: 'error',
    })
  }
}

onLoad((e: any) => {
  bookId.value = e.bookId
  uni.setNavigationBarTitle({
    title: e.title,
  })
  type.value = e.title

  const instance = getCurrentInstance()!.proxy as any
  const eventChannel = instance.getOpenerEventChannel()

  new Promise<void>(resolve => {
    if (e.title === '单词拼写' || e.title === '选择题测试') {
      eventChannel.once('wrongWordsList', (data: string[]) => {
        wrongWordIds.value = data
        resolve()
      })
    } else {
      resolve()
    }
  }).then(async () => {
    getWordCount() // 获取单词数量

    // 加载生词熟词记录
    await loadWordStatus(bookId.value)

    // 只在本书待学页面预加载熟知词缓存（用于检查单词是否已熟知）
    if (type.value === '本书待学') {
      loadFamiliarWordsCache()
    }
  })
})

// 标记是否已经初始化过
const isInitialized = ref(false)

// 监听type变化，重新初始化LoadMoreList
watch(
  () => type.value,
  newType => {
    if (newType) {
      if (isInitialized.value) {
        // 如果已经初始化过，则刷新数据
        nextTick(() => {
          loadMoreList.value?.refresh()
        })
      } else {
        // 第一次初始化，标记为已初始化
        isInitialized.value = true
      }
    }
  }
)

// 使用LoadMoreList的触底加载
onReachBottom(() => {
  loadMoreList.value?.onReachBottom()
})
</script>

<style lang="scss" scoped>
.main-box {
  height: 100vh;
  background: #f4f5f7;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

// 连词造句页面底部间距
.pb-120rpx {
  padding-bottom: 120rpx;
}

// 固定按钮样式
.w-full {
  width: 100%;
}

.fixed {
  position: fixed;
}

.bottom-30rpx {
  bottom: 30rpx;
}

.p30rpx {
  padding: 30rpx;
}

.box-border {
  box-sizing: border-box;
}

// u-checkbox-group 样式穿透
/* #ifdef H5 */
:deep(.u-checkbox-group) {
  display: block !important;
}
/* #endif */

// 连词造句弹窗样式
.sentence-modal {
  text-align: center;
  .selected-words {
    margin-bottom: 20rpx;

    .words-text {
      font-size: 28rpx;
      color: #333;
    }
  }

  .generated-sentence {
    margin-bottom: 5rpx;
    border: 1rpx dashed #dfdfdf;
    border-radius: 16rpx;
    padding: 20rpx 38rpx 28rpx 32rpx;
    .sentence-text {
      font-size: 24rpx;
      color: #333333;
    }
  }
}
</style>

<style lang="scss">
/* #ifndef H5 */
.u-checkbox-group {
  display: block !important;
}
/* #endif */
</style>
