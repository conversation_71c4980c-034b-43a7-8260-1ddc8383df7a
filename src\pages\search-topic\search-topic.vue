<template>
  <view
    v-if="!showCropper"
    class="bg-#459AF7 w100vw h100vh flex flex-col justify-center items-center">
    <view
      class="w-364rpx h-364rpx bg-white rounded-45% flex flex-col justify-center items-center"
      @click="photograph">
      <image
        class="w-198rpx h-159rpx;"
        :src="getSystemImg('6747e9ed0a34815816f11159/670c8c434dfd0e1c49e0a94d')"
        mode="aspectFit" />
      <view class="text-#35A2FD text-size-24rpx">学习助手</view>
    </view>
    <view
      class="w-420rpx h-80rpx bg-#519ef9 rounded-49rpx text-white text-size-36rpx text-center flex justify-center items-center mt-30rpx"
      :class="{ 'bg-white text-#35A2FD': mode === 'photoRecognition' }"
      @click="photoRecognition"
      >AI拍照识题解题</view
    >
    <view
      class="w-420rpx h-80rpx bg-#519ef9 rounded-49rpx text-white text-size-36rpx text-center flex justify-center items-center mt-30rpx"
      :class="{ 'bg-white text-#35A2FD': mode === 'chinesePromote' }"
      @click="chinesePromote"
      >AI语文作文提分</view
    >
    <view
      class="w-420rpx h-80rpx bg-#519ef9 rounded-49rpx text-white text-size-36rpx text-center flex justify-center items-center mt-30rpx"
      :class="{ 'bg-white text-#35A2FD': mode === 'englishPromote' }"
      @click="englishPromote"
      >AI英语作文提分</view
    >
  </view>
  <view v-else class="container">
    <cropper :src="imgSrc" class="cropper-overlay" @choose-image="photograph" @crop="crop" />
  </view>
  <u-popup
    :show="showPopup"
    :round="10"
    mode="bottom"
    class="pop-class"
    :safe-area-inset-bottom="false"
    @close="showPopup = false">
    <scroll-view
      scroll-y
      scroll-with-animation
      :scroll-top="scrollTop"
      style="height: 1000rpx"
      class="m-1">
      <u-skeleton v-if="showSkeleton" rows="30" title loading></u-skeleton>
      <mpHtml :content="resultText" :markdown="true"></mpHtml>
      <u-button v-if="againBtn" type="primary" text="再试一次" @click="photograph"></u-button>
    </scroll-view>
  </u-popup>
  <PointsModel
    :show="pointsModelShow"
    type="reduce"
    :flag="pointsModelobj.flag"
    :points="pointsModelobj.points"
    :content="pointsModelobj.content"
    @close="pointsModelShow = false" />
</template>

<script setup lang="ts">
import mpHtml from '@/components/mp-html/mp-html.vue'
import cropper from '@/components/bt-cropper/cropper.vue'
import useMemberStore from '@/store/modules/member'
import PointsModel from '@/components/PointsModel.vue'
import { getSystemImg } from '@/utils'
const mode = ref('')
const imgSrc = ref('')
const showCropper = ref(false)
const resultText = ref('')
const showPopup = ref(false)
const againBtn = ref(false)
const showSkeleton = ref(false)
const scrollTop = ref(0)
const chatId = ref('')
const token = ref('')
const BaseUrl = 'https://kindoucloud.com/ai'
const postData = {
  messages: [
    {
      role: 'user',
      content: [
        {
          type: 'image_url',
          image_url: {
            url: '',
          },
        },
      ],
    },
  ],
  chatId: 'cIGIcCYfjRKL',
  detail: false,
  stream: true,
}

onMounted(() => {
  mode.value = 'photoRecognition'
  chatId.value = 'cIGIcCYfjRKL'
  token.value = 'Bearer fastgpt-qILwmcDB7fjGXtsqikxXgYigmXIkoc08wh1ztGZ738uEcsIZL1f2KYVY8'
})

// 积分弹窗
const pointsModelShow = ref(false)
const pointsModelobj = ref({
  flag: false,
  points: 0,
  content: '',
})

async function loadData(url: string) {
  // 扣积分
  const res = await useMemberStore().pointsAction('AI识题')
  if (res?.flag) {
    pointsModelobj.value.flag = res.flag
    pointsModelShow.value = true
    return
  } else {
    pointsModelobj.value = res as any
    pointsModelShow.value = true
  }
  showPopup.value = true
  showSkeleton.value = true
  // 截取url中/api前面的部分
  const getUrl = (url: string) => {
    const index = url.indexOf('/api')
    return index !== -1 ? url.substring(index) : url
  }
  postData.messages[0].content[0].image_url.url = `http://kindoucloud.com:8889${getUrl(url)}`
  postData.chatId = chatId.value
  // 这里发送请求
  const requestTask = uni.request({
    url: `${BaseUrl}/api/v1/chat/completions`,
    timeout: 60000,
    responseType: 'text',
    method: 'POST',
    enableChunked: true, // 配置这里
    data: postData,
    header: {
      Accept: 'text/event-stream',
      Authorization: token.value,
      // Origin: BaseUrl,
    },
    success: response => {},
  }) as any
  requestTask.onChunkReceived((response: any) => {
    showSkeleton.value = false
    // 将数据转换为文本格式
    // let decoder = new TextDecoder('utf-8')
    // let text = decoder.decode(new Uint8Array(response.data))
    const uint8Array = new Uint8Array(response.data)
    let text = String.fromCharCode.apply(null, uint8Array as any)
    text = decodeURIComponent(escape(text))
    // 处理接收到的文本
    const lines = text.split('\n')
    for (const line of lines) {
      // 只处理以 "data: " 开头的行
      if (line.startsWith('data: ')) {
        const jsonStr = line.substring(6).trim()
        if (jsonStr === '[DONE]') {
          // 数据接收完成
          againBtn.value = true
          return // 结束接收
        }
        // 解析 JSON 数据并提取内容
        try {
          const jsonData = JSON.parse(jsonStr)
          const content = jsonData.choices[0].delta.content || ''
          resultText.value += content // 拼接内容
          // 设置 scrollTop 让内容滚动到底部
          //  nextTick(() => {
          //   scrollTop.value = 9999; // 可以根据需要设置合适的值
          // });
        } catch (error) {
          console.error('解析JSON失败:', error)
        }
      }
    }
  })
}

// 拍照识题
function photoRecognition() {
  mode.value = 'photoRecognition'
  chatId.value = 'cIGIcCYfjRKL'
  token.value = 'Bearer fastgpt-qILwmcDB7fjGXtsqikxXgYigmXIkoc08wh1ztGZ738uEcsIZL1f2KYVY8'
  photograph()
}
// 语文提分
function chinesePromote() {
  mode.value = 'chinesePromote'
  chatId.value = 'eNlx63AxhaEZ'
  token.value = 'Bearer fastgpt-ngQNKLMUS8b0x3fU4l3Vz9e4nOsC7RKaRNQcdwwvuBPWvrEwJZVX'
  photograph()
}
// 英语提分
function englishPromote() {
  mode.value = 'englishPromote'
  chatId.value = 'p1sONPi68oy1'
  token.value = 'Bearer fastgpt-snJ5Xhdr7OzrFImGypM0mWhYriqQFiIlvFxi6PjmfmZm9kCMh3SJloUWDxM7Zf'
  photograph()
}
function photograph() {
  showPopup.value = false
  againBtn.value = false
  resultText.value = ''
  postData.messages[0].content[0].image_url.url = ''
  uni.chooseImage({
    count: 1,
    success: success => {
      const tempFilePaths = success.tempFilePaths
      imgSrc.value = tempFilePaths[0]
      // crop(tempFilePaths[0])
      uni.hideTabBar()
      showCropper.value = true
    },
  })
}
function crop(filePath: string) {
  uni.showTabBar()
  showCropper.value = false
  uni.uploadFile({
    url: `${BaseUrl}/api/common/file/upload`,
    filePath,
    name: 'file',
    header: {
      Authorization: token.value,
    },
    formData: {
      metadata: { chatId: chatId.value },
      bucketName: 'chat',
    },
    success: res => {
      const data = JSON.parse(res.data)
      loadData(data.data.previewUrl)
    },
  })
}

onLoad(async ({ q }: any) => {
  if (q) {
    const link = decodeURIComponent(q)
    const formUser = /.+[\?&]formUser=(?<id>\w+)&?/.exec(link)?.groups?.id
    if (formUser) uni.setStorageSync('InviteCode', formUser)
  }
})
</script>

<style>
.container {
  position: relative;
  height: 100%;
  width: 100%;
}
.cropper-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 999; /* 确保裁剪组件在最上面 */
}
.pop-class {
  border-top: 2rpx solid #03a9f4;
  padding-top: 10rpx;
}
</style>
