<template>
  <view
    :style="{ borderLeftColor: color }"
    class="word-entry"
    :data-number="no"
    @click="onWordClick">
    <view class="flex-col flex word-info justify-between">
      <text class="word">{{ wordInfo.word }}</text>
      <text class="articulation">英[{{ wordInfo.pronunciation }}]</text>
    </view>
    <view class="flex-col flex meaning justify-between">
      <text v-for="item in wordInfo.meaning || []" :key="item" class="meaning-item">
        {{ item }}
      </text>
    </view>
    <view class="hidden-container">
      <view
        :style="{ background: hiddenEleLeft ? '#e5ecf7' : 'transparent' }"
        class="flex-center hidden-element left"
        @click="hiddenEleClick('left')">
        <text v-show="hiddenEleLeft">点击显示</text>
      </view>
      <view
        :style="{ background: hiddenEleRight ? '#e5ecf7' : 'transparent' }"
        class="flex-center hidden-element right"
        @click="hiddenEleClick('right')">
        <text v-show="hiddenEleRight">点击显示</text>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts" name="word-entry">
import useCommonStore from '@/store/modules/common'

const props = defineProps({
  wordInfo: {
    type: Object,
    default: () => ({}),
  },
  showEnterLeft: {
    type: Boolean,
    default: true,
  },
  showEnterRight: {
    type: Boolean,
    default: true,
  },
  no: {
    type: Number,
    default: 1,
  },
  color: {
    type: String,
  },
})

const emit = defineEmits(['wordLearned', 'hiddenEleClick'])

const { playWord } = useCommonStore()
// 隐藏左边
const hiddenEleLeft = ref(!props.showEnterLeft)
// 隐藏右边
const hiddenEleRight = ref(!props.showEnterRight)

watch(
  () => props.showEnterLeft,
  val => {
    hiddenEleLeft.value = !val
  }
)

watch(
  () => props.showEnterRight,
  val => {
    hiddenEleRight.value = !val
  }
)

function onWordClick() {
  playWord(props.wordInfo.word)
  // 触发学习事件，让父组件处理learned状态
  emit('wordLearned', props.no)
}
function resetHiddenEle() {
  hiddenEleLeft.value = !props.showEnterLeft
  hiddenEleRight.value = !props.showEnterRight
}
function hiddenEleClick(type: string) {
  if (type === 'left') {
    hiddenEleLeft.value = !hiddenEleLeft.value
  } else {
    hiddenEleRight.value = !hiddenEleRight.value
  }
  emit('hiddenEleClick', type)
}
defineExpose({
  resetHiddenEle,
})
</script>

<style lang="scss" scoped>
.word-entry {
  $containerHeight: 120rpx;
  $borderWidth: 55rpx;
  background-color: #f9f9f9;
  display: flex;
  align-items: center;
  border-radius: 20rpx;
  min-height: $containerHeight;
  position: relative;
  border-left: $borderWidth solid #e7f0fa;
  &::before {
    content: attr(data-number);
    color: #999999;
    position: absolute;
    left: -$borderWidth;
    text-align: center;
    width: $borderWidth;
    font-size: 24rpx;
  }

  .word-info {
    flex: 2;
    padding: 16rpx 28rpx;
    position: relative;
    text-align: center;
    .word {
      font-weight: bold;
      font-size: 30rpx;
      margin-bottom: 13rpx;
      word-wrap: break-word;
      word-break: break-all;
      white-space: normal;
      line-height: 1.4;
    }
    .articulation {
      color: #c9c9c9;
      font-size: 24rpx;
    }
  }
  .meaning {
    padding: 16rpx 28rpx;
    flex: 3;
    font-size: 28rpx;
    position: relative;
    .meaning-item {
      margin-bottom: 15rpx;
      &:last-child {
        margin-bottom: 0;
      }
    }
  }
  .hidden-container {
    position: absolute;
    width: 100%;
    height: 100%;
    display: flex;
    font-size: 28rpx;
    .hidden-element {
      background: #e5ecf7;
      z-index: 10;
      color: #333333;
    }
    .left {
      margin-left: 5rpx;
      flex: 2;
    }
    .right {
      flex: 3;
      border-radius: 0 20rpx 20rpx 0;
    }
  }
}
</style>
