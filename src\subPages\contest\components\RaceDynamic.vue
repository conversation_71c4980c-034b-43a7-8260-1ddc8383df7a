<template>
  <view>
    <view
      v-for="item in 10"
      :key="item"
      class="flex justify-between py-31rpx border-1rpx border-b border-solid border-#E2E2E2 border-t-none border-l-none border-r-none">
      <view class="flex">
        <view>
          <image
            class="w-162rpx h-120rpx"
            :src="getSystemImg('6694ec5cc523aa5a70e0a30f/67357112905da85a42991c15')"
            radius="4"></image>
        </view>
        <view class="ml-26rpx flex flex-col justify-between">
          <view class="u-line-2"> 赛道动态主题--2023“猛猖杯”国际生命 科学数据创新大赛专题赛</view>
          <view class="text-#777C8B text-24rpx flex justify-between">
            <view class="flex">
              <up-icon name="eye"></up-icon> <span class="ml10rpx">230</span></view
            >
            <view> 2023-07-03 16:20 </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts" name="">
import { getSystemImg } from '@/utils'
</script>

<style lang="scss" scoped></style>
